# Bank Statement File Storage

## Directory Structure

When you upload PDF bank statements, the system saves both the original PDF and the extracted CSV for debugging and improvement purposes.

```
statements/
├── pdf/           # Original uploaded PDF files
└── csv/           # Extracted transaction CSV files
```

## File Naming Convention

Both PDF and CSV files use the same base filename for easy correlation:

```
{original_filename}_{timestamp}.{extension}
```

### Example:
- **Original upload**: `hdfc_statement_jan2025.pdf`
- **Saved PDF**: `statements/pdf/hdfc_statement_jan2025_1751943764262.pdf`
- **Saved CSV**: `statements/csv/hdfc_statement_jan2025_1751943764262.csv`

## Security

⚠️ **Important**: These directories contain sensitive financial data and are:
- **Excluded from Git** (added to .gitignore)
- **Stored locally only** - never committed to version control
- **Your responsibility** to secure and backup if needed

## Debugging Workflow

1. **Upload fails?** → Check the saved PDF to verify file integrity
2. **Extraction issues?** → Compare CSV output with original PDF
3. **Improve parser?** → Use CSV as reference for algorithm improvements
4. **Data validation?** → Cross-reference CSV with bank statement

## Cleanup

The system automatically creates these directories but **does not auto-delete** old files. You may want to periodically clean up old statements to save disk space:

```bash
# Remove files older than 30 days
find statements/ -type f -mtime +30 -delete
```

## Directory Creation

Directories are automatically created when first PDF is uploaded:
- `statements/pdf/` - for PDF storage
- `statements/csv/` - for CSV storage