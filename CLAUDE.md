# Portfolio Tracker - Custom Instructions

## Tech Stack
- **Mobile**: React Native + Expo (SDK 50)
- **Web**: Next.js 14 with App Router
- **Backend**: Supabase (PostgreSQL + Auth + Realtime)
- **State Management**: Zustand
- **UI**: NativeWind (mobile) + Tailwind CSS (web)
- **Charts**: Victory Native (mobile) + Recharts (web)
- **Forms**: React Hook Form + Zod validation

## PROJECT CONTEXT
Cross-platform personal finance app. Focus on web development first, then mobile UI.

**Priorities:**
1. Code reusability (shared logic in packages/shared)
2. TypeScript strict mode throughout
3. Security-first approach for financial data
4. Web UI completion before mobile development

## CODING STANDARDS
- TypeScript strict mode with proper error boundaries
- Environment variables for sensitive data
- Atomic design principles for components
- Loading and error states for async operations

## UI/UX STANDARDS
- **Cursor Pointer**: ALL buttons, tabs, and interactive elements MUST include `cursor-pointer` in their className
- **Dark Mode**: All components must support dark mode with proper contrast
- **Hover States**: Subtle hover effects without jarring background changes

## NAMING CONVENTIONS
- Components: PascalCase (ExpenseCard.tsx)
- Utilities: camelCase (formatCurrency.ts)
- Types: PascalCase with 'I'/'T' prefix (IExpense, TCategory)
- Constants: UPPER_SNAKE_CASE (MAX_BUDGET_AMOUNT)
- Database: snake_case (expense_categories)
- API: kebab-case (/api/get-expenses)

## SECURITY REQUIREMENTS
- No plain text sensitive data storage
- Rate limiting on API endpoints
- Prepared statements for database queries
- Input validation client and server side
- HTTPS for all network requests

## DATABASE MIGRATION REQUIREMENTS
⚠️ **CRITICAL**: Never apply manual SQL changes!

- Use migration system for schema changes
- Create versioned files in `/migrations/`
- Test locally before committing
- Run `npm run migrate` before deployment

## TOOL USAGE EFFICIENCY
**CRITICAL**: Check CLAUDE.local.md folder structure BEFORE using search tools. Use documented file paths directly to prevent tool call exhaustion.

## ESSENTIAL COMMANDS
- Dev: `npm run dev` (running in background)
- Build: `npm run build`
- Migrate: `npm run migrate`
- Type check: `npm run type-check`

## RECOMMENDED PRACTICES
- Don't run `npm run dev` manually. I am already running it in the background.