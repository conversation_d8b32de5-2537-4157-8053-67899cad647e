import React, { useState, useEffect } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  comprehensiveTransactionFormInputSchema,
  comprehensiveTransactionFormSchema,
  type ComprehensiveTransactionFormInputData,
  type ICategory,
  type IAccount,
  AccountService,
  CategoryService,
  useCurrencyStore
} from '@repo/shared'

interface TabbedTransactionFormProps {
  onSubmit: (data: any) => Promise<void>
  loading?: boolean
  className?: string
  initialData?: any
  compact?: boolean
}

type TabType = 'expense' | 'income' | 'transfer'

export const TabbedTransactionForm: React.FC<TabbedTransactionFormProps> = ({
  onSubmit,
  loading = false,
  className = "",
  initialData,
  compact = false
}) => {
  const [activeTab, setActiveTab] = useState<TabType>(initialData?.transaction_type || 'expense')
  const [categories, setCategories] = useState<ICategory[]>([])
  const [accounts, setAccounts] = useState<IAccount[]>([])
  const [loadingData, setLoadingData] = useState(true)
  const { formatCurrency } = useCurrencyStore()

  const form = useForm<ComprehensiveTransactionFormInputData>({
    resolver: zodResolver(comprehensiveTransactionFormInputSchema),
    defaultValues: {
      amount: initialData?.amount?.toString() || '',
      category_id: initialData?.category_id || '',
      account_id: initialData?.account_id || '',
      to_account_id: initialData?.to_account_id || '',
      description: initialData?.description || '',
      transaction_date: initialData?.transaction_date || new Date(),
      transaction_type: initialData?.transaction_type || 'expense',
      fees: initialData?.fees?.toString() || '',
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = form

  const transactionType = watch('transaction_type')

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    setValue('transaction_type', activeTab)
  }, [activeTab, setValue])

  const loadData = async () => {
    try {
      const [categoriesData, accountsData] = await Promise.all([
        CategoryService.getCategories(),
        AccountService.getAccounts()
      ])
      setCategories(categoriesData)
      setAccounts(accountsData)

      // Auto-select first account if editing and no account is selected
      if (initialData && !initialData.account_id && accountsData.length > 0) {
        setValue('account_id', accountsData[0].id)
      }
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setLoadingData(false)
    }
  }

  const filteredCategories = categories.filter(cat => {
    if (activeTab === 'expense') return cat.type === 'expense'
    if (activeTab === 'income') return cat.type === 'income'
    if (activeTab === 'transfer') {
      // For transfers, exclude income and expense categories
      return !cat.type || (cat.type !== 'income' && cat.type !== 'expense')
    }
    return true
  })

  const filteredAccounts = accounts.filter(acc => acc.is_active)

  const handleFormSubmit = async (data: ComprehensiveTransactionFormInputData) => {
    try {
      const validatedData = comprehensiveTransactionFormSchema.parse(data)
      await onSubmit(validatedData)
      if (!initialData) {
        reset()
      }
    } catch (error) {
      console.error('Error submitting transaction:', error)
    }
  }

  if (loadingData) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-4 border-border border-t-primary"></div>
      </div>
    )
  }

  const tabs = [
    { id: 'expense' as TabType, label: 'Expenses', icon: '💸', color: 'from-red-400 to-red-500' },
    { id: 'income' as TabType, label: 'Income', icon: '💰', color: 'from-green-400 to-green-500' },
    { id: 'transfer' as TabType, label: 'Transfer', icon: '🔄', color: 'from-blue-400 to-blue-500' }
  ]

  const currentTab = tabs.find(tab => tab.id === activeTab)

  return (
    <div className={`${className}`}>
      {/* Tab Navigation */}
      <div className="flex bg-surface rounded-lg p-1 mb-4">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            type="button"
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-all ${
              activeTab === tab.id
                ? 'bg-primary text-white shadow-sm'
                : 'text-text-secondary hover:text-text-primary hover:bg-surface-elevated'
            }`}
          >
            <span className="text-xs">{tab.icon}</span>
            <span className="text-xs">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Header with gradient background */}
      <div className={`-mx-4 -mt-4 mb-6 p-4 rounded-t-xl bg-gradient-to-br ${currentTab?.color}`}>
        <h3 className="text-lg font-semibold text-white">
          {currentTab?.label}
        </h3>
        <p className="text-white/80 text-sm">
          {activeTab === 'expense' && 'Track your spending'}
          {activeTab === 'income' && 'Record your earnings'}
          {activeTab === 'transfer' && 'Move money between accounts'}
        </p>
      </div>

      {/* Form Content */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className={`${compact ? 'space-y-4' : 'space-y-6'}`}>
        {/* Amount */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Amount *
          </label>
          <Controller
            name="amount"
            control={control}
            render={({ field }) => (
              <input
                type="text"
                inputMode="decimal"
                {...field}
                placeholder="0.00"
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary focus:outline-none text-text-primary ${errors.amount ? 'border-error' : ''}`}
              />
            )}
          />
          {errors.amount && (
            <p className="text-sm text-error">{errors.amount.message}</p>
          )}
        </div>

        {/* Category */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            {activeTab === 'transfer' ? 'Transfer Category *' : 'Category *'}
          </label>
          <Controller
            name="category_id"
            control={control}
            render={({ field }) => (
              <select
                {...field}
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary focus:outline-none text-text-primary ${errors.category_id ? 'border-error' : ''}`}
              >
                <option value="">
                  {activeTab === 'transfer' ? 'Select a transfer category' : 'Select category'}
                </option>
                {filteredCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              )}
            />
            {activeTab === 'transfer' && filteredCategories.length === 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <span className="text-blue-500 text-sm">ℹ️</span>
                  <div className="text-xs text-blue-700">
                    <p className="font-medium mb-1">No transfer categories found</p>
                    <p>Create a transfer category first. Don't use income or expense categories for transfers.</p>
                  </div>
                </div>
              </div>
            )}
            {errors.category_id && (
              <p className="text-sm text-error">{errors.category_id.message}</p>
            )}
          </div>

        {/* From Account */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            {activeTab === 'transfer' ? 'From Account *' : 'Account *'}
          </label>
          <Controller
            name="account_id"
            control={control}
            render={({ field }) => (
              <select
                {...field}
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary focus:outline-none text-text-primary ${errors.account_id ? 'border-error' : ''}`}
              >
                <option value="">Select account</option>
                {filteredAccounts.map((account) => (
                  <option key={account.id} value={account.id}>
                    {account.name} ({account.account_type})
                  </option>
                ))}
              </select>
            )}
          />
          {errors.account_id && (
            <p className="text-sm text-error">{errors.account_id.message}</p>
          )}
        </div>

        {/* To Account (for transfers) */}
        {activeTab === 'transfer' && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              To Account *
            </label>
            <Controller
              name="to_account_id"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary focus:outline-none text-text-primary ${errors.to_account_id ? 'border-error' : ''}`}
                >
                  <option value="">Select destination account</option>
                  {filteredAccounts.filter(acc => acc.id !== watch('account_id')).map((account) => (
                    <option key={account.id} value={account.id}>
                      {account.name} ({account.account_type})
                    </option>
                  ))}
                </select>
              )}
            />
            {errors.to_account_id && (
              <p className="text-sm text-error">{errors.to_account_id.message}</p>
            )}
          </div>
        )}

        {/* Description */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Description
          </label>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <input
                type="text"
                {...field}
                placeholder="Optional description"
                className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary focus:outline-none text-text-primary"
              />
            )}
          />
        </div>

        {/* Date */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Date *
          </label>
          <Controller
            name="transaction_date"
            control={control}
            render={({ field }) => (
              <input
                type="date"
                {...field}
                value={field.value instanceof Date ? field.value.toISOString().split('T')[0] : field.value}
                onChange={(e) => field.onChange(new Date(e.target.value))}
                className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary focus:outline-none text-text-primary"
              />
            )}
          />
        </div>

        {/* Fees (for transfers) */}
        {activeTab === 'transfer' && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Transfer Fees
            </label>
            <Controller
              name="fees"
              control={control}
              render={({ field }) => (
                <input
                  type="text"
                  inputMode="decimal"
                  {...field}
                  placeholder="0.00"
                  className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary focus:outline-none text-text-primary"
                />
              )}
            />
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting || loading}
          className={`w-full py-3 px-4 rounded-lg font-medium text-white transition-all ${
            isSubmitting || loading
              ? 'bg-gray-400 cursor-not-allowed'
              : `bg-gradient-to-r ${currentTab?.color} hover:shadow-lg transform hover:scale-[1.02]`
          }`}
        >
          {isSubmitting || loading ? 'Saving...' : `Add ${currentTab?.label.slice(0, -1)}`}
        </button>
      </form>
    </div>
  )
}

export default TabbedTransactionForm
