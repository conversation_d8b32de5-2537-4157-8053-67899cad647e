import React, { createContext, useContext, useEffect, useState } from 'react';
import { Session, AuthError } from '@supabase/supabase-js';
import { supabaseMobile as supabase, BiometricService } from '@repo/shared/src/index.mobile';

export interface IAuthUser {
  id: string
  email?: string
  user_metadata?: any
}

export interface IAuthContext {
  user: IAuthUser | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signUp: (email: string, password: string, name: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<{ error: AuthError | null }>
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>
  signInWithBiometrics: () => Promise<{ error: AuthError | null }>
  isBiometricAvailable: () => Promise<boolean>
  isBiometricEnabled: () => Promise<boolean>
  setBiometricEnabled: (enabled: boolean) => Promise<void>
}

const AuthContext = createContext<IAuthContext | undefined>(undefined)

export interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<IAuthUser | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }: { data: { session: Session | null } }) => {
      setSession(session)
      setUser(session?.user ? {
        id: session.user.id,
        email: session.user.email,
        user_metadata: session.user.user_metadata
      } : null)
      setLoading(false)
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event: string, session: Session | null) => {
      setSession(session)
      setUser(session?.user ? {
        id: session.user.id,
        email: session.user.email,
        user_metadata: session.user.user_metadata
      } : null)
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    
    // If login is successful and biometric is enabled, store credentials for future biometric login
    if (!error) {
      const biometricEnabled = await BiometricService.isBiometricEnabled()
      if (biometricEnabled) {
        try {
          await BiometricService.storeCredentials(email, password)
        } catch (biometricError) {
          console.warn('Failed to store credentials for biometric login:', biometricError)
        }
      }
    }
    
    return { error }
  }

  const signUp = async (email: string, password: string, name: string) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
        },
      },
    })
    return { error }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    
    // Clear biometric data on sign out
    try {
      await BiometricService.clearBiometricData()
    } catch (biometricError) {
      console.warn('Failed to clear biometric data:', biometricError)
    }
    
    return { error }
  }

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: 'https://your-app.com/auth/reset-password', // Replace with your app's deep link
    })
    return { error }
  }

  const signInWithBiometrics = async () => {
    try {
      // Check if biometric authentication is available and enabled
      const capabilities = await BiometricService.getCapabilities()
      if (!capabilities.isAvailable) {
        return { error: new Error('Biometric authentication is not available') as AuthError }
      }

      const biometricEnabled = await BiometricService.isBiometricEnabled()
      if (!biometricEnabled) {
        return { error: new Error('Biometric authentication is not enabled') as AuthError }
      }

      // Authenticate with biometrics
      const authResult = await BiometricService.authenticate('Authenticate to sign in')
      if (!authResult.success) {
        return { error: new Error(authResult.error || 'Biometric authentication failed') as AuthError }
      }

      // Get stored credentials
      const credentials = await BiometricService.getStoredCredentials()
      if (!credentials) {
        return { error: new Error('No stored credentials found') as AuthError }
      }

      // Sign in with stored credentials
      const { error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.hashedPassword,
      })

      return { error }
    } catch (error) {
      console.error('Biometric sign-in error:', error)
      return { error: new Error('An unexpected error occurred during biometric sign-in') as AuthError }
    }
  }

  const isBiometricAvailable = async () => {
    const capabilities = await BiometricService.getCapabilities()
    return capabilities.isAvailable
  }

  const isBiometricEnabled = async () => {
    return await BiometricService.isBiometricEnabled()
  }

  const setBiometricEnabled = async (enabled: boolean) => {
    return await BiometricService.setBiometricEnabled(enabled)
  }

  const value: IAuthContext = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    signInWithBiometrics,
    isBiometricAvailable,
    isBiometricEnabled,
    setBiometricEnabled,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}