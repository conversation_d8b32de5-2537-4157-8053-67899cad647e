{"name": "@apps/mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "dev": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "tsc --noEmit", "lint": "eslint \"src/**/*.{ts,tsx}\"", "type-check": "tsc --noEmit"}, "dependencies": {"@react-native-async-storage/async-storage": "1.24.0", "@repo/shared": "*", "expo": "~53.0.18", "expo-local-authentication": "~14.0.1", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-url-polyfill": "^2.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^8.57.0", "typescript": "~5.8.3"}, "private": true}