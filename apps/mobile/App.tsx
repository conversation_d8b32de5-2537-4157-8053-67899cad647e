import React from 'react';
import { AuthProvider, useAuth } from './src/contexts/AuthContext';
import { View, Text, ActivityIndicator, TouchableOpacity, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';

// Design tokens from style guide
const colors = {
  primaryBlue: '#3B82F6',
  primaryPurple: '#8B5CF6',
  background: '#FFFFFF',
  surface: '#F9FAFB',
  surfaceElevated: '#FFFFFF',
  textPrimary: '#111827',
  textSecondary: '#6B7280',
  textTertiary: '#9CA3AF',
  border: '#E5E7EB',
  borderLight: '#F3F4F6',
  successGreen: '#10B981',
  errorRed: '#EF4444'
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centeredContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 20,
    backgroundColor: colors.primaryBlue,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  logoText: {
    color: 'white',
    fontSize: 32,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 32,
  },
  welcomeText: {
    fontSize: 18,
    color: colors.textSecondary,
    marginBottom: 32,
    textAlign: 'center',
  },
  button: {
    backgroundColor: colors.primaryBlue,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  }
})

// Simple placeholder components for now
function AuthScreen() {
  return (
    <View style={styles.container}>
      <View style={styles.centeredContainer}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>💰</Text>
        </View>
        <Text style={styles.title}>Portfolio Tracker</Text>
        <Text style={styles.subtitle}>
          Your personal finance companion
        </Text>
        <Text style={[styles.subtitle, { color: colors.textTertiary }]}>
          Authentication screens will be available soon
        </Text>
      </View>
      <StatusBar style="auto" />
    </View>
  )
}

function DashboardScreen() {
  const { user, signOut } = useAuth()
  
  const handleSignOut = async () => {
    await signOut()
  }
  
  return (
    <View style={styles.container}>
      <View style={styles.centeredContainer}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>📊</Text>
        </View>
        <Text style={styles.title}>Dashboard</Text>
        <Text style={styles.welcomeText}>
          Welcome, {user?.user_metadata?.name || user?.email?.split('@')[0]}!
        </Text>
        <TouchableOpacity style={styles.button} onPress={handleSignOut}>
          <Text style={styles.buttonText}>Sign Out</Text>
        </TouchableOpacity>
      </View>
      <StatusBar style="auto" />
    </View>
  )
}

function AppContent() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primaryBlue} />
        <Text style={[styles.subtitle, { marginTop: 16 }]}>Loading...</Text>
      </View>
    )
  }

  return user ? <DashboardScreen /> : <AuthScreen />
}

export default function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  )
}
