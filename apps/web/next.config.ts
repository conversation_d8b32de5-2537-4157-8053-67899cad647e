import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  webpack: (config, { isServer }) => {
    // Exclude React Native and related packages from web bundle
    config.resolve.alias = {
      ...config.resolve.alias,
      'react-native$': 'react-native-web',
      'react-native-vector-icons': 'react-native-vector-icons/dist',
    }

    // Ignore React Native specific files and modules
    config.resolve.fallback = {
      ...config.resolve.fallback,
      'react-native': false,
      'react-native-vector-icons': false,
      '@react-native-async-storage/async-storage': false,
      'expo-secure-store': false,
      'expo-crypto': false,
      'expo-local-authentication': false,
      'expo-modules-core': false,
      fs: false,
      path: false,
      os: false,
    }

    // Configure xlsx to work with Next.js
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        module: false,
      }
    }

    // Exclude React Native files from compilation
    config.module.rules.push({
      test: /\.mobile\.(ts|tsx|js|jsx)$/,
      loader: 'ignore-loader',
    })

    return config
  },
  
  transpilePackages: ['@repo/shared'],
}

export default nextConfig;
