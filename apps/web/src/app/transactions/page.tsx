'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import {
  TransactionService,
  TransferService,
  CategoryService,
  type ITransaction,
  type ICategory,
  type TransactionFormData,
  type ComprehensiveTransactionFormData,
  useTemplateStore
} from '@repo/shared'
import { TabbedTransactionForm } from '../../components/TabbedTransactionForm'
import TransactionListWeb from '../../components/TransactionList'
import { TransactionTemplates } from '../../components/TransactionTemplates'
import PDFUpload from '../../components/PDFUpload'
import { Modal } from '@/components/Modal'
import { PageLayout } from '../../components/PageLayout'
import { toast } from 'react-hot-toast'
import ProtectedRoute from '@/components/ProtectedRoute'
import { Plus, FileText, X, Upload } from 'lucide-react'
import { type BankStatementData } from '@repo/shared'

export default function TransactionsPage() {
  const searchParams = useSearchParams()
  const { 
    templateData: globalTemplateData, 
    isNavigatingWithTemplate, 
    clearTemplateData, 
    setNavigatingWithTemplate 
  } = useTemplateStore()
  const [categories, setCategories] = useState<ICategory[]>([])
  const [submitting, setSubmitting] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [showTemplates, setShowTemplates] = useState(false)
  const [showPDFUpload, setShowPDFUpload] = useState(false)
  const [editingTransaction, setEditingTransaction] = useState<ITransaction | null>(null)
  const [templateData, setTemplateData] = useState<any>(null)
  const [refreshKey, setRefreshKey] = useState(0)
  const [isPageReady, setIsPageReady] = useState(false)

  useEffect(() => {
    initializePage()
  }, [])

  // Handle template data from global store
  useEffect(() => {
    if (globalTemplateData && isNavigatingWithTemplate && isPageReady) {
      handleTemplateFromStore()
    }
  }, [globalTemplateData, isNavigatingWithTemplate, isPageReady])

  const initializePage = async () => {
    try {
      // Load categories first
      await loadCategories()
      
      // Mark page as ready for smooth animations
      setIsPageReady(true)
      
      // If not coming from template navigation, check URL for backward compatibility
      if (!isNavigatingWithTemplate) {
        await handleLegacyTemplateFromURL()
      }
    } catch (error) {
      console.error('Failed to initialize page:', error)
      toast.error('Failed to load page')
    }
  }

  const handleTemplateFromStore = async () => {
    if (!globalTemplateData) return
    
    // Wait for page to be fully ready before showing modal
    if (!isPageReady) {
      return
    }
    
    // Add a small delay for smooth transition after navigation
    await new Promise(resolve => setTimeout(resolve, 200))
    
    setTemplateData(globalTemplateData)
    setShowForm(true)
    
    // Reset navigation state
    setNavigatingWithTemplate(false)
  }

  const handleLegacyTemplateFromURL = async () => {
    const useTemplate = searchParams.get('use_template')
    if (useTemplate === 'true') {
      const templateFromURL = {
        amount: searchParams.get('amount') || '',
        category_id: searchParams.get('category_id') || '',
        description: searchParams.get('description') || '',
        transaction_type: (searchParams.get('transaction_type') as 'income' | 'expense') || 'expense',
        template_name: searchParams.get('template_name') || '',
        transaction_date: new Date(),
        account_id: '',
        to_account_id: '',
        fees: '',
        investment_symbol: '',
        investment_quantity: '',
        investment_price: '',
        funding_account_id: '',
      }
      
      setTemplateData(templateFromURL)
      
      // Clear URL parameters
      if (typeof window !== 'undefined') {
        const url = new URL(window.location.href)
        url.search = ''
        window.history.replaceState({}, document.title, url.pathname)
      }
      
      // Small delay then open modal
      await new Promise(resolve => setTimeout(resolve, 200))
      setShowForm(true)
    }
  }

  const loadCategories = async () => {
    try {
      // Load all categories
      const categoriesData = await CategoryService.getCategories({ is_active: true })
      setCategories(categoriesData)
    } catch (error) {
      console.error('Failed to load categories:', error)
      toast.error('Failed to load categories')
    }
  }

  const handleSubmit = async (data: ComprehensiveTransactionFormData) => {
    setSubmitting(true)
    try {
      if (editingTransaction) {
        // For basic transactions, use the update method
        if (editingTransaction.transaction_type === 'income' || editingTransaction.transaction_type === 'expense') {
          await TransactionService.updateTransaction(editingTransaction.id, {
            amount: data.amount,
            description: data.description,
            category_id: data.category_id,
            transaction_date: data.transaction_date,
          })
        } else if (editingTransaction.transaction_type === 'transfer') {
          // Use TransferService for transfer updates
          await TransferService.updateTransfer(editingTransaction.id, {
            amount: data.amount,
            description: data.description,
            category_id: data.category_id,
            transaction_date: data.transaction_date,
            fees: data.fees,
            is_internal: data.is_internal
          })
        } else {
          throw new Error('Cannot update investment transactions')
        }
        toast.success('Transaction updated successfully!')
      } else {
        // Route to appropriate service based on transaction type
        if (data.transaction_type === 'transfer') {
          // Transform data for transfer service
          const transferData = {
            from_account_id: data.account_id!, // Form uses account_id for from account
            to_account_id: data.to_account_id!,
            amount: data.amount,
            description: data.description || '',
            category_id: data.category_id,
            transaction_date: data.transaction_date,
            fees: data.fees,
            is_internal: data.is_internal
          }
          await TransferService.createTransfer(transferData)
        } else {
          await TransactionService.createComprehensiveTransaction(data)
        }
        
        const transactionTypeLabel = {
          income: 'Income',
          expense: 'Expense',
          transfer: 'Transfer'
        }[data.transaction_type] || 'Transaction'

        toast.success(`${transactionTypeLabel} added successfully!`)
      }
      // Only close form and reset state on successful submission
      setShowForm(false)
      setEditingTransaction(null)
      setTemplateData(null)
      clearTemplateData() // Clear global template data
      setRefreshKey(prev => prev + 1)
    } catch (error) {
      console.error('Failed to save transaction:', error)
      console.error('Transaction data that failed:', data)
      // Display more detailed error message if available
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      toast.error(`Failed to ${editingTransaction ? 'update' : 'add'} transaction: ${errorMessage}`)
      // Don't close the form on error - keep it open with user's data
    } finally {
      setSubmitting(false)
    }
  }


  const handleEditTransaction = (transaction: ITransaction) => {
    setEditingTransaction(transaction)
    setShowForm(true)
  }

  const handleCancelEdit = () => {
    setEditingTransaction(null)
    setTemplateData(null)
    clearTemplateData() // Clear global template data
    setShowForm(false)
  }

  const handleUseTemplate = (template: Omit<any, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    setTemplateData({
      amount: template.amount?.toString() || '',
      category_id: template.category_id || '',
      description: template.description || '',
      transaction_date: new Date(),
      transaction_type: template.transaction_type || 'expense',
      account_id: '',
      to_account_id: '',
      fees: '',
      investment_symbol: '',
      investment_quantity: '',
      investment_price: '',
      funding_account_id: '',
    })
    setShowTemplates(false)
    setShowForm(true)
  }

  const handleTransactionsExtracted = async (data: BankStatementData) => {
    try {
      toast.success(`Successfully imported transactions from PDF!`)
      setShowPDFUpload(false)
      setRefreshKey(prev => prev + 1) // Refresh transaction list
    } catch (error) {
      console.error('Failed to process extracted transactions:', error)
      toast.error('Failed to process extracted transactions')
    }
  }

  const pageActions = [
    ...(editingTransaction ? [{
      label: 'Cancel Edit',
      onClick: handleCancelEdit,
      variant: 'secondary' as const,
      icon: X
    }] : []),
    {
      label: 'Import PDF',
      onClick: () => setShowPDFUpload(true),
      variant: 'secondary' as const,
      icon: Upload
    },
    {
      label: 'Templates',
      onClick: () => setShowTemplates(true),
      variant: 'secondary' as const,
      icon: FileText
    },
    {
      label: 'Add Transaction',
      onClick: () => setShowForm(true),
      variant: 'primary' as const,
      icon: Plus
    }
  ]

  return (
    <ProtectedRoute>
      <PageLayout
        title="Transactions"
        description="Track your income, expenses, transfers, and investments"
        actions={pageActions}
      >

        {/* Enhanced Transactions List */}
        <TransactionListWeb
          key={refreshKey}
          onEditTransaction={handleEditTransaction}
        />

        {/* Add/Edit Transaction Modal */}
        <Modal
          isOpen={showForm}
          onClose={() => {
            setShowForm(false)
            setEditingTransaction(null)
            setTemplateData(null)
            clearTemplateData() // Clear global template data
          }}
          title={editingTransaction ? 'Edit Transaction' : templateData ? 'New Transaction from Template' : 'Add New Transaction'}
          size="xl"
        >
          <TabbedTransactionForm
            onSubmit={handleSubmit}
            loading={submitting}
            compact={true}
            isEditing={!!editingTransaction}
            initialData={editingTransaction ? {
              amount: editingTransaction.amount?.toString() || '',
              category_id: editingTransaction.category_id || '',
              account_id: editingTransaction.account_id || '',
              to_account_id: editingTransaction.to_account_id || '',
              description: editingTransaction.description || '',
              transaction_date: new Date(editingTransaction.transaction_date),
              transaction_type: editingTransaction.transaction_type,
              fees: editingTransaction.fees?.toString() || '',
            } : templateData ? templateData : undefined}
          />
        </Modal>

        {/* Templates Modal */}
        <Modal
          isOpen={showTemplates}
          onClose={() => setShowTemplates(false)}
          title="Transaction Templates"
          size="lg"
        >
          <div className="space-y-4">
            <p className="text-text-secondary">
              Choose from your saved templates to quickly create new transactions.
            </p>
            <TransactionTemplates 
              categories={categories}
              onUseTemplate={handleUseTemplate}
            />
          </div>
        </Modal>

        {/* PDF Upload Modal */}
        <Modal
          isOpen={showPDFUpload}
          onClose={() => setShowPDFUpload(false)}
          title="Import Bank Statement"
          size="xl"
        >
          <div className="space-y-4">
            <p className="text-text-secondary">
              Upload your bank statement PDF to extract and import transaction data. Review the extracted transactions and fill in any missing details before importing.
            </p>
            <PDFUpload onTransactionsExtracted={handleTransactionsExtracted} />
          </div>
        </Modal>
      </PageLayout>
    </ProtectedRoute>
  )
}