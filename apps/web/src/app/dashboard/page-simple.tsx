'use client'

import ProtectedRoute from '@/components/ProtectedRoute'
import { useAuth } from '../../contexts/AuthContext'
import { ThemeToggleButton } from '../../components/ThemeToggle'
import Link from 'next/link'

export default function DashboardPage() {
  const { user, signOut } = useAuth()

  const handleSignOut = async () => {
    await signOut()
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <nav className="bg-white dark:bg-gray-800 shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Portfolio Tracker</h1>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-gray-700 dark:text-gray-300">Welcome, {user?.user_metadata?.name || user?.email}</span>
                <ThemeToggleButton />
                <Link
                  href="/transactions"
                  className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                >
                  Transactions
                </Link>
                <Link
                  href="/budgets"
                  className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                >
                  Budgets
                </Link>
                <Link
                  href="/profile"
                  className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                >
                  Profile
                </Link>
                <button
                  onClick={handleSignOut}
                  className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Sign out
                </button>
              </div>
            </div>
          </div>
        </nav>

        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">Dashboard</h2>
              <p className="text-gray-600 dark:text-gray-300">Welcome to your financial dashboard</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Quick Actions</h3>
                <div className="space-y-3">
                  <Link
                    href="/transactions"
                    className="block w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-center"
                  >
                    Add Transaction
                  </Link>
                  <Link
                    href="/budgets"
                    className="block w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-center"
                  >
                    Manage Budgets
                  </Link>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Recent Activity</h3>
                <p className="text-gray-600 dark:text-gray-300">No recent transactions</p>
              </div>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">Account Overview</h3>
                <p className="text-gray-600 dark:text-gray-300">Your financial summary will appear here</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}