'use client'

import { useAuth } from '../../contexts/AuthContext'
import { ProfileProvider } from '../../contexts/ProfileContext'
import { ProfileForm } from '../../components/ProfileForm'
import { DataExport } from '../../components/DataExport'
import { PageLayout } from '../../components/PageLayout'
import { TabNavigation } from '../../components/TabNavigation'
import { ContentCard } from '../../components/ContentCard'
import ProtectedRoute from '../../components/ProtectedRoute'
import { useState } from 'react'
import { User, Download } from 'lucide-react'

export default function ProfilePage() {
  const { user, signOut } = useAuth()
  const [activeTab, setActiveTab] = useState<'profile' | 'data'>('profile')

  const handleSignOut = async () => {
    await signOut()
  }

  const tabs = [
    { id: 'profile', label: 'Profile Information', icon: User },
    { id: 'data', label: 'Data Export', icon: Download }
  ]

  return (
    <ProtectedRoute>
      <ProfileProvider>
        <PageLayout
          title="Profile Settings"
          description="Manage your account settings and preferences"
        >
          {/* Tab Navigation */}
          <TabNavigation
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={(tabId) => setActiveTab(tabId as 'profile' | 'data')}
          />

          {/* Content */}
          <ContentCard>
            {activeTab === 'profile' && (
              <ProfileForm />
            )}
            {activeTab === 'data' && (
              <DataExport />
            )}
          </ContentCard>
        </PageLayout>
      </ProfileProvider>
    </ProtectedRoute>
  )
}