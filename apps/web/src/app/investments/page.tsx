'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  AccountService,
  InvestmentService,
  type IAccount,
  type IInvestmentTransaction,
  useCurrencyStore
} from '@shared/index'
import { InvestmentForm } from '../../components/InvestmentForm'
import { Modal } from '@/components/Modal'
import dynamic from 'next/dynamic'

// Dynamically import CSVImport to avoid bundling xlsx on initial load
const CSVImport = dynamic(() => import('../../components/CSVImport'), {
  loading: () => <div className="flex items-center justify-center p-8">Loading...</div>,
  ssr: false
})
import { LoadingState } from '@/components/LoadingState'
import { EmptyState } from '@/components/EmptyState'
import { PageLayout } from '@/components/PageLayout'
import { TabNavigation } from '@/components/TabNavigation'
import { MetricCard } from '@/components/MetricCard'
import { ContentCard } from '@/components/ContentCard'
import { toast } from 'react-hot-toast'
import ProtectedRoute from '@/components/ProtectedRoute'
import { Plus, Upload, DollarSign, TrendingUp, BarChart3, PieChart, FileText } from 'lucide-react'

type TabType = 'overview' | 'transactions'

export default function InvestmentsPage() {
  const { formatCurrency } = useCurrencyStore()
  const [activeTab, setActiveTab] = useState<TabType>('overview')
  const [investmentAccounts, setInvestmentAccounts] = useState<IAccount[]>([])
  const [investmentTransactions, setInvestmentTransactions] = useState<IInvestmentTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [showInvestmentForm, setShowInvestmentForm] = useState(false)
  const [showCSVImport, setShowCSVImport] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  useEffect(() => {
    loadInvestmentData()
  }, [refreshKey])

  const loadInvestmentData = async () => {
    try {
      setLoading(true)
      const [accounts, transactions] = await Promise.all([
        AccountService.getAccounts({ account_type: 'investment' }),
        InvestmentService.getInvestmentTransactions()
      ])
      setInvestmentAccounts(accounts)
      setInvestmentTransactions(transactions.data)
    } catch (error) {
      console.error('Failed to load investment data:', error)
      toast.error('Failed to load investment data')
    } finally {
      setLoading(false)
    }
  }

  const handleInvestmentSubmit = async (data: any) => {
    try {
      setSubmitting(true)
      await InvestmentService.createInvestmentTransaction(data)
      toast.success('Investment transaction created successfully!')
      setShowInvestmentForm(false)
      setRefreshKey(prev => prev + 1)
    } catch (error) {
      console.error('Error submitting investment transaction:', error)
      toast.error('Failed to submit investment transaction')
    } finally {
      setSubmitting(false)
    }
  }

  const handleCSVImportComplete = (result: any) => {
    if (result.success > 0) {
      toast.success(`Successfully imported ${result.success} transactions!`)
      setRefreshKey(prev => prev + 1)
    }
    if (result.errors.length > 0) {
      toast.error(`${result.errors.length} transactions failed to import`)
    }
    setShowCSVImport(false)
  }

  const calculatePortfolioValue = () => {
    return investmentAccounts.reduce((total, account) => total + (account.current_balance || 0), 0)
  }

  const calculateTotalInvested = () => {
    return investmentTransactions
      .filter(t => t.transaction_type === 'investment_buy')
      .reduce((total, t) => total + t.amount, 0)
  }

  const calculateTotalReturns = () => {
    const totalValue = calculatePortfolioValue()
    const totalInvested = calculateTotalInvested()
    return totalValue - totalInvested
  }

  const getReturnPercentage = () => {
    const totalInvested = calculateTotalInvested()
    if (totalInvested === 0) return 0
    return ((calculateTotalReturns() / totalInvested) * 100)
  }

  const pageActions = [
    {
      label: 'Import CSV',
      onClick: () => setShowCSVImport(true),
      variant: 'secondary' as const,
      icon: Upload
    },
    {
      label: 'Add Investment',
      onClick: () => setShowInvestmentForm(true),
      variant: 'primary' as const,
      icon: Plus
    }
  ]

  const tabs = [
    { id: 'overview', label: 'Portfolio Overview', icon: BarChart3 },
    { id: 'transactions', label: 'Transactions', icon: FileText }
  ]

  if (loading) {
    return (
      <ProtectedRoute>
        <PageLayout
          title="Investment Portfolio"
          description="Monitor your investment performance and grow your wealth"
          actions={pageActions}
        >
          <LoadingState message="Loading investment data..." />
        </PageLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <PageLayout
        title="Investment Portfolio"
        description="Monitor your investment performance and grow your wealth"
        actions={pageActions}
      >
        {/* Tab Navigation */}
        <TabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={(tabId) => setActiveTab(tabId as TabType)}
        />

        {/* Overview Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Primary KPI Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <MetricCard
                title="Total Value"
                value={formatCurrency(calculatePortfolioValue())}
                subtitle="Current market value"
                icon={DollarSign}
                color="blue"
              />
              
              <MetricCard
                title="Invested"
                value={formatCurrency(calculateTotalInvested())}
                subtitle="Capital deployed"
                icon={TrendingUp}
                color="green"
              />

              <MetricCard
                title="Returns"
                value={`${calculateTotalReturns() >= 0 ? '+' : ''}${formatCurrency(calculateTotalReturns())}`}
                subtitle={calculateTotalReturns() >= 0 ? 'Profit earned' : 'Current loss'}
                icon={BarChart3}
                color={calculateTotalReturns() >= 0 ? 'green' : 'red'}
                trend={{
                  value: `${calculateTotalReturns() >= 0 ? '+' : ''}${formatCurrency(calculateTotalReturns())}`,
                  direction: calculateTotalReturns() >= 0 ? 'up' : 'down'
                }}
              />

              <MetricCard
                title="Performance"
                value={`${getReturnPercentage() >= 0 ? '+' : ''}${Math.abs(getReturnPercentage()).toFixed(1)}%`}
                subtitle="Overall return rate"
                icon={PieChart}
                color={getReturnPercentage() >= 0 ? 'purple' : 'orange'}
                trend={{
                  value: `${Math.abs(getReturnPercentage()).toFixed(1)}%`,
                  direction: getReturnPercentage() >= 0 ? 'up' : 'down'
                }}
              />
            </div>

            {/* Portfolio Insights */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <ContentCard>
                <div className="flex items-center gap-4 mb-6">
                  <div className="p-3 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl text-white">
                    <PieChart className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-text-primary">Portfolio Insights</h3>
                    <p className="text-text-secondary text-sm">Investment allocation overview</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex justify-between items-center py-3 px-4 bg-surface rounded-lg border border-border">
                    <span className="text-text-secondary font-medium">Total Accounts</span>
                    <span className="text-2xl font-bold text-text-primary">{investmentAccounts.length}</span>
                  </div>
                  <div className="flex justify-between items-center py-3 px-4 bg-surface rounded-lg border border-border">
                    <span className="text-text-secondary font-medium">Total Transactions</span>
                    <span className="text-2xl font-bold text-text-primary">{investmentTransactions.length}</span>
                  </div>
                  <div className="flex justify-between items-center py-3 px-4 bg-surface rounded-lg border border-border">
                    <span className="text-text-secondary font-medium">Avg. Transaction</span>
                    <span className="text-2xl font-bold text-text-primary">
                      {investmentTransactions.length > 0 ? formatCurrency(calculateTotalInvested() / investmentTransactions.filter(t => t.transaction_type === 'investment_buy').length || 0) : formatCurrency(0)}
                    </span>
                  </div>
                </div>
              </ContentCard>

              <ContentCard>
                <div className="flex items-center gap-4 mb-6">
                  <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl text-white">
                    <TrendingUp className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-text-primary">Performance Summary</h3>
                    <p className="text-text-secondary text-sm">Key performance indicators</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex justify-between items-center py-3 px-4 bg-surface rounded-lg border border-border">
                    <span className="text-text-secondary font-medium">Best Performer</span>
                    <span className="text-text-primary font-bold">📈 Coming Soon</span>
                  </div>
                  <div className="flex justify-between items-center py-3 px-4 bg-surface rounded-lg border border-border">
                    <span className="text-text-secondary font-medium">Dividend Income</span>
                    <span className="text-2xl font-bold text-success-green">
                      {formatCurrency(investmentTransactions.filter(t => t.transaction_type === 'dividend').reduce((sum, t) => sum + t.amount, 0))}
                    </span>
                  </div>
                  <div className="flex justify-between items-center py-3 px-4 bg-surface rounded-lg border border-border">
                    <span className="text-text-secondary font-medium">Growth Rate</span>
                    <span className={`text-2xl font-bold ${getReturnPercentage() >= 0 ? 'text-success-green' : 'text-error-red'}`}>
                      {getReturnPercentage() >= 0 ? '📈' : '📉'} {Math.abs(getReturnPercentage()).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </ContentCard>
            </div>

                {/* Recent Transactions */}
                {investmentTransactions.length > 0 && (
                  <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-2xl p-8 shadow-lg">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-4">
                        <div className="p-3 bg-gradient-to-r from-slate-500 to-gray-600 rounded-xl text-white">
                          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-slate-900">Recent Activity</h3>
                          <p className="text-slate-600 text-sm">Latest investment transactions</p>
                        </div>
                      </div>
                      <button
                        onClick={() => setActiveTab('transactions')}
                        className="bg-gradient-to-r from-primary-blue to-primary-purple text-white px-6 py-2 rounded-lg text-sm font-semibold hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5"
                      >
                        View All →
                      </button>
                    </div>
                    <div className="space-y-3">
                      {investmentTransactions.slice(0, 4).map((transaction) => (
                        <div key={transaction.id} className="flex items-center justify-between p-6 bg-white rounded-xl border border-slate-100 hover:shadow-md transition-all duration-200 hover:-translate-y-0.5">
                          <div className="flex items-center gap-4">
                            <div className={`p-3 rounded-xl ${
                              transaction.transaction_type === 'investment_buy'
                                ? 'bg-green-100 text-green-600'
                                : transaction.transaction_type === 'investment_sell'
                                ? 'bg-red-100 text-red-600'
                                : 'bg-blue-100 text-blue-600'
                            }`}>
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={
                                  transaction.transaction_type === 'investment_buy'
                                    ? "M7 11l5-5m0 0l5 5m-5-5v12"
                                    : transaction.transaction_type === 'investment_sell'
                                    ? "M17 13l-5 5m0 0l-5-5m5 5V6"
                                    : "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                                } />
                              </svg>
                            </div>
                            <div>
                              <p className="font-bold text-slate-900 text-lg">{transaction.investment_symbol}</p>
                              <p className="text-sm text-slate-600">
                                <span className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-semibold mr-2 ${
                                  transaction.transaction_type === 'investment_buy'
                                    ? 'bg-green-100 text-green-800'
                                    : transaction.transaction_type === 'investment_sell'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-blue-100 text-blue-800'
                                }`}>
                                  {transaction.transaction_type === 'investment_buy' ? 'BUY' :
                                   transaction.transaction_type === 'investment_sell' ? 'SELL' : 'DIV'}
                                </span>
                                {new Date(transaction.transaction_date).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className={`text-xl font-bold ${
                              transaction.transaction_type === 'investment_buy'
                                ? 'text-red-600'
                                : 'text-green-600'
                            }`}>
                              {transaction.transaction_type === 'investment_buy' ? '-' : '+'}{formatCurrency(transaction.amount).replace(/^[^\d-]/, '')}
                            </p>
                            {transaction.investment_quantity && (
                              <p className="text-sm text-slate-600">
                                {transaction.investment_quantity.toLocaleString()} shares
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

        {/* Transactions Tab Content */}
        {activeTab === 'transactions' && (
          <div className="space-y-6">
            <ContentCard>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                <h2 className="text-xl font-semibold text-text-primary">Investment Transactions</h2>
                <div className="flex items-center gap-3">
                  <select className="px-3 py-2 border border-border rounded-lg bg-background text-text-primary text-sm">
                    <option value="">All Types</option>
                    <option value="investment_buy">Buy</option>
                    <option value="investment_sell">Sell</option>
                    <option value="dividend">Dividend</option>
                  </select>
                  <select className="px-3 py-2 border border-border rounded-lg bg-background text-text-primary text-sm">
                    <option value="">All Accounts</option>
                    {investmentAccounts.map((account) => (
                      <option key={account.id} value={account.id}>{account.name}</option>
                    ))}
                  </select>
                </div>
              </div>
              {investmentTransactions.length === 0 ? (
                <EmptyState
                  icon={FileText}
                  title="No Transactions Found"
                  description="Start by adding your first investment transaction using the 'Add Investment' button above."
                  action={{
                    label: 'Add Investment',
                    onClick: () => setShowInvestmentForm(true)
                  }}
                />
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-border">
                        <th className="text-left py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Date</th>
                        <th className="text-left py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Type</th>
                        <th className="text-left py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Symbol</th>
                        <th className="text-left py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Account</th>
                        <th className="text-right py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Quantity</th>
                        <th className="text-right py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Price</th>
                        <th className="text-right py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Amount</th>
                        <th className="text-right py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {investmentTransactions.map((transaction) => (
                        <tr key={transaction.id} className="border-b border-border hover:bg-surface-elevated transition-colors">
                          <td className="py-4 px-4 text-text-primary">
                            <div className="flex flex-col">
                              <span className="font-medium">{new Date(transaction.transaction_date).toLocaleDateString()}</span>
                              <span className="text-xs text-text-secondary">{new Date(transaction.transaction_date).toLocaleTimeString()}</span>
                            </div>
                          </td>
                          <td className="py-4 px-4">
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${
                              transaction.transaction_type === 'investment_buy'
                                ? 'bg-green-100 text-green-800'
                                : transaction.transaction_type === 'investment_sell'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-blue-100 text-blue-800'
                            }`}>
                              {transaction.transaction_type === 'investment_buy' ? 'BUY' :
                               transaction.transaction_type === 'investment_sell' ? 'SELL' : 'DIVIDEND'}
                            </span>
                          </td>
                          <td className="py-4 px-4">
                            <div className="flex flex-col">
                              <span className="font-semibold text-text-primary">{transaction.investment_symbol || 'N/A'}</span>
                              {transaction.description && (
                                <span className="text-xs text-text-secondary truncate max-w-32">{transaction.description}</span>
                              )}
                            </div>
                          </td>
                          <td className="py-4 px-4 text-text-primary">
                            <span className="text-sm">{transaction.account?.name || 'Unknown'}</span>
                          </td>
                          <td className="py-4 px-4 text-right text-text-primary font-medium">
                            {transaction.investment_quantity ? transaction.investment_quantity.toLocaleString() : 'N/A'}
                          </td>
                          <td className="py-4 px-4 text-right text-text-primary font-medium">
                            {transaction.investment_price ? formatCurrency(transaction.investment_price) : 'N/A'}
                          </td>
                          <td className="py-4 px-4 text-right">
                            <span className={`font-bold ${
                              transaction.transaction_type === 'investment_buy'
                                ? 'text-red-600'
                                : 'text-green-600'
                            }`}>
                              {transaction.transaction_type === 'investment_buy' ? '-' : '+'}{formatCurrency(transaction.amount).replace(/^[^\d-]/, '')}
                            </span>
                            {transaction.fees && transaction.fees > 0 && (
                              <div className="text-xs text-text-secondary">Fee: {formatCurrency(transaction.fees)}</div>
                            )}
                          </td>
                          <td className="py-4 px-4 text-right">
                            <div className="flex items-center justify-end gap-2">
                              <button className="text-text-secondary hover:text-text-primary p-1 rounded transition-colors">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                              </button>
                              <button className="text-text-secondary hover:text-error-red p-1 rounded transition-colors">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  {investmentTransactions.length > 10 && (
                    <div className="mt-4 text-center">
                      <button className="text-primary-blue hover:text-primary-blue/80 font-medium text-sm">
                        View All Transactions ({investmentTransactions.length})
                      </button>
                    </div>
                  )}
                </div>
              )}
            </ContentCard>
          </div>
        )}
      </PageLayout>

      {/* Investment Form Modal */}
      <Modal
        isOpen={showInvestmentForm}
        onClose={() => setShowInvestmentForm(false)}
        title="Investment Transaction"
        size="xl"
      >
        <InvestmentForm
          onSubmit={handleInvestmentSubmit}
          loading={submitting}
          compact={true}
        />
      </Modal>

      {/* CSV Import Modal */}
      <Modal
        isOpen={showCSVImport}
        onClose={() => setShowCSVImport(false)}
        title="Import Investment Transactions"
        size="2xl"
      >
        <CSVImport
          onImportComplete={handleCSVImportComplete}
        />
      </Modal>
    </ProtectedRoute>
  )
}
