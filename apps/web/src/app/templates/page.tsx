'use client'

import { useState, useEffect } from 'react'
import {
  CategoryService,
  TransactionService,
  TransferService,
  type ICategory,
  type ComprehensiveTransactionFormData,
  useCurrencyStore,
  useTemplateStore
} from '@repo/shared'
import { TransactionTemplates } from '../../components/TransactionTemplates'
import { TemplateForm } from '../../components/TemplateForm'
import { TabbedTransactionForm } from '../../components/TabbedTransactionForm'
import { Modal } from '@/components/Modal'
import { LoadingState } from '@/components/LoadingState'
import { EmptyState } from '@/components/EmptyState'
import { PageLayout } from '@/components/PageLayout'
import { TabNavigation } from '@/components/TabNavigation'
import { ContentCard } from '@/components/ContentCard'
import { toast } from 'react-hot-toast'
import ProtectedRoute from '@/components/ProtectedRoute'
import { supabase } from '@repo/shared'
import { useAuth } from '../../contexts/AuthContext'
import { Plus, FileText, Edit2, Trash2 } from 'lucide-react'

export type ITransactionTemplate = {
  id: string
  user_id: string
  name: string
  amount: number
  category_id: string | null
  description: string | null
  transaction_type: 'income' | 'expense'
  is_recurring: boolean
  frequency: string | null
  auto_create: boolean
  next_due_date: string | null
  created_at: string
  updated_at: string
}

export default function TemplatesPage() {
  const { user } = useAuth()
  const { formatCurrency } = useCurrencyStore()
  const [templates, setTemplates] = useState<ITransactionTemplate[]>([])
  const [categories, setCategories] = useState<ICategory[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState<ITransactionTemplate | null>(null)
  const [filter, setFilter] = useState<'all' | 'income' | 'expense' | 'recurring'>('all')
  
  // Transaction form states
  const [showTransactionForm, setShowTransactionForm] = useState(false)
  const [templateData, setTemplateData] = useState<any>(null)
  const [submittingTransaction, setSubmittingTransaction] = useState(false)

  useEffect(() => {
    if (user) {
      loadData()
    }
  }, [user])

  const loadData = async () => {
    try {
      setLoading(true)
      const [templatesData, categoriesData] = await Promise.all([
        loadTemplates(),
        CategoryService.getCategories({ is_active: true })
      ])
      setCategories(categoriesData)
    } catch (error) {
      console.error('Failed to load data:', error)
      toast.error('Failed to load templates')
    } finally {
      setLoading(false)
    }
  }

  const loadTemplates = async () => {
    if (!user) return []

    try {
      const { data, error } = await supabase
        .from('transaction_templates')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching templates:', error)
        return []
      }

      setTemplates(data || [])
      return data || []
    } catch (error) {
      console.error('Error fetching templates:', error)
      return []
    }
  }

  const handleCreateTemplate = async (data: any) => {
    if (!user) return

    try {
      const { data: newTemplate, error } = await supabase
        .from('transaction_templates')
        .insert({
          ...data,
          user_id: user.id
        })
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      setTemplates(prev => [newTemplate, ...prev])
      toast.success('Template created successfully!')
      setShowForm(false)
    } catch (error: any) {
      console.error('Failed to create template:', error)
      toast.error(error.message || 'Failed to create template')
      throw error
    }
  }

  const handleUpdateTemplate = async (data: any) => {
    if (!editingTemplate || !user) return

    try {
      const { data: updatedTemplate, error } = await supabase
        .from('transaction_templates')
        .update(data)
        .eq('id', editingTemplate.id)
        .eq('user_id', user.id)
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      setTemplates(prev => prev.map(t => t.id === editingTemplate.id ? updatedTemplate : t))
      toast.success('Template updated successfully!')
      setEditingTemplate(null)
    } catch (error: any) {
      console.error('Failed to update template:', error)
      toast.error(error.message || 'Failed to update template')
      throw error
    }
  }

  const handleDeleteTemplate = async (template: ITransactionTemplate) => {
    if (!confirm(`Are you sure you want to delete "${template.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      const { error } = await supabase
        .from('transaction_templates')
        .delete()
        .eq('id', template.id)
        .eq('user_id', user?.id)

      if (error) {
        throw new Error(error.message)
      }

      setTemplates(prev => prev.filter(t => t.id !== template.id))
      toast.success('Template deleted successfully!')
    } catch (error: any) {
      console.error('Failed to delete template:', error)
      toast.error(error.message || 'Failed to delete template')
    }
  }

  const handleUseTemplate = (template: ITransactionTemplate) => {
    // Prepare template data for the transaction form
    const transactionData = {
      amount: template.amount.toString(),
      category_id: template.category_id || '',
      description: template.description || '',
      transaction_type: template.transaction_type,
      template_name: template.name,
      transaction_date: new Date(),
      account_id: '',
      to_account_id: '',
      fees: '',
      investment_symbol: '',
      investment_quantity: '',
      investment_price: '',
      funding_account_id: '',
    }
    
    setTemplateData(transactionData)
    setShowTransactionForm(true)
    toast.success(`Using template: ${template.name}`)
  }

  const handleTransactionSubmit = async (data: ComprehensiveTransactionFormData) => {
    setSubmittingTransaction(true)
    try {
      // Route to appropriate service based on transaction type
      if (data.transaction_type === 'transfer') {
        // Transform data for transfer service
        const transferData = {
          from_account_id: data.account_id!, // Form uses account_id for from account
          to_account_id: data.to_account_id!,
          amount: data.amount,
          description: data.description || '',
          category_id: data.category_id,
          transaction_date: data.transaction_date,
          fees: data.fees,
          is_internal: data.is_internal
        }
        await TransferService.createTransfer(transferData)
      } else {
        await TransactionService.createComprehensiveTransaction(data)
      }
      
      const transactionTypeLabel = {
        income: 'Income',
        expense: 'Expense',
        transfer: 'Transfer'
      }[data.transaction_type] || 'Transaction'

      toast.success(`${transactionTypeLabel} created successfully from template!`)
      setShowTransactionForm(false)
      setTemplateData(null)
    } catch (error) {
      console.error('Failed to create transaction:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      toast.error(`Failed to create transaction: ${errorMessage}`)
    } finally {
      setSubmittingTransaction(false)
    }
  }

  const filteredTemplates = templates.filter(template => {
    if (filter === 'all') return true
    if (filter === 'recurring') return template.is_recurring
    return template.transaction_type === filter
  })

  const getCategoryName = (categoryId: string | null) => {
    if (!categoryId) return 'No Category'
    const category = categories.find(c => c.id === categoryId)
    return category?.name || 'Unknown Category'
  }

  const pageActions = [{
    label: 'Create Template',
    onClick: () => setShowForm(true),
    variant: 'primary' as const,
    icon: Plus
  }]

  const tabs = [
    { id: 'all', label: 'All Templates', count: templates.length },
    { id: 'expense', label: 'Expense', count: templates.filter(t => t.transaction_type === 'expense').length },
    { id: 'income', label: 'Income', count: templates.filter(t => t.transaction_type === 'income').length },
    { id: 'recurring', label: 'Recurring', count: templates.filter(t => t.is_recurring).length }
  ]

  if (loading) {
    return (
      <ProtectedRoute>
        <PageLayout
          title="Transaction Templates"
          description="Save time with reusable transaction templates"
          actions={pageActions}
        >
          <LoadingState message="Loading templates..." />
        </PageLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <PageLayout
        title="Transaction Templates"
        description="Save time with reusable transaction templates"
        actions={pageActions}
      >
        {/* Filter Tabs */}
        <TabNavigation
          tabs={tabs}
          activeTab={filter}
          onTabChange={(tabId) => setFilter(tabId as typeof filter)}
        />

        {/* Templates Grid */}
        {filteredTemplates.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map((template) => (
              <ContentCard
                key={template.id}
                hoverable
                className="border border-border"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="font-semibold text-text-primary text-lg mb-1">{template.name}</h3>
                    <p className="text-sm text-text-secondary">{getCategoryName(template.category_id)}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setEditingTemplate(template)}
                      className="text-text-secondary hover:text-primary-blue transition-colors"
                      title="Edit template"
                    >
                      <Edit2 className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteTemplate(template)}
                      className="text-text-secondary hover:text-error-red transition-colors"
                      title="Delete template"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className={`text-2xl font-bold ${
                    template.transaction_type === 'income' ? 'text-success-green' : 'text-error-red'
                  }`}>
                    {template.transaction_type === 'income' ? '+' : '-'}{formatCurrency(template.amount)}
                  </div>

                  {template.description && (
                    <p className="text-sm text-text-secondary">{template.description}</p>
                  )}

                  <div className="flex items-center gap-2 text-xs">
                    <span className={`px-2 py-1 rounded-full ${
                      template.transaction_type === 'income' 
                        ? 'bg-success-green/10 text-success-green' 
                        : 'bg-error-red/10 text-error-red'
                    }`}>
                      {template.transaction_type}
                    </span>
                    {template.is_recurring && (
                      <span className="px-2 py-1 rounded-full bg-primary-blue/10 text-primary-blue">
                        {template.frequency}
                      </span>
                    )}
                    {template.auto_create && (
                      <span className="px-2 py-1 rounded-full bg-primary-purple/10 text-primary-purple">
                        Auto
                      </span>
                    )}
                  </div>

                  <button
                    onClick={() => handleUseTemplate(template)}
                    className="w-full bg-gradient-to-r from-primary-blue to-primary-purple text-white py-2 px-4 rounded-lg hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 text-sm font-medium"
                  >
                    Use Template
                  </button>
                </div>
              </ContentCard>
            ))}
          </div>
        ) : (
          <EmptyState
            icon={FileText}
            title="No Templates Found"
            description={
              filter === 'all' 
                ? "You don't have any templates yet. Create your first template to get started."
                : `No ${filter} templates found. Try switching to a different filter or create a new template.`
            }
            action={{
              label: 'Create Template',
              onClick: () => setShowForm(true)
            }}
          />
        )}

        {/* Create Template Modal */}
        <Modal
          isOpen={showForm}
          onClose={() => setShowForm(false)}
          title="Create Template"
          size="md"
        >
          <TemplateForm
            onSubmit={handleCreateTemplate}
            categories={categories}
          />
        </Modal>

        {/* Edit Template Modal */}
        <Modal
          isOpen={!!editingTemplate}
          onClose={() => setEditingTemplate(null)}
          title="Edit Template"
          size="md"
        >
          {editingTemplate && (
            <TemplateForm
              onSubmit={handleUpdateTemplate}
              categories={categories}
              initialData={editingTemplate}
            />
          )}
        </Modal>

        {/* Add Transaction from Template Modal */}
        <Modal
          isOpen={showTransactionForm}
          onClose={() => {
            setShowTransactionForm(false)
            setTemplateData(null)
          }}
          title={templateData ? `Create Transaction from "${templateData.template_name}"` : 'Create Transaction'}
          size="xl"
        >
          <TabbedTransactionForm
            onSubmit={handleTransactionSubmit}
            loading={submittingTransaction}
            compact={true}
            initialData={templateData}
          />
        </Modal>
      </PageLayout>
    </ProtectedRoute>
  )
}
