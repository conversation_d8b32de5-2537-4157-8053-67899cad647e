@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Primary Colors */
  --primary-blue: #3B82F6;
  --primary-purple: #8B5CF6;
  --primary-gradient: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);

  /* Semantic Colors */
  --success-green: #10B981;
  --error-red: #EF4444;
  --warning-orange: #F59E0B;
  --info-blue: #3B82F6;

  /* Light Mode */
  --background: #FFFFFF;
  --surface: #F9FAFB;
  --surface-elevated: #FFFFFF;
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --text-tertiary: #9CA3AF;
  --border: #E5E7EB;
  --border-light: #F3F4F6;
  --border-dark: #D1D5DB;

  /* Gradient Cards */
  --gradient-purple: linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%);
  --gradient-blue: linear-gradient(135deg, #3B82F6 0%, #06B6D4 100%);
  --gradient-green: linear-gradient(135deg, #10B981 0%, #34D399 100%);
  --gradient-orange: linear-gradient(135deg, #F59E0B 0%, #FB923C 100%);

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono: 'Roboto Mono', monospace;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;

  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}

/* Dark mode styles */
.dark,
[data-theme="dark"] {
  /* Dark Mode */
  --background: #0A0A0B;
  --surface: #18181B;
  --surface-elevated: #27272A;
  --text-primary: #F9FAFB;
  --text-secondary: #A1A1AA;
  --text-tertiary: #71717A;
  --border: #27272A;
  --border-light: #18181B;
  --border-dark: #3F3F46;
}

@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    /* Dark Mode (system preference) */
    --background: #0A0A0B;
    --surface: #18181B;
    --surface-elevated: #27272A;
    --text-primary: #F9FAFB;
    --text-secondary: #A1A1AA;
    --text-tertiary: #71717A;
    --border: #27272A;
    --border-light: #18181B;
    --border-dark: #3F3F46;
  }
}

@theme inline {
  /* Base Colors */
  --color-background: var(--background);
  --color-surface: var(--surface);
  --color-surface-elevated: var(--surface-elevated);
  
  /* Primary Colors */
  --color-primary-blue: var(--primary-blue);
  --color-primary-purple: var(--primary-purple);
  
  /* Semantic Colors */
  --color-success-green: var(--success-green);
  --color-error-red: var(--error-red);
  --color-warning-orange: var(--warning-orange);
  --color-info-blue: var(--info-blue);
  
  /* Text Colors */
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-tertiary: var(--text-tertiary);
  
  /* Border Colors */
  --color-border: var(--border);
  --color-border-light: var(--border-light);
  --color-border-dark: var(--border-dark);

  /* Fonts */
  --font-sans: var(--font-primary);
  --font-mono: var(--font-mono);
}

/* Animation Classes */
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slide-up {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-right {
  from { 
    opacity: 0;
    transform: translateX(-20px);
  }
  to { 
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes slide-in-from-bottom {
  from { 
    opacity: 0;
    transform: translateY(24px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-4px); }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out;
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.animate-in {
  animation: slide-in-from-bottom 0.7s ease-out;
}

.fade-in {
  animation: fade-in 0.7s ease-out;
}

.slide-in-from-bottom-4 {
  animation: slide-in-from-bottom 0.7s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Transition Classes */
.transition-all {
  transition: all 0.2s ease-in-out;
}

.transition-colors {
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

.transition-transform {
  transition: transform 0.2s ease-in-out;
}

.transition-shadow {
  transition: box-shadow 0.2s ease-in-out;
}

body {
  background: var(--background);
  color: var(--text-primary);
  font-family: var(--font-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Prevent flash of unstyled content during theme transitions */
.theme-transitioning * {
  transition: none !important;
}

/* Smooth theme transitions */
:root {
  transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out;
}

/* Ensure charts inherit proper theming */
.recharts-wrapper {
  color: var(--text-primary);
}

.recharts-cartesian-grid line {
  stroke: var(--border);
}

.recharts-tooltip-wrapper {
  z-index: 1000;
}
