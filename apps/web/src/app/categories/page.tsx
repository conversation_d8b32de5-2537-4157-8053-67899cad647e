'use client'

import { useState, useEffect } from 'react'
import {
  CategoryService,
  type ICategory,
  type ICategoryForm
} from '@repo/shared'
import { CategoryForm } from '../../components/CategoryForm'
import { CategoryBadge, CategoryIcon, CATEGORY_ICONS } from '../../components/CategoryBadge'
import { Modal } from '@/components/Modal'
import { LoadingState } from '@/components/LoadingState'
import { EmptyState } from '@/components/EmptyState'
import { PageLayout } from '@/components/PageLayout'
import { TabNavigation } from '@/components/TabNavigation'
import { ContentCard } from '@/components/ContentCard'
import { toast } from 'react-hot-toast'
import ProtectedRoute from '@/components/ProtectedRoute'
import { Plus, Tag, Edit2, Trash2 } from 'lucide-react'

export default function CategoriesPage() {
  const [categories, setCategories] = useState<ICategory[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingCategory, setEditingCategory] = useState<ICategory | null>(null)
  const [filter, setFilter] = useState<'all' | 'income' | 'expense' | 'transfer'>('all')

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    try {
      setLoading(true)
      const data = await CategoryService.getCategories({ is_active: true })
      setCategories(data)
    } catch (error) {
      console.error('Failed to load categories:', error)
      toast.error('Failed to load categories')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateCategory = async (data: ICategoryForm) => {
    try {
      await CategoryService.createCategory(data)
      toast.success('Category created successfully!')
      setShowForm(false)
      loadCategories()
    } catch (error: any) {
      console.error('Failed to create category:', error)
      toast.error(error.message || 'Failed to create category')
      throw error
    }
  }

  const handleUpdateCategory = async (data: ICategoryForm) => {
    if (!editingCategory) return
    
    try {
      await CategoryService.updateCategory(editingCategory.id, data)
      toast.success('Category updated successfully!')
      setEditingCategory(null)
      loadCategories()
    } catch (error: any) {
      console.error('Failed to update category:', error)
      toast.error(error.message || 'Failed to update category')
      throw error
    }
  }

  const handleDeleteCategory = async (category: ICategory) => {
    if (!confirm(`Are you sure you want to delete "${category.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      await CategoryService.deleteCategory(category.id)
      toast.success('Category deleted successfully!')
      loadCategories()
    } catch (error: any) {
      console.error('Failed to delete category:', error)
      toast.error(error.message || 'Failed to delete category')
    }
  }

  const filteredCategories = categories.filter(category => {
    if (filter === 'all') return true
    if (filter === 'transfer') {
      // Transfer categories are those without type or with null type (not income/expense)
      return !category.type || (category.type !== 'income' && category.type !== 'expense')
    }
    return category.type === filter
  })

  const userCategories = filteredCategories.filter(cat => !cat.is_system)
  const systemCategories = filteredCategories.filter(cat => cat.is_system)

  const getCategoryIcon = (category: ICategory) => {
    // If icon is a key in our CATEGORY_ICONS array, return the CategoryIcon component
    if (category.icon && CATEGORY_ICONS.includes(category.icon as any)) {
      return (
        <div className="w-6 h-6 text-current">
          <CategoryIcon name={category.icon} />
        </div>
      )
    }
    
    // If icon is an emoji or other string, return as is
    if (category.icon) return category.icon
    
    // Default fallback icons based on type
    if (category.type === 'income') return '💰'
    if (category.type === 'expense') return '💸'
    return '🔄' // Transfer categories
  }

  const pageActions = [{
    label: 'Add Category',
    onClick: () => setShowForm(true),
    variant: 'primary' as const,
    icon: Plus
  }]

  const tabs = [
    { id: 'all', label: 'All Categories', count: categories.length },
    { id: 'expense', label: 'Expense', count: categories.filter(c => c.type === 'expense').length },
    { id: 'income', label: 'Income', count: categories.filter(c => c.type === 'income').length },
    { id: 'transfer', label: 'Transfer', count: categories.filter(c => !c.type || (c.type !== 'income' && c.type !== 'expense')).length }
  ]

  if (loading) {
    return (
      <ProtectedRoute>
        <PageLayout
          title="Categories"
          description="Organize your transactions with custom categories"
          actions={pageActions}
        >
          <LoadingState message="Loading categories..." />
        </PageLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <PageLayout
        title="Categories"
        description="Organize your transactions with custom categories"
        actions={pageActions}
      >
        {/* Filter Tabs */}
        <TabNavigation
          tabs={tabs}
          activeTab={filter}
          onTabChange={(tabId) => setFilter(tabId as typeof filter)}
        />

        {/* User Categories */}
        {userCategories.length > 0 && (
          <ContentCard>
            <h2 className="text-xl font-semibold text-text-primary mb-6">Your Categories</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {userCategories.map((category) => (
                <ContentCard
                  key={category.id}
                  padding="sm"
                  hoverable
                  className="border border-border"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{getCategoryIcon(category)}</div>
                      <div>
                        <h3 className="font-medium text-text-primary">{category.name}</h3>
                        <CategoryBadge
                          name={category.type || 'general'}
                          color={category.color || '#3B82F6'}
                          size="small"
                        />
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setEditingCategory(category)}
                        className="text-text-secondary hover:text-primary-blue transition-colors"
                        title="Edit category"
                      >
                        <Edit2 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteCategory(category)}
                        className="text-text-secondary hover:text-error-red transition-colors"
                        title="Delete category"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  {category.description && (
                    <p className="text-sm text-text-secondary">{category.description}</p>
                  )}
                </ContentCard>
              ))}
            </div>
          </ContentCard>
        )}

        {/* System Categories */}
        {systemCategories.length > 0 && (
          <ContentCard>
            <h2 className="text-xl font-semibold text-text-primary mb-6">System Categories</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {systemCategories.map((category) => (
                <ContentCard
                  key={category.id}
                  padding="sm"
                  className="border border-border opacity-75"
                >
                  <div className="flex items-center gap-3 mb-3">
                    <div className="text-2xl">{getCategoryIcon(category)}</div>
                    <div>
                      <h3 className="font-medium text-text-primary">{category.name}</h3>
                      <CategoryBadge
                        name={category.type || 'system'}
                        color={category.color || '#6B7280'}
                        size="small"
                      />
                    </div>
                  </div>
                  {category.description && (
                    <p className="text-sm text-text-secondary">{category.description}</p>
                  )}
                </ContentCard>
              ))}
            </div>
          </ContentCard>
        )}

        {/* Empty State */}
        {filteredCategories.length === 0 && (
          <EmptyState
            icon={Tag}
            title="No Categories Found"
            description={
              filter === 'all' 
                ? "You don't have any categories yet. Create your first category to get started."
                : `No ${filter} categories found. Try switching to a different filter or create a new ${filter} category.`
            }
            action={{
              label: 'Create Category',
              onClick: () => setShowForm(true)
            }}
          />
        )}

        {/* Create Category Modal */}
        <Modal
          isOpen={showForm}
          onClose={() => setShowForm(false)}
          title="Create Category"
          size="md"
        >
          <CategoryForm
            onSubmit={handleCreateCategory}
          />
        </Modal>

        {/* Edit Category Modal */}
        <Modal
          isOpen={!!editingCategory}
          onClose={() => setEditingCategory(null)}
          title="Edit Category"
          size="md"
        >
          {editingCategory && (
            <CategoryForm
              onSubmit={handleUpdateCategory}
              initialData={editingCategory}
            />
          )}
        </Modal>
      </PageLayout>
    </ProtectedRoute>
  )
}
