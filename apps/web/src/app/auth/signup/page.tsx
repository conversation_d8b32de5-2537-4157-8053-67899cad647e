'use client'

import { useState } from 'react'
// import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useAuth } from '@/contexts/AuthContext'
import { signUpSchema, type SignUpFormData } from '@repo/shared'

export default function SignUpPage() {
  // const router = useRouter() // Commented out as it's not used
  const { signUp } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
  })

  const onSubmit = async (data: SignUpFormData) => {
    try {
      setIsLoading(true)
      setError(null)
      
      const { error } = await signUp(data.email, data.password, data.name)
      
      if (error) {
        setError(error.message)
      } else {
        setSuccess(true)
      }
    } catch (err: unknown) {
      setError('An unexpected error occurred')
      console.error('Sign up error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  if (success) {
    return (
      <div className="space-y-8">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="w-20 h-20 rounded-2xl shadow-lg bg-success-green/10 flex items-center justify-center">
              <svg className="w-12 h-12 text-success-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
          <h2 className="text-3xl font-bold text-text-primary">Check your email</h2>
        </div>
        <div className="bg-success-green/5 border border-success-green/20 rounded-xl p-4">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-success-green/10 rounded-full flex items-center justify-center flex-shrink-0">
              <svg className="w-3 h-3 text-success-green" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="text-sm text-success-green">
              Please check your email for a verification link to complete your account setup.
            </div>
          </div>
        </div>
        <div className="text-center">
          <Link href="/auth/signin" className="text-primary-blue font-medium hover:underline transition-all">
            Back to sign in
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="text-center">
        <div className="flex justify-center mb-6">
          <img 
            src="/portfolio-tracker-icon.svg" 
            alt="Portfolio Tracker" 
            className="w-24 h-24 object-contain"
          />
        </div>
        <h2 className="text-3xl font-bold text-text-primary">Create your account</h2>
        <p className="mt-2 text-text-secondary">
          Already have an account?{' '}
          <Link href="/auth/signin" className="text-primary-blue font-medium hover:underline transition-all">
            Sign in here
          </Link>
        </p>
      </div>

      <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
        {error && (
          <div className="bg-error-red/5 border border-error-red/20 rounded-xl p-4">
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-error-red/10 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-3 h-3 text-error-red" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="text-sm text-error-red">{error}</div>
            </div>
          </div>
        )}

        <div>
          <label htmlFor="name" className="block text-sm font-medium text-text-primary mb-2">
            Full name
          </label>
          <input
            {...register('name')}
            type="text"
            autoComplete="name"
            placeholder="Enter your full name"
            className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base text-text-primary transition-all focus:border-primary-blue focus:outline-none focus:ring-4 focus:ring-primary-blue/10 placeholder:text-text-tertiary"
          />
          {errors.name && (
            <p className="mt-2 text-sm text-error-red flex items-center gap-1">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {errors.name.message}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-text-primary mb-2">
            Email address
          </label>
          <input
            {...register('email')}
            type="email"
            autoComplete="email"
            placeholder="Enter your email"
            className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base text-text-primary transition-all focus:border-primary-blue focus:outline-none focus:ring-4 focus:ring-primary-blue/10 placeholder:text-text-tertiary"
          />
          {errors.email && (
            <p className="mt-2 text-sm text-error-red flex items-center gap-1">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {errors.email.message}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-text-primary mb-2">
            Password
          </label>
          <input
            {...register('password')}
            type="password"
            autoComplete="new-password"
            placeholder="Create a password"
            className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base text-text-primary transition-all focus:border-primary-blue focus:outline-none focus:ring-4 focus:ring-primary-blue/10 placeholder:text-text-tertiary"
          />
          {errors.password && (
            <p className="mt-2 text-sm text-error-red flex items-center gap-1">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {errors.password.message}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium text-text-primary mb-2">
            Confirm password
          </label>
          <input
            {...register('confirmPassword')}
            type="password"
            autoComplete="new-password"
            placeholder="Confirm your password"
            className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base text-text-primary transition-all focus:border-primary-blue focus:outline-none focus:ring-4 focus:ring-primary-blue/10 placeholder:text-text-tertiary"
          />
          {errors.confirmPassword && (
            <p className="mt-2 text-sm text-error-red flex items-center gap-1">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {errors.confirmPassword.message}
            </p>
          )}
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-gradient-to-r from-primary-blue to-primary-purple text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
              Creating account...
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
              </svg>
              Create account
            </>
          )}
        </button>
      </form>
    </div>
  )
}