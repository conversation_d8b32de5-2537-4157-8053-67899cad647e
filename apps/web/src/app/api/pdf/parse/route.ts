import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import { writeFileSync, unlinkSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';
import { CSVParser, type BankStatementData } from '@/../../../../packages/shared/src/lib/pdf-parser';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    
    if (!(file instanceof File)) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }
    
    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }
    
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }
    
    const buffer = Buffer.from(await file.arrayBuffer());
    
    // Create directories for permanent storage
    const projectRoot = join(process.cwd(), '..', '..');
    const statementsDir = join(projectRoot, 'statements');
    const pdfDir = join(statementsDir, 'pdf');
    const csvDir = join(statementsDir, 'csv');
    
    // Ensure directories exist
    if (!existsSync(statementsDir)) mkdirSync(statementsDir, { recursive: true });
    if (!existsSync(pdfDir)) mkdirSync(pdfDir, { recursive: true });
    if (!existsSync(csvDir)) mkdirSync(csvDir, { recursive: true });
    
    // Generate unique filename with timestamp and original name
    const timestamp = Date.now();
    const originalName = file.name.replace(/\.pdf$/i, '');
    const baseFileName = `${originalName}_${timestamp}`;
    
    // Save PDF permanently
    const permanentPdfPath = join(pdfDir, `${baseFileName}.pdf`);
    writeFileSync(permanentPdfPath, buffer);
    console.log(`PDF saved to: ${permanentPdfPath}`);
    
    // Create temporary file for processing (Python script needs a temp path)
    const tempPdfPath = join(tmpdir(), `upload_${timestamp}.pdf`);
    writeFileSync(tempPdfPath, buffer);
    
    try {
      // Path to the Python script (go up two levels from apps/web to project root)
      const scriptPath = join(process.cwd(), '..', '..', 'enhanced_bank_parser_fixed.py');
      
      // Debug: Check if script exists
      console.log('Current working directory:', process.cwd());
      console.log('Script path:', scriptPath);
      console.log('Script exists:', existsSync(scriptPath));
      
      if (!existsSync(scriptPath)) {
        throw new Error(`Python script not found at: ${scriptPath}`);
      }
      
      // Execute Python script
      const parsedData = await new Promise<BankStatementData & { savedFiles: { pdf: string; csv: string }; rawCsvContent: string }>((resolve, reject) => {
        const python = spawn('python3', [scriptPath, tempPdfPath]);
        
        let stdout = '';
        let stderr = '';
        
        python.stdout.on('data', (data) => {
          stdout += data.toString();
        });
        
        python.stderr.on('data', (data) => {
          stderr += data.toString();
        });
        
        python.on('close', (code) => {
          console.log('Python script output:', stdout);
          if (stderr) {
            console.warn('Python script stderr:', stderr);
          }
          
          if (code !== 0) {
            reject(new Error(`Python script failed with code ${code}: ${stderr || 'Unknown error'}`));
            return;
          }
          
          try {
            // Look for CSV output file - try multiple patterns
            const tempBaseFileName = tempPdfPath.replace(/\.[^/.]+$/, '');
            const possibleCsvPaths = [
              `${tempBaseFileName}.csv`,
              `transactions_${tempBaseFileName.split('/').pop()}.csv`,
              join(process.cwd(), '..', '..', `transactions_${tempBaseFileName.split('/').pop()}.csv`)
            ];
            
            let csvContent = '';
            let tempCsvPath = '';
            
            for (const csvPath of possibleCsvPaths) {
              if (existsSync(csvPath)) {
                csvContent = readFileSync(csvPath, 'utf-8');
                tempCsvPath = csvPath;
                console.log(`Found CSV file at: ${csvPath}`);
                break;
              }
            }
            
            if (!csvContent) {
              console.log('No CSV file found. Tried paths:', possibleCsvPaths);
              console.log('Python script stdout:', stdout);
            }
            
            if (csvContent) {
              // Use the new CSV parser utility
              const bankStatementData = CSVParser.parseCSVToBankStatementData(csvContent);
              
              // Save CSV permanently with matching name
              const permanentCsvPath = join(csvDir, `${baseFileName}.csv`);
              writeFileSync(permanentCsvPath, csvContent);
              console.log(`CSV saved to: ${permanentCsvPath}`);
              
              // Clean up temporary files only
              if (tempCsvPath) {
                unlinkSync(tempCsvPath);
              }
              
              resolve({ 
                ...bankStatementData,
                savedFiles: {
                  pdf: permanentPdfPath,
                  csv: permanentCsvPath
                },
                rawCsvContent: csvContent // Include raw CSV content for download
              });
            } else {
              resolve({ transactions: [] });
            }
          } catch (parseError) {
            reject(new Error(`Failed to parse output: ${parseError}`));
          }
        });
        
        python.on('error', (error) => {
          reject(new Error(`Failed to start Python process: ${error.message}`));
        });
      });
      
      return NextResponse.json({
        success: true,
        data: parsedData
      });
      
    } finally {
      // Clean up temporary PDF file only (keep permanent files)
      try {
        unlinkSync(tempPdfPath);
      } catch (cleanupError) {
        console.warn('Failed to clean up temp file:', cleanupError);
      }
    }
    
  } catch (error) {
    console.error('PDF parsing error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to parse PDF',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
