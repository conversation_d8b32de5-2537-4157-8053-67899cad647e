'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import {
  AccountService,
  TransactionService,
  TransferService,
  type IAccount,
  useCurrencyStore
} from '@repo/shared'
import { toast } from 'react-hot-toast'
import ProtectedRoute from '@/components/ProtectedRoute'
import TransactionListWeb from '../../../components/TransactionList'
import { TabbedTransactionForm } from '../../../components/TabbedTransactionForm'
import { Modal } from '../../../components/Modal'

export default function AccountDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { formatCurrency } = useCurrencyStore()
  const [account, setAccount] = useState<IAccount | null>(null)
  const [loading, setLoading] = useState(true)
  const [showTransactionForm, setShowTransactionForm] = useState(false)
  const [refreshKey, setRefresh<PERSON><PERSON>] = useState(0)
  const [editingTransaction, setEditingTransaction] = useState<any>(null)

  const accountId = params.id as string

  useEffect(() => {
    if (accountId) {
      loadAccountDetails()
    }
  }, [accountId])

  const loadAccountDetails = async () => {
    try {
      setLoading(true)
      const accountData = await AccountService.getAccount(accountId)
      if (!accountData) {
        toast.error('Account not found')
        router.push('/accounts')
        return
      }
      setAccount(accountData)
    } catch (error) {
      console.error('Failed to load account details:', error)
      toast.error('Failed to load account details')
      router.push('/accounts')
    } finally {
      setLoading(false)
    }
  }



  const handleTransactionSubmit = async (data: any) => {
    try {
      if (editingTransaction) {
        await TransactionService.updateTransaction(editingTransaction.id, data)
        toast.success('Transaction updated successfully!')
      } else {
        // Route to appropriate service based on transaction type
        if (data.transaction_type === 'transfer') {
          // Transform data for transfer service
          const transferData = {
            from_account_id: data.account_id!, // Form uses account_id for from account
            to_account_id: data.to_account_id!,
            amount: data.amount,
            description: data.description || '',
            transaction_date: data.transaction_date,
            fees: data.fees,
            category_id: data.category_id || null
          }
          await TransferService.createTransfer(transferData)
          toast.success('Transfer created successfully!')
        } else {
          await TransactionService.createComprehensiveTransaction(data)
          toast.success('Transaction added successfully!')
        }
      }
      setShowTransactionForm(false)
      setEditingTransaction(null)
      setRefreshKey(prev => prev + 1)
      // Reload account details to update balance
      await loadAccountDetails()
    } catch (error) {
      console.error('Failed to create/update transaction:', error)
      toast.error(editingTransaction ? 'Failed to update transaction' : 'Failed to add transaction')
      throw error // Re-throw to let the form handle the error state
    }
  }

  const getAccountTypeIcon = (type: string) => {
    switch (type) {
      case 'bank':
        return '🏦'
      case 'investment':
        return '📈'
      case 'savings':
        return '💰'
      case 'credit_card':
        return '💳'
      case 'cash':
        return '💵'

      default:
        return '🏛️'
    }
  }

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'bank':
        return 'bg-blue-100 text-blue-800'
      case 'investment':
        return 'bg-green-100 text-green-800'
      case 'savings':
        return 'bg-purple-100 text-purple-800'
      case 'credit_card':
        return 'bg-red-100 text-red-800'
      case 'cash':
        return 'bg-yellow-100 text-yellow-800'

      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading && !account) {
    return (
      <ProtectedRoute>
        <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-4 border-border border-t-primary-blue"></div>
          </div>
        </main>
      </ProtectedRoute>
    )
  }

  if (!account) {
    return null
  }

  return (
    <ProtectedRoute>
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Back Button */}
          <button
            onClick={() => router.push('/accounts')}
            className="flex items-center gap-2 text-text-secondary hover:text-primary-blue transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Accounts
          </button>

          {/* Account Header */}
          <div className="bg-surface-elevated border border-border-light rounded-xl p-6 shadow-sm">
            <div className="flex items-start justify-between mb-6">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gradient-to-br from-primary-blue/10 to-primary-purple/10 rounded-xl flex items-center justify-center border border-border-light">
                  <span className="text-3xl">{getAccountTypeIcon(account.account_type)}</span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-text-primary">{account.name}</h1>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getAccountTypeColor(account.account_type)} mt-1`}>
                    {account.account_type.replace('_', ' ')}
                  </span>
                </div>
              </div>
              <button
                onClick={() => setShowTransactionForm(true)}
                className="bg-gradient-to-r from-primary-blue to-primary-purple text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5"
              >
                Add Transaction
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-2">
                <span className="text-sm text-text-secondary font-medium">Current Balance</span>
                <div className="text-3xl font-bold text-text-primary">
                  {formatCurrency(account.current_balance || 0)}
                </div>
              </div>
              
              {account.institution_name && (
                <div className="space-y-2">
                  <span className="text-sm text-text-secondary font-medium">Institution</span>
                  <div className="text-lg font-semibold text-text-primary">{account.institution_name}</div>
                </div>
              )}
              
              {account.account_number && (
                <div className="space-y-2">
                  <span className="text-sm text-text-secondary font-medium">Account Number</span>
                  <div className="text-lg font-semibold text-text-primary font-mono">
                    ****{account.account_number.slice(-4)}
                  </div>
                </div>
              )}
              
              <div className="space-y-2">
                <span className="text-sm text-text-secondary font-medium">Status</span>
                <div>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    account.is_active
                      ? 'bg-success-green/10 text-success-green border border-success-green/20'
                      : 'bg-error-red/10 text-error-red border border-error-red/20'
                  }`}>
                    {account.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Transactions Section */}
          <div className="bg-surface-elevated border border-border-light rounded-xl shadow-sm">
            <TransactionListWeb
              accountId={accountId}
              hideFilters={true}
              refreshTrigger={refreshKey}
              onEditTransaction={(transaction) => {
                setEditingTransaction(transaction)
                setShowTransactionForm(true)
              }}
              onTransactionDeleted={loadAccountDetails}
            />
          </div>

          {/* Transaction Form Modal */}
          <Modal
            isOpen={showTransactionForm}
            onClose={() => {
              setShowTransactionForm(false)
              setEditingTransaction(null)
            }}
            title={editingTransaction ? "Edit Transaction" : "Add Transaction"}
            size="lg"
          >
            <TabbedTransactionForm
              onSubmit={handleTransactionSubmit}
              initialData={editingTransaction || { account_id: accountId }}
              isEditing={!!editingTransaction}
            />
          </Modal>
        </div>
      </main>
    </ProtectedRoute>
  )
}