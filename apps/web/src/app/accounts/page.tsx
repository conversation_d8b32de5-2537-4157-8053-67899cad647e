'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  AccountService,
  type IAccount,
  type IAccountForm,
  type AccountType,
  useCurrencyStore
} from '@shared/index'
import { Modal } from '@/components/Modal'
import { LoadingState } from '@/components/LoadingState'
import { EmptyState } from '@/components/EmptyState'
import { PageLayout } from '@/components/PageLayout'
import { TabNavigation } from '@/components/TabNavigation'
import { DraggableAccountCard } from '@/components/DraggableAccountCard'
import { toast } from 'react-hot-toast'
import ProtectedRoute from '@/components/ProtectedRoute'
import { AccountForm } from '../../components/AccountForm'
import { Plus, Building2, TrendingUp, Wallet, DollarSign, Home, CreditCard } from 'lucide-react'
import { 
  DndContext, 
  closestCenter, 
  KeyboardSensor, 
  PointerSensor, 
  useSensor, 
  useSensors,
  DragEndEvent,
  DragOverlay,
  DragStartEvent
} from '@dnd-kit/core'
import { 
  arrayMove, 
  SortableContext, 
  sortableKeyboardCoordinates, 
  rectSortingStrategy 
} from '@dnd-kit/sortable'

export default function AccountsPage() {
  const router = useRouter()
  const { formatCurrency } = useCurrencyStore()
  const [accounts, setAccounts] = useState<IAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [showAccountForm, setShowAccountForm] = useState(false)
  const [editingAccount, setEditingAccount] = useState<IAccount | null>(null)
  const [submitting, setSubmitting] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)
  const [activeId, setActiveId] = useState<string | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [selectedAccountType, setSelectedAccountType] = useState<string>('')

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
        tolerance: 5,
        delay: 100,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  useEffect(() => {
    loadAccounts()
  }, [refreshKey])

  const loadAccounts = async () => {
    try {
      setLoading(true)
      const allAccounts = await AccountService.getAccounts()
      setAccounts(allAccounts)
    } catch (error) {
      console.error('Failed to load accounts:', error)
      toast.error('Failed to load accounts')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateAccount = () => {
    setEditingAccount(null)
    setShowAccountForm(true)
  }

  const handleEditAccount = (account: IAccount) => {
    setEditingAccount(account)
    setShowAccountForm(true)
  }

  const handleAccountSubmit = async (data: IAccountForm) => {
    try {
      setSubmitting(true)
      
      if (editingAccount) {
        await AccountService.updateAccount(editingAccount.id, data)
        toast.success('Account updated successfully!')
      } else {
        await AccountService.createAccount(data)
        toast.success('Account created successfully!')
      }
      
      setShowAccountForm(false)
      setEditingAccount(null)
      setRefreshKey(prev => prev + 1)
    } catch (error) {
      console.error('Failed to save account:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to save account')
    } finally {
      setSubmitting(false)
    }
  }

  const handleDeleteAccount = async (account: IAccount) => {
    if (!confirm(`Are you sure you want to delete "${account.name}"?`)) {
      return
    }

    try {
      await AccountService.deleteAccount(account.id)
      toast.success('Account deleted successfully!')
      setRefreshKey(prev => prev + 1)
    } catch (error) {
      console.error('Failed to delete account:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to delete account')
    }
  }

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string)
    setIsDragging(true)
    
    // Add subtle vibration on mobile devices
    if (typeof window !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate(50)
    }
  }

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event

    if (!over || active.id === over.id) {
      setActiveId(null)
      setIsDragging(false)
      return
    }

    // Find the accounts being reordered
    const activeAccount = accounts.find(acc => acc.id === active.id)
    const overAccount = accounts.find(acc => acc.id === over.id)

    if (!activeAccount || !overAccount || activeAccount.account_type !== overAccount.account_type) {
      setActiveId(null)
      setIsDragging(false)
      return
    }

    // Get accounts of the same type, sorted by display_order
    const sameTypeAccounts = accounts
      .filter(acc => acc.account_type === activeAccount.account_type)
      .sort((a, b) => (a.display_order || 0) - (b.display_order || 0))
    const activeIndex = sameTypeAccounts.findIndex(acc => acc.id === active.id)
    const overIndex = sameTypeAccounts.findIndex(acc => acc.id === over.id)

    if (activeIndex === overIndex) {
      setActiveId(null)
      setIsDragging(false)
      return
    }

    // Reorder the accounts of the same type
    const reorderedAccounts = arrayMove(sameTypeAccounts, activeIndex, overIndex)
    
    // Update display order for all accounts in this type
    const updates = reorderedAccounts.map((account, index) => ({
      id: account.id,
      display_order: index + 1,
      account_type: account.account_type as AccountType
    }))

    // Optimistically update the UI
    const updatedAllAccounts = accounts.map(account => {
      const update = updates.find(u => u.id === account.id)
      return update ? { ...account, display_order: update.display_order } : account
    })
    setAccounts(updatedAllAccounts)

    // Delay clearing drag state to allow smooth visual transition
    setTimeout(() => {
      setActiveId(null)
      setIsDragging(false)
    }, 150)

    try {
      await AccountService.reorderAccounts(updates)
      toast.success('Account order updated!')
      
      // Success vibration
      if (typeof window !== 'undefined' && 'vibrate' in navigator) {
        navigator.vibrate([100, 50, 100])
      }
    } catch (error) {
      console.error('Failed to reorder accounts:', error)
      toast.error('Failed to update account order')
      // Revert the optimistic update
      setRefreshKey(prev => prev + 1)
      
      // Clear drag state immediately on error
      setActiveId(null)
      setIsDragging(false)
      
      // Error vibration
      if (typeof window !== 'undefined' && 'vibrate' in navigator) {
        navigator.vibrate([200, 100, 200])
      }
    }
  }

  const getAccountTypeIcon = (type: string) => {
    switch (type) {
      case 'bank':
        return Building2
      case 'investment':
        return TrendingUp
      case 'savings':
        return DollarSign
      case 'credit_card':
        return CreditCard
      case 'cash':
        return Wallet
      case 'loan':
        return Home
      default:
        return Building2
    }
  }


  const groupedAccounts = accounts.reduce((groups, account) => {
    const type = account.account_type
    if (!groups[type]) {
      groups[type] = []
    }
    groups[type].push(account)
    return groups
  }, {} as Record<string, IAccount[]>)

  // Sort accounts within each group by display_order
  Object.keys(groupedAccounts).forEach(type => {
    groupedAccounts[type].sort((a, b) => (a.display_order || 0) - (b.display_order || 0))
  })

  // Create tabs for navigation and set default selected type
  const accountTypes = Object.keys(groupedAccounts)
  const tabs = accountTypes.map(type => ({
    id: type,
    label: type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    count: groupedAccounts[type].length
  }))

  // Set default selected type if not set
  if (!selectedAccountType && accountTypes.length > 0) {
    setSelectedAccountType(accountTypes[0])
  }

  // Get filtered accounts based on selected tab
  const filteredGroupedAccounts = selectedAccountType 
    ? { [selectedAccountType]: groupedAccounts[selectedAccountType] || [] }
    : {}

  const pageActions = [{
    label: 'Add Account',
    onClick: handleCreateAccount,
    variant: 'primary' as const,
    icon: Plus
  }]

  if (loading) {
    return (
      <ProtectedRoute>
        <PageLayout
          title="Accounts"
          description="Manage your financial accounts"
          actions={pageActions}
        >
          <LoadingState message="Loading accounts..." />
        </PageLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <PageLayout
        title="Accounts"
        description="Manage your financial accounts"
        actions={pageActions}
      >
        {/* Tab Navigation */}
        <TabNavigation
          tabs={tabs}
          activeTab={selectedAccountType}
          onTabChange={setSelectedAccountType}
        />

        {/* Accounts Grid */}
        {!selectedAccountType || Object.keys(filteredGroupedAccounts).length === 0 || 
         (filteredGroupedAccounts[selectedAccountType] && filteredGroupedAccounts[selectedAccountType].length === 0) ? (
          <EmptyState
            icon={Building2}
            title={!selectedAccountType ? 'No Accounts Yet' : `No ${selectedAccountType.replace('_', ' ')} Accounts`}
            description={
              !selectedAccountType 
                ? "Create your first account to start tracking your finances."
                : `You don't have any ${selectedAccountType.replace('_', ' ')} accounts yet. Create one to get started.`
            }
            action={{
              label: 'Create Account',
              onClick: handleCreateAccount
            }}
          />
        ) : (
          <div className={`
            space-y-8 transition-all duration-300
            ${isDragging ? 'opacity-95' : 'opacity-100'}
          `}>
            {Object.entries(filteredGroupedAccounts).map(([type, typeAccounts]) => {
              return (
                <div key={type} className="space-y-4">
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragStart={handleDragStart}
                    onDragEnd={handleDragEnd}
                  >
                    <SortableContext 
                      items={typeAccounts.map(acc => acc.id)} 
                      strategy={rectSortingStrategy}
                    >
                      <div className={`
                        grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6
                        ${isDragging ? 'bg-primary-blue/5 rounded-lg p-2 border border-dashed border-primary-blue/30' : ''}
                        transition-all duration-200 ease-out
                      `}>
                        {typeAccounts.map((account) => (
                          <DraggableAccountCard
                            key={account.id}
                            account={account}
                            onEdit={handleEditAccount}
                            onDelete={handleDeleteAccount}
                          />
                        ))}
                      </div>
                    </SortableContext>
                    
                    <DragOverlay>
                      {activeId ? (
                        <div className={`
                          rotate-1 scale-105 transform-gpu cursor-grabbing
                          transition-all duration-150 ease-out
                          ${!isDragging ? 'opacity-0 scale-100' : 'opacity-100'}
                        `}>
                          <div className="relative w-80">
                            {/* Enhanced shadow */}
                            <div className="absolute inset-0 bg-black/20 rounded-xl translate-y-2 translate-x-1 blur-md" />
                            
                            {/* Card */}
                            <div className="relative ring-2 ring-primary-blue/30 rounded-xl">
                              <DraggableAccountCard
                                account={accounts.find(acc => acc.id === activeId)!}
                                onEdit={() => {}}
                                onDelete={() => {}}
                              />
                            </div>
                          </div>
                        </div>
                      ) : null}
                    </DragOverlay>
                  </DndContext>
                </div>
              )
            })}
          </div>
        )}

        {/* Account Form Modal */}
        <Modal
          isOpen={showAccountForm}
          onClose={() => {
            setShowAccountForm(false)
            setEditingAccount(null)
          }}
          title={editingAccount ? 'Edit Account' : 'Create Account'}
          size="lg"
        >
          <AccountForm
            onSubmit={handleAccountSubmit}
            loading={submitting}
            initialData={editingAccount}
          />
        </Modal>
      </PageLayout>
    </ProtectedRoute>
  )
}
