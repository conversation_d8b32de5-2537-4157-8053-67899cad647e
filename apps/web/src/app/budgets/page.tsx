'use client'

import { useState } from 'react'
import { BudgetDashboard } from '../../components/BudgetDashboard'
import { PageLayout } from '../../components/PageLayout'
import ProtectedRoute from '@/components/ProtectedRoute'
import { Plus } from 'lucide-react'

export default function BudgetsPage() {
  const [showForm, setShowForm] = useState(false)

  const pageActions = [{
    label: 'Create Budget',
    onClick: () => setShowForm(true),
    variant: 'primary' as const,
    icon: Plus
  }]

  return (
    <ProtectedRoute>
      <PageLayout
        title="Budgets"
        description="Set and track your spending limits by category"
        actions={pageActions}
      >
        <BudgetDashboard showForm={showForm} setShowForm={setShowForm} />
      </PageLayout>
    </ProtectedRoute>
  )
}