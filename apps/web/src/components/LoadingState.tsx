'use client';

import React from 'react';

interface LoadingStateProps {
  variant?: 'spinner' | 'skeleton' | 'fullscreen';
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  height?: string;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  variant = 'spinner',
  message = 'Loading...',
  size = 'lg',
  className = '',
  height
}) => {
  const sizeClasses = {
    sm: 'py-8',
    md: 'py-16', 
    lg: 'py-24'
  };

  const spinnerSizes = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  if (variant === 'fullscreen') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-border border-t-primary-blue"></div>
          <p className="mt-4 text-text-secondary text-sm">{message}</p>
        </div>
      </div>
    );
  }

  if (variant === 'skeleton') {
    return (
      <div className={`animate-pulse ${className}`} style={height ? { height } : undefined}>
        <div className="bg-gray-200 dark:bg-gray-700 rounded" style={height ? { height } : { height: '48px' }}></div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col items-center justify-center ${sizeClasses[size]} ${className}`}>
      <div className={`animate-spin rounded-full border-4 border-border border-t-primary-blue ${spinnerSizes[size]}`}></div>
      <p className="mt-4 text-text-secondary text-sm">
        {message}
      </p>
    </div>
  );
};

export default LoadingState;