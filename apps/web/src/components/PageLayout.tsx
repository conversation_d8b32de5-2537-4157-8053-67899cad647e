'use client';

import React from 'react';

interface PageHeaderAction {
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
  icon?: React.ComponentType<{ className?: string }>;
}

interface PageLayoutProps {
  title: string;
  description?: string;
  actions?: PageHeaderAction[];
  children: React.ReactNode;
  className?: string;
}

export const PageLayout: React.FC<PageLayoutProps> = ({
  title,
  description,
  actions = [],
  children,
  className = ''
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-surface/30 to-background">
      {/* Container with proper spacing and max-width */}
      <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-10 ${className}`}>
        {/* Page Header with enhanced styling */}
        <div className="mb-8 sm:mb-10">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 sm:gap-6">
            <div className="space-y-2">
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-text-primary via-primary-blue to-primary-purple bg-clip-text text-transparent leading-tight">
                {title}
              </h1>
              {description && (
                <p className="text-lg text-text-secondary max-w-2xl leading-relaxed">
                  {description}
                </p>
              )}
            </div>
            
            {actions.length > 0 && (
              <div className="flex flex-wrap items-center gap-3">
                {actions.map((action, index) => {
                  const Icon = action.icon;
                  return (
                    <button
                      key={index}
                      onClick={action.onClick}
                      className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 ${
                        action.variant === 'secondary'
                          ? 'bg-surface-elevated text-text-primary border-2 border-border hover:border-primary-blue hover:text-primary-blue hover:shadow-lg backdrop-blur-sm'
                          : 'bg-gradient-to-r from-primary-blue to-primary-purple text-white shadow-lg hover:shadow-xl hover:-translate-y-1 ring-2 ring-primary-blue/20 hover:ring-primary-blue/40'
                      }`}
                    >
                      {Icon && <Icon className="w-5 h-5" />}
                      {action.label}
                    </button>
                  );
                })}
              </div>
            )}
          </div>
          
          {/* Decorative line */}
          <div className="mt-6 h-px bg-gradient-to-r from-transparent via-border to-transparent"></div>
        </div>
        
        {/* Page Content with enhanced container */}
        <div className="space-y-6 sm:space-y-8">
          <div className="animate-in fade-in duration-700 slide-in-from-bottom-4">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PageLayout;