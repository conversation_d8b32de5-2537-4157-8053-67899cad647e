'use client'

import { useState, useEffect } from 'react'
import { useTheme } from '../contexts/ThemeContext'

export default function ThemeToggleButton() {
  const { theme, resolvedTheme, setTheme } = useTheme()
  const [isClient, setIsClient] = useState(false)

  // Prevent hydration mismatch by only rendering on client
  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleToggle = () => {
    if (resolvedTheme === 'light') {
      setTheme('dark')
    } else {
      setTheme('light')
    }
  }

  // Show neutral state until hydrated to prevent theme mismatch
  if (!isClient) {
    return (
      <button
        className="relative w-10 h-10 rounded-lg bg-surface hover:bg-surface-elevated border border-border-light hover:border-border transition-all duration-200 flex items-center justify-center group"
        aria-label="Theme toggle"
      >
        <div className="w-5 h-5 rounded-full bg-text-tertiary opacity-30" />
      </button>
    )
  }

  return (
    <button
      onClick={handleToggle}
      className="relative w-10 h-10 rounded-lg bg-surface hover:bg-surface-elevated border border-border-light hover:border-border transition-all duration-200 flex items-center justify-center group"
      aria-label={`Switch to ${resolvedTheme === 'light' ? 'dark' : 'light'} mode`}
    >
      {/* Sun Icon */}
      <svg
        className={`absolute w-5 h-5 text-warning-orange transition-all duration-300 ${
          resolvedTheme === 'light' 
            ? 'opacity-100 rotate-0 scale-100' 
            : 'opacity-0 rotate-90 scale-75'
        }`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path
          fillRule="evenodd"
          d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
          clipRule="evenodd"
        />
      </svg>

      {/* Moon Icon */}
      <svg
        className={`absolute w-5 h-5 text-primary-purple transition-all duration-300 ${
          resolvedTheme === 'dark' 
            ? 'opacity-100 rotate-0 scale-100' 
            : 'opacity-0 -rotate-90 scale-75'
        }`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
      </svg>

      {/* Subtle background glow effect */}
      <div className={`absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${
        resolvedTheme === 'light' 
          ? 'bg-warning-orange/5' 
          : 'bg-primary-purple/5'
      }`} />
    </button>
  )
}