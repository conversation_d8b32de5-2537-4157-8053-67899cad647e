import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { budgetFormInputSchema, type BudgetFormInputData, BudgetService, TransactionService, type IBudget, type ICategory } from '@repo/shared'

interface BudgetFormProps {
  budget?: IBudget
  onSuccess?: (budget: IBudget) => void
  onCancel?: () => void
}

export const BudgetForm: React.FC<BudgetFormProps> = ({
  budget,
  onSuccess,
  onCancel,
}) => {
  const [categories, setCategories] = useState<ICategory[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<BudgetFormInputData>({
    resolver: zodResolver(budgetFormInputSchema),
    defaultValues: {
      name: budget?.name || '',
      amount: budget?.amount?.toString() || '',
      period: budget?.period || 'monthly',
      category_id: budget?.category_id || '',
      start_date: budget?.start_date ? new Date(budget.start_date) : new Date(),
      end_date: budget?.end_date ? new Date(budget.end_date) : null,
    },
  })

  const selectedPeriod = watch('period')

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    try {
      const fetchedCategories = await TransactionService.getCategories()
      setCategories(fetchedCategories.filter(cat => cat.type === 'expense'))
    } catch (err) {
      console.error('Failed to load categories:', err)
    }
  }

  const onSubmit = async (data: BudgetFormInputData) => {
    setLoading(true)
    setError(null)

    try {
      // Transform the data
      const transformedData = {
        name: data.name,
        amount: parseFloat(data.amount.replace(/[^\d.-]/g, '')),
        period: data.period,
        category_id: data.category_id || undefined,
        start_date: data.start_date,
        end_date: data.end_date,
      }

      let savedBudget: IBudget
      
      if (budget) {
        savedBudget = await BudgetService.updateBudget(budget.id, transformedData)
      } else {
        savedBudget = await BudgetService.createBudget(transformedData)
      }

      reset()
      onSuccess?.(savedBudget)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save budget')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    reset()
    onCancel?.()
  }

  return (
    <div className="bg-surface-elevated rounded-xl border border-border-light p-6 shadow-sm hover:shadow-md transition-all duration-200">
      <h2 className="text-xl font-semibold mb-6 text-text-primary">
        {budget ? 'Edit Budget' : 'Create New Budget'}
      </h2>

      {error && (
        <div className="mb-6 p-4 bg-error-red/5 border border-error-red/20 text-error-red rounded-lg">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-semibold text-text-secondary mb-2">
            Budget Name
          </label>
          <input
            type="text"
            id="name"
            {...register('name')}
            className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base text-text-primary transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20"
            placeholder="e.g., Monthly Groceries"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-error-red">{errors.name.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="amount" className="block text-sm font-semibold text-text-secondary mb-2">
            Budget Amount
          </label>
          <input
            type="text"
            id="amount"
            {...register('amount')}
            className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base text-text-primary transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20"
            placeholder="0.00"
          />
          {errors.amount && (
            <p className="mt-1 text-sm text-error-red">{errors.amount.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="period" className="block text-sm font-semibold text-text-secondary mb-2">
            Budget Period
          </label>
          <select
            id="period"
            {...register('period')}
            className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-text-primary appearance-none cursor-pointer transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20"
          >
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
          </select>
          {errors.period && (
            <p className="mt-1 text-sm text-error-red">{errors.period.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="category_id" className="block text-sm font-semibold text-text-secondary mb-2">
            Category (Optional)
          </label>
          <select
            id="category_id"
            {...register('category_id')}
            className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-text-primary appearance-none cursor-pointer transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20"
          >
            <option value="">All Categories</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.icon} {category.name}
              </option>
            ))}
          </select>
          {errors.category_id && (
            <p className="mt-1 text-sm text-error-red">{errors.category_id.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="start_date" className="block text-sm font-semibold text-text-secondary mb-2">
            Start Date
          </label>
          <input
            type="date"
            id="start_date"
            {...register('start_date', { 
              valueAsDate: true,
              setValueAs: (value) => value === '' ? new Date() : new Date(value)
            })}
            className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base text-text-primary transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20"
          />
          {errors.start_date && (
            <p className="mt-1 text-sm text-error-red">{errors.start_date.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="end_date" className="block text-sm font-semibold text-text-secondary mb-2">
            End Date (Optional)
          </label>
          <input
            type="date"
            id="end_date"
            {...register('end_date', { 
              setValueAs: (value) => {
                if (value === '' || !value) return undefined
                const date = new Date(value)
                return isNaN(date.getTime()) ? undefined : date
              }
            })}
            className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base text-text-primary transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20"
          />
          <p className="mt-1 text-sm text-text-tertiary">
            Leave empty for recurring {selectedPeriod} budget
          </p>
          {errors.end_date && (
            <p className="mt-1 text-sm text-error-red">{errors.end_date.message}</p>
          )}
        </div>

        <div className="flex justify-end space-x-3 pt-6">
          <button
            type="button"
            onClick={handleCancel}
            className="bg-surface text-text-secondary border border-border px-6 py-3 rounded-lg font-medium hover:bg-surface-elevated transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-gradient-to-r from-primary-blue to-primary-purple text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {loading ? 'Saving...' : budget ? 'Update Budget' : 'Create Budget'}
          </button>
        </div>
      </form>
    </div>
  )
}