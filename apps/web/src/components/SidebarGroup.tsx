'use client'

import { ChevronDown, ChevronRight } from 'lucide-react'

interface SidebarGroupProps {
  label: string
  icon: React.ReactNode
  isExpanded: boolean
  isCollapsed: boolean
  onToggle: () => void
  children: React.ReactNode
}

export default function SidebarGroup({
  label,
  icon,
  isExpanded,
  isCollapsed,
  onToggle,
  children
}: SidebarGroupProps) {
  return (
    <div className="space-y-1">
      {/* Group Header - Hidden when collapsed */}
      {!isCollapsed && (
        <button
          onClick={onToggle}
          className="w-full flex items-center px-3 py-2 rounded-lg text-left transition-all duration-200 text-text-secondary hover:text-text-primary hover:bg-surface"
        >
          <div className="flex items-center gap-3 min-w-0 flex-1">
            {icon}
            <span className="text-sm font-medium uppercase tracking-wide truncate">
              {label}
            </span>
          </div>
          
          <div className="ml-2 flex-shrink-0 transition-transform duration-200">
            {isExpanded ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </div>
        </button>
      )}

      {/* Group Items */}
      <div className={`
        overflow-hidden transition-all duration-300 ease-in-out
        ${isExpanded || isCollapsed ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}
        ${isCollapsed ? 'space-y-1' : 'ml-3 space-y-1'}
      `}>
        {(isExpanded || isCollapsed) && children}
      </div>
    </div>
  )
}