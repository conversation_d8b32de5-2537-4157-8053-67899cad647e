'use client'

import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { ChevronLeft, ChevronRight, LayoutDashboard, BarChart3, Receipt, FileText, Upload, PiggyBank, TrendingUp, Target, CreditCard, Tag, User, X } from 'lucide-react'
import SidebarItem from './SidebarItem'
import ThemeToggleButton from './ThemeToggleButton'
import AccountDropdown from './AccountDropdown'
import { useSidebar } from '../hooks/useSidebar'

interface SidebarProps {
  isMobileOpen: boolean
  onMobileClose: () => void
}

interface NavigationItem {
  href: string
  label: string
  icon: React.ReactNode
  key: string
}


export default function Sidebar({ isMobileOpen, onMobileClose }: SidebarProps) {
  const pathname = usePathname()
  const { isCollapsed, setIsCollapsed } = useSidebar()

  // Navigation configuration
  const standaloneItems: NavigationItem[] = [
    {
      href: '/dashboard',
      label: 'Dashboard',
      key: 'dashboard',
      icon: <LayoutDashboard className="w-5 h-5" />
    }
  ]

  const allNavigationItems: NavigationItem[] = [
    {
      href: '/accounts',
      label: 'Accounts',
      key: 'accounts',
      icon: <CreditCard className="w-5 h-5" />
    },
    {
      href: '/transactions',
      label: 'Transactions',
      key: 'transactions',
      icon: <Receipt className="w-5 h-5" />
    },
    {
      href: '/templates',
      label: 'Templates',
      key: 'templates',
      icon: <FileText className="w-5 h-5" />
    },
    {
      href: '/budgets',
      label: 'Budgets',
      key: 'budgets',
      icon: <PiggyBank className="w-5 h-5" />
    },
    {
      href: '/investments',
      label: 'Investments',
      key: 'investments',
      icon: <TrendingUp className="w-5 h-5" />
    },
    {
      href: '/categories',
      label: 'Categories',
      key: 'categories',
      icon: <Tag className="w-5 h-5" />
    }
  ]

  // Remove group-related logic since we're showing all items directly

  const getCurrentPage = () => {
    const currentPath = pathname.replace('/', '') || 'dashboard'
    return currentPath
  }

  const sidebarWidth = isCollapsed ? 'w-16' : 'w-64'

  return (
    <>
      {/* Mobile Backdrop */}
      {isMobileOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onMobileClose}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed top-0 left-0 h-full bg-surface-elevated border-r border-border-light z-50 transition-all duration-300 ease-in-out
        ${sidebarWidth}
        ${isMobileOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0
        flex flex-col
      `}>
        {/* Header */}
        <div className={`flex items-center p-4 border-b border-border-light ${
          isCollapsed ? 'flex-col gap-3' : 'justify-between'
        }`}>
          {/* Logo Section */}
          <Link href="/dashboard" className={`flex items-center text-lg font-semibold text-text-primary ${
            isCollapsed ? 'justify-center' : 'gap-3'
          }`}>
            <div className="w-8 h-8 rounded-lg">
              <img 
                src="/portfolio-tracker-icon.svg" 
                alt="Portfolio Tracker" 
                className="w-full h-full"
              />
            </div>
            {!isCollapsed && <span className="hidden lg:block">Portfolio Tracker</span>}
          </Link>

          {/* Header Actions */}
          <div className="flex items-center gap-2">
            {/* Theme Toggle */}
            <ThemeToggleButton />
            
            {/* Mobile close button */}
            <button
              onClick={onMobileClose}
              className="lg:hidden p-1 rounded-lg hover:bg-surface transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 overflow-y-auto py-4">
          <nav className="space-y-1 px-3">
            {/* Standalone Items */}
            {standaloneItems.map((item) => (
              <SidebarItem
                key={item.key}
                href={item.href}
                icon={item.icon}
                label={item.label}
                isActive={getCurrentPage() === item.key}
                isCollapsed={isCollapsed}
                onClick={onMobileClose}
              />
            ))}

            {/* Divider */}
            <div className="my-4 border-t border-border-light" />

            {/* All Navigation Items */}
            {allNavigationItems.map((item) => (
              <SidebarItem
                key={item.key}
                href={item.href}
                icon={item.icon}
                label={item.label}
                isActive={getCurrentPage() === item.key}
                isCollapsed={isCollapsed}
                onClick={onMobileClose}
              />
            ))}
          </nav>
        </div>

        {/* Footer */}
        <div className="border-t border-border-light p-3">
          {/* Profile Dropdown */}
          <div className="mb-3 w-full">
            <div className="w-full">
              <AccountDropdown isCollapsed={isCollapsed} />
            </div>
          </div>

          {/* Collapse Toggle - Desktop Only */}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="hidden lg:flex items-center justify-center w-full p-2 rounded-lg hover:bg-surface transition-colors text-text-secondary hover:text-text-primary"
          >
            {isCollapsed ? (
              <ChevronRight className="w-5 h-5" />
            ) : (
              <>
                <ChevronLeft className="w-5 h-5" />
                <span className="ml-2 text-sm">Collapse</span>
              </>
            )}
          </button>
        </div>
      </div>
    </>
  )
}