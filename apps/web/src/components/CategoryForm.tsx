import React from 'react'
import Image from 'next/image'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { type ICategory, type ICategoryForm } from '@repo/shared'
import { CategoryIcon, CATEGORY_ICONS } from './CategoryBadge'

// Category form input schema (before transformation)
const categoryFormInputSchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  type: z.enum(['income', 'expense', 'transfer']),
  icon: z.string().optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format').optional(),
  description: z.string().optional(),
  sort_order: z.string().optional(),
})

// Category form schema (after transformation)
const categoryFormSchema = categoryFormInputSchema.transform((data) => ({
  ...data,
  sort_order: data.sort_order ? parseInt(data.sort_order, 10) : 0,
}))

type CategoryFormInputData = z.infer<typeof categoryFormInputSchema>

interface CategoryFormProps {
  onSubmit: (data: ICategoryForm) => Promise<void>
  loading?: boolean
  className?: string
  initialData?: ICategory
}

export const CategoryForm: React.FC<CategoryFormProps> = ({
  onSubmit,
  loading = false,
  className = "",
  initialData
}) => {
  const form = useForm<CategoryFormInputData>({
    resolver: zodResolver(categoryFormInputSchema),
    defaultValues: {
      name: initialData?.name || '',
      type: initialData?.type || 'expense',
      icon: initialData?.icon || '',
      color: initialData?.color || '#3B82F6',
      description: initialData?.description || '',
      sort_order: initialData?.sort_order?.toString() || '0',
    }
  })

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting }
  } = form

  const categoryType = watch('type')
  const selectedColor = watch('color')
  const [showAdvancedIcons, setShowAdvancedIcons] = React.useState(false)

  // URL validation for security
  const isValidImageUrl = (url: string): boolean => {
    try {
      const urlObj = new URL(url)
      // Only allow HTTPS URLs
      if (urlObj.protocol !== 'https:') return false
      
      // Check for known safe domains (you can expand this list)
      const safeDomains = [
        'cdn.jsdelivr.net',
        'unpkg.com',
        'cdnjs.cloudflare.com',
        'raw.githubusercontent.com',
        'via.placeholder.com',
        'images.unsplash.com',
        'cdn.iconscout.com',
        'img.icons8.com'
      ]
      
      const domain = urlObj.hostname.toLowerCase()
      const isSafeDomain = safeDomains.some(safeDomain => 
        domain === safeDomain || domain.endsWith('.' + safeDomain)
      )
      
      // Check file extension
      const validExtensions = ['.png', '.jpg', '.jpeg', '.svg', '.webp', '.gif']
      const hasValidExtension = validExtensions.some(ext => 
        urlObj.pathname.toLowerCase().endsWith(ext)
      )
      
      return isSafeDomain && hasValidExtension
    } catch {
      return false
    }
  }

  const handleFormSubmit = async (data: CategoryFormInputData) => {
    try {
      const validatedData = categoryFormSchema.parse(data)
      await onSubmit(validatedData)
    } catch (error) {
      console.error('Error submitting category:', error)
    }
  }

  const categoryTypes = [
    { value: 'expense', label: 'Expense', icon: '💸', description: 'Money going out' },
    { value: 'income', label: 'Income', icon: '💰', description: 'Money coming in' },
    { value: 'transfer', label: 'Transfer', icon: '🔄', description: 'Money moving between accounts' },
  ]

  const getIconsForType = (type: string) => {
    switch (type) {
      case 'income':
        return [
          { key: 'income', name: 'Income' },
          { key: 'salary', name: 'Salary' },
          { key: 'freelance', name: 'Freelance' },
          { key: 'business', name: 'Business' },
          { key: 'investment', name: 'Investment' },
          { key: 'bonus', name: 'Bonus' },
          { key: 'rental', name: 'Rental' },
        ]
      case 'expense':
        return [
          { key: 'food', name: 'Food' },
          { key: 'groceries', name: 'Groceries' },
          { key: 'restaurant', name: 'Restaurant' },
          { key: 'transport', name: 'Transport' },
          { key: 'gas', name: 'Gas/Fuel' },
          { key: 'entertainment', name: 'Entertainment' },
          { key: 'shopping', name: 'Shopping' },
          { key: 'clothing', name: 'Clothing' },
          { key: 'electronics', name: 'Electronics' },
          { key: 'healthcare', name: 'Healthcare' },
          { key: 'bills', name: 'Bills' },
          { key: 'utilities', name: 'Utilities' },
          { key: 'subscriptions', name: 'Subscriptions' },
          { key: 'gym', name: 'Gym/Fitness' },
          { key: 'education', name: 'Education' },
          { key: 'travel', name: 'Travel' },
          { key: 'beauty', name: 'Beauty' },
          { key: 'pets', name: 'Pets' },
          { key: 'gifts', name: 'Gifts' },
        ]
      case 'transfer':
        return [
          { key: 'internal-transfer', name: 'Internal Transfer' },
          { key: 'investment', name: 'Investment' },
          { key: 'loan-emi', name: 'Loan EMI' },
          { key: 'credit-card-payment', name: 'Credit Card Payment' },
          { key: 'savings', name: 'Savings' },
          { key: 'loan-repayment', name: 'Loan Repayment' },
          { key: 'insurance', name: 'Insurance' },
          { key: 'tax-payment', name: 'Tax Payment' },
        ]
      default:
        return []
    }
  }

  const commonColors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
  ]

  return (
    <div className={`${className}`}>
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Category Name */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Category Name *
          </label>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="text"
                placeholder="e.g., Groceries, Salary, etc."
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.name ? 'border-error-red' : ''}`}
              />
            )}
          />
          {errors.name && (
            <p className="text-error-red text-sm">{errors.name.message}</p>
          )}
        </div>

        {/* Category Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Category Type *
          </label>
          <Controller
            name="type"
            control={control}
            render={({ field }) => (
              <div className="grid grid-cols-3 gap-3">
                {categoryTypes.map((type) => (
                  <label
                    key={type.value}
                    className={`relative flex flex-col items-center p-4 border-2 rounded-lg cursor-pointer transition-all ${
                      field.value === type.value
                        ? 'border-primary-blue bg-primary-blue/5'
                        : 'border-border hover:border-primary-blue/50'
                    }`}
                  >
                    <input
                      type="radio"
                      {...field}
                      value={type.value}
                      className="sr-only"
                    />
                    <span className="text-2xl mb-2">{type.icon}</span>
                    <span className="text-sm font-medium text-text-primary">{type.label}</span>
                    <span className="text-xs text-text-secondary mt-1">{type.description}</span>
                  </label>
                ))}
              </div>
            )}
          />
          {errors.type && (
            <p className="text-error-red text-sm">{errors.type.message}</p>
          )}
        </div>

        {/* Icon Selection */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Icon
          </label>
          <Controller
            name="icon"
            control={control}
            render={({ field }) => (
              <div className="space-y-4">
                {/* Icon Input */}
                <div className="space-y-2">
                  <input
                    {...field}
                    type="text"
                    placeholder="Select an icon below, enter emoji, or paste icon URL"
                    className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary"
                  />
                  <p className="text-xs text-text-secondary">
                    You can use emojis (🏠), icon URLs, or select from the options below
                  </p>
                </div>

                {/* Predefined Icons */}
                <div>
                  <h4 className="text-sm font-medium text-text-primary mb-3">Choose from templates:</h4>
                  <div className="grid grid-cols-4 gap-3 max-h-60 overflow-y-auto px-4 py-2">
                    {getIconsForType(categoryType).map((iconItem) => (
                      <button
                        key={iconItem.key}
                        type="button"
                        onClick={() => field.onChange(iconItem.key)}
                        className={`flex flex-col items-center p-3 rounded-lg border-2 transition-all hover:scale-105 cursor-pointer ${
                          field.value === iconItem.key
                            ? 'border-primary-blue bg-primary-blue/10 shadow-lg'
                            : 'border-border hover:border-primary-blue/50 hover:bg-primary-blue/5'
                        }`}
                      >
                        <div className="w-6 h-6 text-text-primary mb-1">
                          <CategoryIcon name={iconItem.key} />
                        </div>
                        <span className="text-xs text-text-secondary text-center">
                          {iconItem.name}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Advanced Icon Options - Collapsible */}
                <div className="border-t pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAdvancedIcons(!showAdvancedIcons)}
                    className="flex items-center justify-between w-full p-2 text-sm font-medium text-text-primary hover:bg-surface-elevated rounded-lg transition-colors"
                  >
                    <span className="flex items-center gap-2">
                      <svg 
                        className={`w-4 h-4 transition-transform ${showAdvancedIcons ? 'rotate-90' : ''}`} 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      Advanced Icon Options
                    </span>
                    <span className="text-xs text-text-secondary">Custom emojis & images</span>
                  </button>
                  
                  {showAdvancedIcons && (
                    <div className="mt-3 space-y-3 pl-6">
                      <div className="bg-surface-elevated p-3 rounded-lg border border-border">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-lg">😀</span>
                          <span className="text-sm font-medium text-text-primary">Use Emoji</span>
                        </div>
                        <p className="text-xs text-text-secondary mb-2">
                          Type any emoji directly in the input field above (e.g., 🏠, 💰, 🎮, 🍕)
                        </p>
                        <div className="flex gap-2">
                          <button
                            type="button"
                            onClick={() => field.onChange('🏠')}
                            className="px-2 py-1 text-xs bg-primary-blue/10 text-primary-blue rounded hover:bg-primary-blue/20 transition-colors"
                          >
                            🏠 Try this
                          </button>
                          <button
                            type="button"
                            onClick={() => field.onChange('💰')}
                            className="px-2 py-1 text-xs bg-primary-blue/10 text-primary-blue rounded hover:bg-primary-blue/20 transition-colors"
                          >
                            💰 Try this
                          </button>
                          <button
                            type="button"
                            onClick={() => field.onChange('🎮')}
                            className="px-2 py-1 text-xs bg-primary-blue/10 text-primary-blue rounded hover:bg-primary-blue/20 transition-colors"
                          >
                            🎮 Try this
                          </button>
                        </div>
                      </div>
                      
                      <div className="bg-surface-elevated p-3 rounded-lg border border-border">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-lg">🔗</span>
                          <span className="text-sm font-medium text-text-primary">Use Secure Image URL</span>
                          <span className="px-2 py-1 text-xs bg-warning-orange/10 text-warning-orange rounded">
                            Restricted
                          </span>
                        </div>
                        <p className="text-xs text-text-secondary mb-2">
                          Only HTTPS URLs from trusted domains are allowed. Best size: 24x24px
                        </p>
                        <div className="mb-2">
                          <p className="text-xs text-text-secondary">Allowed domains:</p>
                          <p className="text-xs text-text-tertiary">
                            cdn.jsdelivr.net, unpkg.com, cdnjs.cloudflare.com, github, icons8.com
                          </p>
                        </div>
                        {watch('icon') && watch('icon')?.startsWith('http') && !isValidImageUrl(watch('icon') || '') && (
                          <div className="mb-2 p-2 bg-error-red/10 border border-error-red/20 rounded text-xs text-error-red">
                            ⚠️ Invalid or unsafe URL. Please use a URL from trusted domains.
                          </div>
                        )}
                        <button
                          type="button"
                          onClick={() => field.onChange('https://cdn.jsdelivr.net/npm/heroicons@2.0.18/24/outline/home.svg')}
                          className="px-2 py-1 text-xs bg-primary-blue/10 text-primary-blue rounded hover:bg-primary-blue/20 transition-colors"
                        >
                          Try safe example URL
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          />
        </div>

        {/* Color Selection */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Color
          </label>
          <Controller
            name="color"
            control={control}
            render={({ field }) => (
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <input
                    {...field}
                    type="color"
                    className="w-12 h-12 rounded-lg border-2 border-border cursor-pointer"
                  />
                  <input
                    {...field}
                    type="text"
                    placeholder="#3B82F6"
                    className="flex-1 bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary"
                  />
                </div>
                <div className="grid grid-cols-10 gap-2">
                  {commonColors.map((color) => (
                    <button
                      key={color}
                      type="button"
                      onClick={() => field.onChange(color)}
                      className={`w-8 h-8 rounded-lg border-2 transition-all hover:scale-110 ${
                        field.value === color
                          ? 'border-text-primary'
                          : 'border-border'
                      }`}
                      style={{ backgroundColor: color }}
                      title={color}
                    />
                  ))}
                </div>
              </div>
            )}
          />
          {errors.color && (
            <p className="text-error-red text-sm">{errors.color.message}</p>
          )}
        </div>

        {/* Description */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Description
          </label>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <textarea
                {...field}
                rows={3}
                placeholder="Optional description for this category..."
                className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary resize-none"
              />
            )}
          />
        </div>

        {/* Sort Order */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Sort Order
          </label>
          <Controller
            name="sort_order"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="number"
                min="0"
                placeholder="0"
                className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary"
              />
            )}
          />
          <p className="text-xs text-text-secondary">
            Lower numbers appear first in lists
          </p>
        </div>

        {/* Preview */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Preview
          </label>
          <div className="p-4 bg-surface rounded-lg border border-border">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 text-text-primary flex items-center justify-center">
                {(() => {
                  const iconValue = watch('icon')
                  
                  // If it's a predefined icon key (check if it exists in our CATEGORY_ICONS array)
                  if (iconValue && CATEGORY_ICONS.includes(iconValue as typeof CATEGORY_ICONS[number])) {
                    return <CategoryIcon name={iconValue} />
                  }
                  
                  // If it's a URL (starts with http) - only show if valid
                  if (iconValue && iconValue.startsWith('http')) {
                    if (isValidImageUrl(iconValue)) {
                      return <Image src={iconValue} alt="Custom icon" width={24} height={24} className="object-contain" />
                    } else {
                      return <span className="text-xs text-error-red">❌</span>
                    }
                  }
                  
                  // If it's an emoji or text
                  if (iconValue && iconValue.length > 0) {
                    return <span className="text-lg">{iconValue}</span>
                  }
                  
                  // Default fallback
                  return categoryType === 'income' ? <CategoryIcon name="income" /> : 
                         categoryType === 'transfer' ? <CategoryIcon name="internal-transfer" /> : 
                         <CategoryIcon name="food" />
                })()}
              </div>
              <div>
                <div className="font-medium text-text-primary">
                  {watch('name') || 'Category Name'}
                </div>
                <div 
                  className="inline-block px-2 py-1 rounded-full text-xs font-medium text-white"
                  style={{ backgroundColor: selectedColor || '#3B82F6' }}
                >
                  {categoryType}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            disabled={isSubmitting || loading}
            className="w-full bg-primary-blue hover:bg-primary-blue/90 disabled:bg-primary-blue/50 text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            {isSubmitting || loading ? 'Saving...' : initialData ? 'Update Category' : 'Create Category'}
          </button>
        </div>
      </form>
    </div>
  )
}
