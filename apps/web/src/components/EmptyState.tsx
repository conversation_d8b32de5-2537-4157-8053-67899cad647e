'use client';

import React from 'react';
import { LucideIcon } from 'lucide-react';

interface EmptyStateProps {
  icon: LucideIcon;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary';
  };
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon: Icon,
  title,
  description,
  action,
  className = ''
}) => {
  return (
    <div className={`flex flex-col items-center justify-center py-16 px-6 text-center ${className}`}>
      <div className="w-16 h-16 rounded-full bg-surface-elevated flex items-center justify-center mb-6 shadow-sm">
        <Icon className="w-8 h-8 text-text-tertiary" strokeWidth={1.5} />
      </div>
      
      <h3 className="text-xl font-semibold text-text-primary mb-2">
        {title}
      </h3>
      
      <p className="text-text-secondary max-w-md mb-6">
        {description}
      </p>
      
      {action && (
        <button
          onClick={action.onClick}
          className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
            action.variant === 'secondary'
              ? 'bg-surface-elevated text-text-primary border-2 border-border hover:border-primary-blue hover:text-primary-blue'
              : 'bg-gradient-to-r from-primary-blue to-primary-purple text-white shadow-md hover:shadow-lg hover:-translate-y-0.5'
          }`}
        >
          {action.label}
        </button>
      )}
    </div>
  );
};

export default EmptyState;