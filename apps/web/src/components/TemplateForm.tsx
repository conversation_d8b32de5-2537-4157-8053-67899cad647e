import React from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { type ICategory } from '@repo/shared'

// Template form input schema (before transformation)
const templateFormInputSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  amount: z.string().min(1, 'Amount is required'),
  category_id: z.string().min(1, 'Category is required'),
  description: z.string().optional(),
  transaction_type: z.enum(['income', 'expense']),
  is_recurring: z.boolean().optional(),
  frequency: z.enum(['daily', 'weekly', 'monthly', 'yearly']).optional(),
  auto_create: z.boolean().optional(),
})

// Template form schema (after transformation)
const templateFormSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  amount: z.number().positive('Amount must be positive'),
  category_id: z.string().min(1, 'Category is required'),
  description: z.string().optional(),
  transaction_type: z.enum(['income', 'expense']),
  is_recurring: z.boolean().optional(),
  frequency: z.enum(['daily', 'weekly', 'monthly', 'yearly']).optional(),
  auto_create: z.boolean().optional(),
}).transform((data) => ({
  ...data,
  amount: parseFloat(data.amount as any),
  is_recurring: data.is_recurring || false,
  auto_create: data.auto_create || false,
}))

type TemplateFormInputData = z.infer<typeof templateFormInputSchema>

interface TemplateFormProps {
  onSubmit: (data: any) => Promise<void>
  loading?: boolean
  className?: string
  categories: ICategory[]
  initialData?: any
}

export const TemplateForm: React.FC<TemplateFormProps> = ({
  onSubmit,
  loading = false,
  className = "",
  categories,
  initialData
}) => {
  const form = useForm<TemplateFormInputData>({
    resolver: zodResolver(templateFormInputSchema),
    defaultValues: {
      name: initialData?.name || '',
      amount: initialData?.amount?.toString() || '',
      category_id: initialData?.category_id || '',
      description: initialData?.description || '',
      transaction_type: initialData?.transaction_type || 'expense',
      is_recurring: initialData?.is_recurring || false,
      frequency: initialData?.frequency || 'monthly',
      auto_create: initialData?.auto_create || false,
    }
  })

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting }
  } = form

  const transactionType = watch('transaction_type')
  const isRecurring = watch('is_recurring')

  const handleFormSubmit = async (data: TemplateFormInputData) => {
    try {
      // Transform the data before validation
      const transformedData = {
        ...data,
        amount: parseFloat(data.amount)
      }
      const validatedData = templateFormSchema.parse(transformedData)
      await onSubmit(validatedData)
    } catch (error) {
      console.error('Error submitting template:', error)
    }
  }

  const transactionTypes = [
    { value: 'expense', label: 'Expense', icon: '💸', description: 'Money going out' },
    { value: 'income', label: 'Income', icon: '💰', description: 'Money coming in' },
  ]

  const frequencies = [
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'yearly', label: 'Yearly' },
  ]

  const filteredCategories = categories.filter(category => 
    category.type === transactionType
  )

  return (
    <div className={`${className}`}>
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Template Name */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Template Name *
          </label>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="text"
                placeholder="e.g., Monthly Rent, Weekly Groceries"
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.name ? 'border-error-red' : ''}`}
              />
            )}
          />
          {errors.name && (
            <p className="text-error-red text-sm">{errors.name.message}</p>
          )}
        </div>

        {/* Transaction Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Transaction Type *
          </label>
          <Controller
            name="transaction_type"
            control={control}
            render={({ field }) => (
              <div className="grid grid-cols-2 gap-3">
                {transactionTypes.map((type) => (
                  <label
                    key={type.value}
                    className={`relative flex flex-col items-center p-4 border-2 rounded-lg cursor-pointer transition-all ${
                      field.value === type.value
                        ? 'border-primary-blue bg-primary-blue/5'
                        : 'border-border hover:border-primary-blue/50'
                    }`}
                  >
                    <input
                      type="radio"
                      {...field}
                      value={type.value}
                      className="sr-only"
                    />
                    <span className="text-2xl mb-2">{type.icon}</span>
                    <span className="text-sm font-medium text-text-primary">{type.label}</span>
                    <span className="text-xs text-text-secondary mt-1">{type.description}</span>
                  </label>
                ))}
              </div>
            )}
          />
          {errors.transaction_type && (
            <p className="text-error-red text-sm">{errors.transaction_type.message}</p>
          )}
        </div>

        {/* Amount */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Amount *
          </label>
          <Controller
            name="amount"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="text"
                inputMode="decimal"
                placeholder="0.00"
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.amount ? 'border-error-red' : ''}`}
              />
            )}
          />
          {errors.amount && (
            <p className="text-error-red text-sm">{errors.amount.message}</p>
          )}
        </div>

        {/* Category */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Category *
          </label>
          <Controller
            name="category_id"
            control={control}
            render={({ field }) => (
              <select
                {...field}
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.category_id ? 'border-error-red' : ''}`}
              >
                <option value="">Select a category</option>
                {filteredCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            )}
          />
          {errors.category_id && (
            <p className="text-error-red text-sm">{errors.category_id.message}</p>
          )}
        </div>

        {/* Description */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Description
          </label>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <textarea
                {...field}
                rows={3}
                placeholder="Optional description for this template..."
                className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary resize-none"
              />
            )}
          />
        </div>

        {/* Recurring Options */}
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <Controller
              name="is_recurring"
              control={control}
              render={({ field }) => (
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={field.value}
                    onChange={field.onChange}
                    className="w-4 h-4 text-primary-blue bg-surface border-border rounded focus:ring-primary-blue focus:ring-2"
                  />
                  <span className="text-sm font-medium text-text-primary">Make this a recurring template</span>
                </label>
              )}
            />
          </div>

          {isRecurring && (
            <>
              {/* Frequency */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-text-primary">
                  Frequency
                </label>
                <Controller
                  name="frequency"
                  control={control}
                  render={({ field }) => (
                    <select
                      {...field}
                      className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary"
                    >
                      {frequencies.map((freq) => (
                        <option key={freq.value} value={freq.value}>
                          {freq.label}
                        </option>
                      ))}
                    </select>
                  )}
                />
              </div>

              {/* Auto Create */}
              <div className="flex items-center gap-3">
                <Controller
                  name="auto_create"
                  control={control}
                  render={({ field }) => (
                    <label className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        className="w-4 h-4 text-primary-blue bg-surface border-border rounded focus:ring-primary-blue focus:ring-2"
                      />
                      <span className="text-sm font-medium text-text-primary">Automatically create transactions</span>
                    </label>
                  )}
                />
              </div>
              <p className="text-xs text-text-secondary">
                When enabled, transactions will be automatically created based on the frequency
              </p>
            </>
          )}
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            disabled={isSubmitting || loading}
            className="w-full bg-primary-blue hover:bg-primary-blue/90 disabled:bg-primary-blue/50 text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            {isSubmitting || loading ? 'Saving...' : initialData ? 'Update Template' : 'Create Template'}
          </button>
        </div>
      </form>
    </div>
  )
}
