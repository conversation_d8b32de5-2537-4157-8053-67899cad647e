'use client';

import React from 'react';
import { LucideIcon } from 'lucide-react';
import { ContentCard } from './ContentCard';

interface MetricCardProps {
  title: string;
  value: string;
  subtitle?: string;
  icon: LucideIcon;
  trend?: {
    value: string;
    direction: 'up' | 'down' | 'neutral';
  };
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  className?: string;
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  color = 'blue',
  className = ''
}) => {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-success-green',
    purple: 'bg-purple-500',
    orange: 'bg-warning-orange',
    red: 'bg-error-red'
  };

  const trendColors = {
    up: 'text-success-green',
    down: 'text-error-red',
    neutral: 'text-text-secondary'
  };

  return (
    <ContentCard className={`${className} relative overflow-hidden group`} padding="md" hoverable>
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary-blue/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      
      <div className="relative">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className={`w-12 h-12 rounded-xl ${colorClasses[color]} flex items-center justify-center shadow-lg ring-4 ring-white/10`}>
              <Icon className="w-6 h-6 text-white" strokeWidth={2} />
            </div>
            <div>
              <p className="text-xs font-semibold text-text-secondary uppercase tracking-wider">
                {title}
              </p>
            </div>
          </div>
          
          {trend && (
            <div className={`text-sm font-semibold px-2 py-1 rounded-lg ${
              trend.direction === 'up' 
                ? 'bg-success-green/10 text-success-green' 
                : trend.direction === 'down'
                ? 'bg-error-red/10 text-error-red'
                : 'bg-gray-100 text-text-secondary'
            }`}>
              {trend.value}
            </div>
          )}
        </div>
        
        <div className="space-y-2">
          <p className="text-3xl font-bold text-text-primary bg-gradient-to-r from-text-primary to-primary-blue bg-clip-text">
            {value}
          </p>
          {subtitle && (
            <p className="text-sm text-text-secondary leading-relaxed">
              {subtitle}
            </p>
          )}
        </div>
      </div>
    </ContentCard>
  );
};

export default MetricCard;