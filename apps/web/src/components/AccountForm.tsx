import React from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { type IAccount, type IAccountForm } from '@shared/index'

// Account form input schema (before transformation)
const accountFormInputSchema = z.object({
  name: z.string().min(1, 'Account name is required'),
  account_type: z.enum(['bank', 'investment', 'savings', 'credit_card', 'cash']),
  account_number: z.string().optional(),
  institution_name: z.string().optional(),
  current_balance: z.string().optional(),
  available_balance: z.string().optional(),
  credit_limit: z.string().optional(),
  interest_rate: z.string().optional(),
  is_primary: z.boolean().optional(),
})

// Account form schema with transformation
const accountFormSchema = accountFormInputSchema.transform((data) => ({
  ...data,
  current_balance: data.current_balance ? parseFloat(data.current_balance) : 0,
  available_balance: data.available_balance ? parseFloat(data.available_balance) : undefined,
  credit_limit: data.credit_limit ? parseFloat(data.credit_limit) : undefined,
  interest_rate: data.interest_rate ? parseFloat(data.interest_rate) : undefined,
}))

type AccountFormInputData = z.infer<typeof accountFormInputSchema>
type AccountFormData = z.infer<typeof accountFormSchema>

interface AccountFormProps {
  onSubmit: (data: IAccountForm) => Promise<void>
  loading?: boolean
  className?: string
  initialData?: IAccount | null
}

export const AccountForm: React.FC<AccountFormProps> = ({
  onSubmit,
  loading = false,
  className = "",
  initialData
}) => {
  const form = useForm<AccountFormInputData>({
    resolver: zodResolver(accountFormInputSchema),
    defaultValues: {
      name: initialData?.name || '',
      account_type: initialData?.account_type || 'bank',
      account_number: initialData?.account_number || '',
      institution_name: initialData?.institution_name || '',
      current_balance: initialData?.current_balance?.toString() || '0',
      available_balance: initialData?.available_balance?.toString() || '',
      credit_limit: initialData?.credit_limit?.toString() || '',
      interest_rate: initialData?.interest_rate?.toString() || '',
      is_primary: initialData?.is_primary || false,
    }
  })

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting }
  } = form

  const accountType = watch('account_type')

  const handleFormSubmit = async (data: AccountFormInputData) => {
    try {
      const validatedData = accountFormSchema.parse(data)
      await onSubmit(validatedData)
    } catch (error) {
      console.error('Error submitting account:', error)
    }
  }

  const accountTypes = [
    { value: 'bank', label: 'Bank Account', icon: '🏦' },
    { value: 'investment', label: 'Investment Account', icon: '📈' },
    { value: 'loan', label: 'Loan Account', icon: '💰' },
    { value: 'credit_card', label: 'Credit Card', icon: '💳' },
    { value: 'cash', label: 'Cash', icon: '💵' },
  ]


  return (
    <div className={`space-y-6 ${className}`}>
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Account Name */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Account Name *
          </label>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="text"
                placeholder="Enter account name"
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.name ? 'border-error-red' : ''}`}
              />
            )}
          />
          {errors.name && (
            <p className="text-error-red text-sm">{errors.name.message}</p>
          )}
        </div>

        {/* Account Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Account Type *
          </label>
          <Controller
            name="account_type"
            control={control}
            render={({ field }) => (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {accountTypes.map((type) => (
                  <label
                    key={type.value}
                    className={`flex items-center space-x-3 p-3 border-2 rounded-lg cursor-pointer transition-all ${
                      field.value === type.value
                        ? 'border-primary-blue bg-primary-blue/10'
                        : 'border-border hover:border-primary-blue/50'
                    }`}
                  >
                    <input
                      type="radio"
                      value={type.value}
                      checked={field.value === type.value}
                      onChange={field.onChange}
                      className="sr-only"
                    />
                    <span className="text-lg">{type.icon}</span>
                    <span className="text-sm font-medium text-text-primary">{type.label}</span>
                  </label>
                ))}
              </div>
            )}
          />
          {errors.account_type && (
            <p className="text-error-red text-sm">{errors.account_type.message}</p>
          )}
        </div>

        {/* Institution Name */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Institution Name
          </label>
          <Controller
            name="institution_name"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="text"
                placeholder="e.g., Chase Bank, Fidelity, etc."
                className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary"
              />
            )}
          />
        </div>

        {/* Account Number */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Account Number
          </label>
          <Controller
            name="account_number"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="text"
                placeholder="Last 4 digits or full number"
                className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary font-mono"
              />
            )}
          />
        </div>

        {/* Current Balance */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Current Balance
          </label>
          <Controller
            name="current_balance"
            control={control}
            render={({ field }) => (
              <input
                {...field}
                type="number"
                step="0.01"
                placeholder="0.00"
                className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary"
              />
            )}
          />
        </div>

        {/* Credit Card Specific Fields */}
        {accountType === 'credit_card' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-text-primary">
                Credit Limit
              </label>
              <Controller
                name="credit_limit"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary"
                  />
                )}
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-text-primary">
                Interest Rate (%)
              </label>
              <Controller
                name="interest_rate"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary"
                  />
                )}
              />
            </div>
          </div>
        )}

        {/* Loan Specific Fields */}
        {accountType === 'loan' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-text-primary">
                Loan Amount (Principal)
              </label>
              <Controller
                name="credit_limit"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary"
                  />
                )}
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-text-primary">
                Interest Rate (%)
              </label>
              <Controller
                name="interest_rate"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary"
                  />
                )}
              />
            </div>
          </div>
        )}

        {/* Bank Account Specific Fields */}
        {accountType === 'bank' && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Available Balance
            </label>
            <Controller
              name="available_balance"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary"
                />
              )}
            />
          </div>
        )}

        {/* Primary Account */}
        <div className="space-y-2">
          <Controller
            name="is_primary"
            control={control}
            render={({ field }) => (
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={field.value}
                  onChange={field.onChange}
                  className="w-4 h-4 text-primary-blue bg-surface border-border rounded focus:ring-primary-blue focus:ring-2"
                />
                <span className="text-sm font-medium text-text-primary">
                  Set as primary account for this type
                </span>
              </label>
            )}
          />
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="submit"
            disabled={loading || isSubmitting}
            className="bg-primary-blue text-white px-6 py-3 rounded-lg hover:bg-primary-blue/90 transition-colors disabled:opacity-50 font-medium"
          >
            {loading || isSubmitting ? 'Saving...' : initialData ? 'Update Account' : 'Create Account'}
          </button>
        </div>
      </form>
    </div>
  )
}
