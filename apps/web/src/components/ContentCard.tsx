'use client';

import React from 'react';

interface ContentCardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
  hoverable?: boolean;
  clickable?: boolean;
  onClick?: () => void;
}

export const ContentCard: React.FC<ContentCardProps> = ({
  children,
  className = '',
  padding = 'md',
  hoverable = false,
  clickable = false,
  onClick
}) => {
  const paddingClasses = {
    sm: 'p-4 sm:p-5',
    md: 'p-5 sm:p-6 lg:p-7',
    lg: 'p-6 sm:p-8 lg:p-10'
  };

  const baseClasses = 'bg-surface-elevated/90 backdrop-blur-sm rounded-2xl border border-border-light/50 shadow-sm ring-1 ring-black/5';
  const hoverClasses = hoverable || clickable 
    ? 'hover:shadow-lg hover:shadow-primary-blue/10 hover:border-primary-blue/20 hover:ring-primary-blue/20 transition-all duration-300 ease-out' 
    : 'transition-shadow duration-200';
  const clickableClasses = clickable ? 'cursor-pointer hover:-translate-y-1 hover:scale-[1.02] transform-gpu' : '';

  return (
    <div
      className={`${baseClasses} ${paddingClasses[padding]} ${hoverClasses} ${clickableClasses} ${className}`}
      onClick={clickable ? onClick : undefined}
    >
      {children}
    </div>
  );
};

export default ContentCard;