'use client';

import React from 'react';

interface Tab {
  id: string;
  label: string;
  count?: number;
  icon?: React.ComponentType<{ className?: string }>;
}

interface TabNavigationProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className = ''
}) => {
  return (
    <div className={`mb-4 ${className}`}>
      <nav className="flex space-x-1 bg-surface p-1 rounded-lg border border-border">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          const isActive = activeTab === tab.id;
          
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`flex items-center gap-2 py-2 px-4 rounded-md font-medium text-sm transition-all duration-200 whitespace-nowrap cursor-pointer ${
                isActive
                  ? 'bg-background text-primary-blue shadow-sm'
                  : 'text-text-secondary hover:text-text-primary hover:bg-background/50'
              }`}
            >
              {Icon && <Icon className="w-4 h-4" />}
              <span>{tab.label}</span>
              {typeof tab.count !== 'undefined' && (
                <span className={`ml-1 px-2 py-0.5 text-xs rounded-full font-medium ${
                  isActive
                    ? 'bg-primary-blue/10 text-primary-blue'
                    : 'bg-text-tertiary/20 text-text-tertiary'
                }`}>
                  {tab.count}
                </span>
              )}
            </button>
          );
        })}
      </nav>
    </div>
  );
};

export default TabNavigation;