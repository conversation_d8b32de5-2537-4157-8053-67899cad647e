import React from 'react'
import Image from 'next/image'
import { useTheme } from '@/contexts/ThemeContext'

export interface CategoryBadgeProps {
  name: string
  color?: string
  icon?: React.ReactNode
  size?: 'small' | 'medium' | 'large'
  variant?: 'default' | 'outline' | 'solid'
  className?: string
}

export function CategoryBadge({
  name,
  color = '#3B82F6',
  icon,
  size = 'medium',
  variant = 'default',
  className = ''
}: CategoryBadgeProps) {
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'px-2 py-1 text-xs'
      case 'large':
        return 'px-4 py-2 text-base'
      default:
        return 'px-3 py-1.5 text-sm'
    }
  }

  const getVariantClasses = () => {
    switch (variant) {
      case 'outline':
        return 'border-2 bg-transparent'
      case 'solid':
        return 'text-white'
      default:
        return 'bg-opacity-10 border border-opacity-20'
    }
  }

  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 'w-3 h-3'
      case 'large':
        return 'w-5 h-5'
      default:
        return 'w-4 h-4'
    }
  }

  const styles = {
    backgroundColor: variant === 'solid' ? color : variant === 'default' ? `${color}10` : 'transparent',
    borderColor: color,
    color: variant === 'solid' ? 'white' : color
  }

  return (
    <div 
      className={`inline-flex items-center gap-2 rounded-full font-medium transition-all ${getSizeClasses()} ${getVariantClasses()} ${className}`}
      style={styles}
    >
      {icon && (
        <span className={getIconSize()}>
          {icon}
        </span>
      )}
      <span>{name}</span>
    </div>
  )
}

// Icon utility function to load SVG icons from public/icons folder
export function CategoryIcon({ name, className = "w-full h-full" }: { name: string; className?: string }) {
  const { resolvedTheme } = useTheme()
  
  const iconName = name.toLowerCase()
    .replace(/([a-z])([A-Z])/g, '$1-$2') // Convert camelCase to kebab-case
    .replace(/\s+/g, '-') // Replace spaces with dashes
    .toLowerCase()

  // Apply appropriate filter based on theme
  const iconFilter = resolvedTheme === 'dark' 
    ? 'brightness(0) saturate(100%) invert(1)' // Invert to white in dark mode
    : 'brightness(0) saturate(100%)' // Black in light mode

  return (
    <Image 
      src={`/icons/${iconName}.svg`}
      alt={name}
      width={24}
      height={24}
      className={className}
      style={{ filter: iconFilter }}
    />
  )
}

// Available category icon names for easy reference
export const CATEGORY_ICONS = [
  'food', 'transport', 'entertainment', 'shopping', 'healthcare', 'bills', 
  'income', 'salary', 'freelance', 'business', 'investment', 'bonus', 'rental',
  'groceries', 'restaurant', 'gas', 'utilities', 'gym', 'education', 'travel',
  'beauty', 'subscriptions', 'clothing', 'electronics', 'pets', 'gifts',
  'internal-transfer', 'loan-emi', 'credit-card-payment', 'savings', 
  'loan-repayment', 'insurance', 'tax-payment', 'bills-utilities', 'food-dining',
  'gas-fuel', 'gift', 'other', 'other-income', 'transportation'
] as const

export type CategoryIconName = typeof CATEGORY_ICONS[number]

export default CategoryBadge