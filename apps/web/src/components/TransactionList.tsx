import * as React from 'react'
import { useState, useRef, useEffect } from 'react'
import {
  TransactionService,
  CategoryService,
  TransferService,
  type ITransaction,
  type ITransferTransaction,
  type ICategory,
  type TransactionType,
  useCurrencyStore
} from '@repo/shared'
import { CategoryIcon, CATEGORY_ICONS } from './CategoryBadge'
import { ContentCard } from './ContentCard'
import { LoadingState } from './LoadingState'
import { toast } from 'react-hot-toast'

// Temporary placeholder types
interface TransactionListProps {
  // Add props as needed
}

// Combined type for displaying both regular transactions and transfers
type DisplayTransaction = ITransaction | (ITransferTransaction & { transaction_type: 'transfer' })

interface TransactionFilters {
  searchQuery: string
  categoryId: string
  accountId: string
  startDate: string
  endDate: string
  transactionType: 'all' | TransactionType
}

interface TransactionListWebProps extends TransactionListProps {
  onEditTransaction?: (transaction: ITransaction) => void
  onTransactionDeleted?: () => void // Callback when transaction is deleted
  refreshTrigger?: number // For refresh functionality
  accountId?: string // Filter transactions by account
  hideFilters?: boolean // Hide filter controls
  onLoadingChange?: (isLoading: boolean) => void // Callback for loading state changes
}

export function TransactionListWeb({
  onEditTransaction,
  onTransactionDeleted,
  refreshTrigger,
  accountId,
  hideFilters = false,
  onLoadingChange,
  ...props
}: TransactionListWebProps) {
  const { formatCurrency } = useCurrencyStore()
  const [transactions, setTransactions] = useState<DisplayTransaction[]>([])
  const [categories, setCategories] = useState<ICategory[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingMore, setLoadingMore] = useState(false)
  const [error, setError] = useState('')
  const [totalCount, setTotalCount] = useState(0)
  const [filters, setFilters] = useState<TransactionFilters>({
    searchQuery: '',
    categoryId: '',
    accountId: accountId || '',
    startDate: '',
    endDate: '',
    transactionType: 'all'
  })
  const [offset, setOffset] = useState(0)
  const [hasMoreItems, setHasMoreItems] = useState(false)
  const ITEMS_PER_PAGE = 20

  // Helper function to combine regular transactions and transfers for display
  const combineTransactionsAndTransfers = (
    regularTransactions: ITransaction[], 
    transfers: ITransferTransaction[]
  ): DisplayTransaction[] => {
    const combined: DisplayTransaction[] = []
    
    // Add regular transactions (income, expense, investments)
    combined.push(...regularTransactions)
    
    // Add transfers with transaction_type for consistent display
    transfers.forEach(transfer => {
      combined.push({
        ...transfer,
        transaction_type: 'transfer' as const,
        // Map transfer fields to transaction interface
        account_id: transfer.from_account_id,
        to_account_id: transfer.to_account_id,
        account: transfer.from_account,
        to_account: transfer.to_account,
        category: null, // Transfers may not have categories in the same way
        category_id: null,
      })
    })
    
    // Sort by date (most recent first)
    return combined.sort((a, b) => 
      new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime()
    )
  }

  const loadTransactions = async (reset = false) => {
    try {
      const currentOffset = reset ? 0 : offset
      if (reset) {
        setLoading(true)
        setError('')
      } else {
        setLoadingMore(true)
      }

      // Prepare filter options
      const baseOptions = {
        limit: ITEMS_PER_PAGE,
        offset: currentOffset,
        ...(filters.accountId && { accountId: filters.accountId }),
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate }),
        ...(filters.searchQuery && { searchQuery: filters.searchQuery }),
      }

      let regularTransactions: ITransaction[] = []
      let transfers: ITransferTransaction[] = []
      let totalCount = 0

      // Fetch regular transactions (income, expense, investments)
      if (filters.transactionType === 'all' || 
          ['income', 'expense', 'investment_buy', 'investment_sell', 'dividend'].includes(filters.transactionType)) {
        
        const transactionOptions = {
          ...baseOptions,
          ...(filters.categoryId && { categoryId: filters.categoryId }),
          ...(filters.transactionType !== 'all' && filters.transactionType !== 'transfer' && { 
            transactionType: filters.transactionType as TransactionType 
          }),
          includeTransfers: false, // Don't include individual transfer transactions
          includeInvestments: true,
        }

        const transactionResult = await TransactionService.getTransactions(transactionOptions)
        regularTransactions = transactionResult.data
        totalCount += transactionResult.count
      }

      // Fetch transfers separately (only if transfer type is included)
      if (filters.transactionType === 'all' || filters.transactionType === 'transfer') {
        const transferOptions = {
          ...baseOptions,
          account_id: filters.accountId, // TransferService uses account_id instead of accountId
        }

        const transferResult = await TransferService.getTransfers(transferOptions)
        transfers = transferResult.data
        totalCount += transferResult.count
      }

      // Combine and sort the results
      const combinedTransactions = combineTransactionsAndTransfers(regularTransactions, transfers)

      if (reset) {
        setTransactions(combinedTransactions)
        setOffset(ITEMS_PER_PAGE)
      } else {
        setTransactions(prev => {
          // Merge new transactions, avoiding duplicates
          const existingIds = new Set(prev.map(t => t.id))
          const newTransactions = combinedTransactions.filter(t => !existingIds.has(t.id))
          return [...prev, ...newTransactions]
        })
        setOffset(prev => prev + ITEMS_PER_PAGE)
      }

      setTotalCount(totalCount)
      setHasMoreItems(combinedTransactions.length === ITEMS_PER_PAGE && (currentOffset + ITEMS_PER_PAGE) < totalCount)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load transactions')
      toast.error('Failed to load transactions')
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }

  const loadCategories = async () => {
    try {
      const categoriesData = await CategoryService.getCategories({ is_active: true })
      setCategories(categoriesData)
    } catch (err) {
      console.error('Failed to load categories:', err)
    }
  }

  // Initial load
  useEffect(() => {
    loadCategories()
    loadTransactions(true)
  }, [])

  // Reload when refreshTrigger changes (for refresh from parent)
  useEffect(() => {
    if (refreshTrigger !== undefined) {
      loadTransactions(true)
    }
  }, [refreshTrigger])

  // Update filters when accountId prop changes
  useEffect(() => {
    if (accountId) {
      setFilters(prev => ({ ...prev, accountId }))
    }
  }, [accountId])

  // Reload when filters change
  useEffect(() => {
    loadTransactions(true)
  }, [filters])

  const handleRefresh = () => {
    loadTransactions(true)
  }

  const handleLoadMore = () => {
    if (!loadingMore && hasMoreItems) {
      loadTransactions(false)
    }
  }

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, searchQuery: query }))
  }

  const handleFilterChange = (newFilters: Partial<TransactionFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  const handleEdit = (transaction: ITransaction) => { 
    onEditTransaction?.(transaction) 
  }

  const handleDelete = async (id: string) => {
    // Find the transaction to determine if it's a transfer
    const transaction = transactions.find(t => t.id === id)
    const isTransfer = transaction && transaction.transaction_type === 'transfer'
    
    const confirmMessage = isTransfer 
      ? 'Are you sure you want to delete this transfer? This will cancel the entire transfer operation.'
      : 'Are you sure you want to delete this transaction?'
    
    if (!confirm(confirmMessage)) {
      return
    }

    try {
      if (isTransfer && 'transfer_id' in transaction) {
        // Use TransferService to cancel the transfer
        await TransferService.cancelTransfer(transaction.transfer_id)
        toast.success('Transfer cancelled successfully')
      } else {
        // Use regular transaction deletion
        await TransactionService.deleteTransaction(id)
        toast.success('Transaction deleted successfully')
      }
      
      loadTransactions(true)
      // Notify parent component that transaction was deleted
      onTransactionDeleted?.()
    } catch (err) {
      const errorMessage = isTransfer ? 'Failed to cancel transfer' : 'Failed to delete transaction'
      toast.error(errorMessage)
    }
  }

  const formatDate = (date: string | Date) => new Date(date).toLocaleDateString()

  // Helper function to get category icon like in the categories page
  const getCategoryIcon = (category: ICategory | null | undefined) => {
    if (!category) return '💰' // Default fallback
    
    // If icon is a key in our CATEGORY_ICONS array, return the CategoryIcon component
    if (category.icon && CATEGORY_ICONS.includes(category.icon as any)) {
      return (
        <div className="w-5 h-5 text-current">
          <CategoryIcon name={category.icon} />
        </div>
      )
    }
    
    // If icon is an emoji or other string, return as is
    if (category.icon) return category.icon
    
    // Default fallback icons based on type
    if (category.type === 'income') return '💰'
    if (category.type === 'expense') return '💸'
    return '🔄' // Transfer categories
  }

  // Helper function to get transaction display info
  const getTransactionDisplayInfo = (transaction: DisplayTransaction) => {
    switch (transaction.transaction_type) {
      case 'income':
        return {
          icon: getCategoryIcon(transaction.category),
          title: transaction.category?.name || 'Income',
          color: 'text-green-600',
          sign: '+',
          description: transaction.description
        }
      case 'expense':
        return {
          icon: getCategoryIcon(transaction.category),
          title: transaction.category?.name || 'Expense',
          color: 'text-red-600',
          sign: '-',
          description: transaction.description
        }
      case 'transfer':
        // Handle both ITransaction and ITransferTransaction types
        const fromAccount = 'account' in transaction 
          ? transaction.account 
          : 'from_account' in transaction 
          ? transaction.from_account 
          : null
        
        const toAccount = 'to_account' in transaction 
          ? transaction.to_account 
          : null
        
        return {
          icon: '🔄', // Use transfer icon
          title: `Transfer from ${fromAccount?.name || 'Unknown'} to ${toAccount?.name || 'Unknown'}`,
          color: 'text-blue-600',
          sign: '',
          description: transaction.description || 'Account transfer'
        }
      case 'investment_buy':
        return {
          icon: '📈',
          title: `Buy ${transaction.investment_symbol}`,
          color: 'text-purple-600',
          sign: '-',
          description: transaction.description || `${transaction.investment_quantity} shares at ${formatCurrency(transaction.investment_price || 0)}`
        }
      case 'investment_sell':
        return {
          icon: '📉',
          title: `Sell ${transaction.investment_symbol}`,
          color: 'text-orange-600',
          sign: '+',
          description: transaction.description || `${transaction.investment_quantity} shares at ${formatCurrency(transaction.investment_price || 0)}`
        }
      case 'dividend':
        return {
          icon: '💎',
          title: `Dividend from ${transaction.investment_symbol}`,
          color: 'text-green-600',
          sign: '+',
          description: transaction.description || 'Dividend payment'
        }
      default:
        return {
          icon: '💰',
          title: 'Transaction',
          color: 'text-gray-600',
          sign: '',
          description: transaction.description
        }
    }
  }

  // Quick date filter functions
  const [activeQuickFilter, setActiveQuickFilter] = useState<string | null>(null)
  
  const setQuickDateFilter = (period: 'week' | 'month' | 'year') => {
    const today = new Date()
    let startDate: Date

    switch (period) {
      case 'week':
        startDate = new Date(today)
        startDate.setDate(today.getDate() - 7)
        break
      case 'month':
        startDate = new Date(today)
        startDate.setMonth(today.getMonth() - 1)
        break
      case 'year':
        startDate = new Date(today)
        startDate.setFullYear(today.getFullYear() - 1)
        break
    }

    setActiveQuickFilter(period)
    setFilters(prev => ({
      ...prev,
      startDate: startDate.toISOString().split('T')[0],
      endDate: today.toISOString().split('T')[0]
    }))
  }
  
  const clearDateFilter = () => {
    setActiveQuickFilter('all')
    setFilters(prev => ({ ...prev, startDate: '', endDate: '' }))
  }

  const [showFilters, setShowFilters] = useState(false)
  const [searchInput, setSearchInput] = useState('')
  const loadMoreRef = useRef<HTMLDivElement>(null)

  // Sync search input with filters
  useEffect(() => {
    setSearchInput(filters.searchQuery)
  }, [filters.searchQuery])

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchInput !== filters.searchQuery) {
        setFilters(prev => ({ ...prev, searchQuery: searchInput }))
      }
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchInput, filters.searchQuery])

  // Infinite scroll implementation
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMoreItems && !loadingMore) {
          handleLoadMore()
        }
      },
      { threshold: 0.1 }
    )

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current)
    }

    return () => observer.disconnect()
  }, [hasMoreItems, loadingMore, handleLoadMore])

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSearch(searchInput)
  }

  const clearFilters = () => {
    const clearedFilters = {
      searchQuery: '',
      categoryId: '',
      accountId: '',
      startDate: '',
      endDate: '',
      transactionType: 'all' as const
    }
    setActiveQuickFilter(null)
    setFilters(clearedFilters)
  }

  const hasActiveFilters = filters.searchQuery || filters.categoryId || filters.accountId ||
    filters.startDate || filters.endDate || filters.transactionType !== 'all'

  return (
    <ContentCard className="shadow-lg" padding="sm">
      {/* Header with Search and Filters */}
      <div className="pb-5 border-b border-border dark:border-dark-border">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-xl font-semibold text-text-primary">
              Transactions
            </h2>
            {totalCount > 0 && (
              <p className="text-sm text-text-tertiary mt-1">
                {totalCount} transaction{(totalCount as number) === 1 ? '' : 's'} found
              </p>
            )}
          </div>
          
          {!hideFilters && (
            <div className="flex items-center gap-3">
              {/* Search Form */}
              <form onSubmit={handleSearchSubmit} className="flex-1 sm:flex-initial">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search by description or category..."
                    value={searchInput}
                    onChange={(e) => setSearchInput(e.target.value)}
                    className="w-full sm:w-64 pl-10 pr-4 py-2 bg-surface dark:bg-dark-surface border border-border dark:border-dark-border rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent text-text-primary"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-text-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
              </form>

              {/* Filter Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  showFilters
                    ? 'bg-primary-blue text-white'
                    : 'bg-surface dark:bg-dark-surface text-text-primary hover:bg-surface-elevated dark:hover:bg-dark-surface-elevated'
                }`}
              >
                <span className="flex items-center gap-2">
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                  </svg>
                  Filters
                  {hasActiveFilters && (
                    <span className="bg-red-500 text-white rounded-full w-2 h-2"></span>
                  )}
                </span>
              </button>

              {/* Refresh Button */}
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="px-4 py-2 bg-surface dark:bg-dark-surface hover:bg-surface-elevated dark:hover:bg-dark-surface-elevated text-text-primary rounded-lg font-medium transition-colors disabled:opacity-50"
              >
                <svg className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            </div>
          )}

          {/* Always show refresh button even when filters are hidden */}
          {hideFilters && (
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="px-4 py-2 bg-surface dark:bg-dark-surface hover:bg-surface-elevated dark:hover:bg-dark-surface-elevated text-text-primary rounded-lg font-medium transition-colors disabled:opacity-50"
            >
              <svg className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          )}
        </div>

        {/* Filter Panel */}
        {!hideFilters && showFilters && (
          <div className="mt-4 p-4 bg-surface dark:bg-dark-surface rounded-lg">
            {/* Quick Date Filters */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-text-primary mb-2">
                Quick Date Filters
              </label>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => setQuickDateFilter('week')}
                  className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                    activeQuickFilter === 'week'
                      ? 'bg-primary-blue text-white'
                      : 'bg-primary-blue/10 hover:bg-primary-blue/20 text-primary-blue'
                  }`}
                >
                  Last 7 Days
                </button>
                <button
                  onClick={() => setQuickDateFilter('month')}
                  className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                    activeQuickFilter === 'month'
                      ? 'bg-primary-blue text-white'
                      : 'bg-primary-blue/10 hover:bg-primary-blue/20 text-primary-blue'
                  }`}
                >
                  Last Month
                </button>
                <button
                  onClick={() => setQuickDateFilter('year')}
                  className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                    activeQuickFilter === 'year'
                      ? 'bg-primary-blue text-white'
                      : 'bg-primary-blue/10 hover:bg-primary-blue/20 text-primary-blue'
                  }`}
                >
                  Last Year
                </button>
                <button
                  onClick={clearDateFilter}
                  className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                    activeQuickFilter === 'all'
                      ? 'bg-text-secondary text-white'
                      : 'bg-surface dark:bg-dark-surface hover:bg-surface-elevated dark:hover:bg-dark-surface-elevated text-text-primary'
                  }`}
                >
                  All Time
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-1">
                  Category
                </label>
                <select
                  value={filters.categoryId}
                  onChange={(e) => handleFilterChange({ categoryId: e.target.value })}
                  className="w-full bg-surface dark:bg-dark-surface border border-border dark:border-dark-border rounded-md px-3 py-2 text-text-primary focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Transaction Type Filter */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-1">
                  Type
                </label>
                <select
                  value={filters.transactionType}
                  onChange={(e) => handleFilterChange({
                    transactionType: e.target.value as 'all' | TransactionType
                  })}
                  className="w-full bg-surface dark:bg-dark-surface border border-border dark:border-dark-border rounded-md px-3 py-2 text-text-primary focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                >
                  <option value="all">All Types</option>
                  <option value="income">Income</option>
                  <option value="expense">Expense</option>
                  <option value="transfer">Transfer</option>
                  <option value="investment_buy">Investment Purchase</option>
                  <option value="investment_sell">Investment Sale</option>
                  <option value="dividend">Dividend</option>
                </select>
              </div>

              {/* Start Date Filter */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-1">
                  From Date
                </label>
                <input
                  type="date"
                  value={filters.startDate}
                  onChange={(e) => handleFilterChange({ startDate: e.target.value })}
                  className="w-full bg-surface dark:bg-dark-surface border border-border dark:border-dark-border rounded-md px-3 py-2 text-text-primary focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
              </div>

              {/* End Date Filter */}
              <div>
                <label className="block text-sm font-medium text-text-primary mb-1">
                  To Date
                </label>
                <input
                  type="date"
                  value={filters.endDate}
                  onChange={(e) => handleFilterChange({ endDate: e.target.value })}
                  className="w-full bg-surface dark:bg-dark-surface border border-border dark:border-dark-border rounded-md px-3 py-2 text-text-primary focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
              </div>
            </div>

            {/* Clear Filters */}
            {hasActiveFilters && (
              <div className="mt-4">
                <button
                  onClick={clearFilters}
                  className="text-sm text-primary-blue hover:text-blue-800 font-medium"
                >
                  Clear all filters
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 dark:border-red-600">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading ? (
        <LoadingState message="Loading transactions..." size="md" />
      ) : transactions.length === 0 ? (
        <div className="py-16 px-8 text-center">
          <div className="mx-auto w-24 h-24 bg-surface dark:bg-dark-surface rounded-full flex items-center justify-center mb-6">
            <svg className="w-12 h-12 text-text-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-text-primary mb-2">No transactions found</h3>
          <p className="text-text-secondary max-w-sm mx-auto">
            {hasActiveFilters 
              ? 'Try adjusting your filters or search terms to find more transactions'
              : 'Add your first expense or income to get started!'
            }
          </p>
        </div>
      ) : (
        <>
          {/* Transaction List */}
          <div className="divide-y divide-border dark:divide-dark-border">
            {transactions.map((transaction) => {
              const displayInfo = getTransactionDisplayInfo(transaction)
              return (
                <div
                  key={transaction.id}
                  className="px-0 py-3 hover:bg-surface dark:hover:bg-dark-surface transition-colors group cursor-pointer"
                  onClick={() => {
                    if (onEditTransaction && (transaction.transaction_type === 'income' || transaction.transaction_type === 'expense' || transaction.transaction_type === 'transfer')) {
                      handleEdit(transaction as ITransaction)
                    }
                  }}
                >
                  <div className="flex justify-between items-center ml-4 mr-2">
                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                      <span className="text-xl flex-shrink-0">
                        {displayInfo.icon}
                      </span>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-text-primary truncate">
                            {displayInfo.title}
                          </h3>
                          <span className="text-xs text-text-tertiary ml-2 flex-shrink-0">
                            {formatDate(transaction.transaction_date)}
                          </span>
                        </div>
                        {displayInfo.description && (
                          <p className="text-sm text-text-secondary truncate">
                            {displayInfo.description}
                          </p>
                        )}
                        {/* Show account info for transfers */}
                        {transaction.transaction_type === 'transfer' && (
                          <div className="flex items-center gap-2 text-xs text-text-tertiary mt-1">
                            {(() => {
                              // Handle both ITransaction and ITransferTransaction types
                              const fromAccountName = 'account' in transaction 
                                ? transaction.account?.name 
                                : 'from_account' in transaction 
                                ? transaction.from_account?.name 
                                : 'Unknown'
                              
                              const toAccountName = 'to_account' in transaction 
                                ? transaction.to_account?.name 
                                : 'Unknown'
                              
                              return (
                                <>
                                  <span>{fromAccountName}</span>
                                  <span>→</span>
                                  <span>{toAccountName}</span>
                                </>
                              )
                            })()}
                          </div>
                        )}
                        {/* Show investment details */}
                        {(transaction.transaction_type === 'investment_buy' || transaction.transaction_type === 'investment_sell') && (
                          <div className="text-xs text-text-tertiary mt-1">
                            {transaction.investment_quantity} shares × {formatCurrency(transaction.investment_price || 0)}
                            {transaction.fees && transaction.fees > 0 && (
                              <span className="ml-2">+ {formatCurrency(transaction.fees)} fees</span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 ml-4">
                      <div className="text-right">
                        <p className={`text-base font-semibold ${displayInfo.color}`}>
                          {displayInfo.sign}{formatCurrency(transaction.amount)}
                        </p>
                        {/* Show transaction type badge */}
                        <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                          transaction.transaction_type === 'income' ? 'bg-green-100 text-green-800' :
                          transaction.transaction_type === 'expense' ? 'bg-red-100 text-red-800' :
                          transaction.transaction_type === 'transfer' ? 'bg-blue-100 text-blue-800' :
                          transaction.transaction_type === 'investment_buy' ? 'bg-purple-100 text-purple-800' :
                          transaction.transaction_type === 'investment_sell' ? 'bg-orange-100 text-orange-800' :
                          transaction.transaction_type === 'dividend' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {transaction.transaction_type.replace('_', ' ')}
                        </span>
                      </div>

                      {/* Delete Button */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(transaction.id)
                        }}
                        className="p-1.5 text-text-tertiary hover:text-error-red transition-colors opacity-0 group-hover:opacity-100"
                        title="Delete transaction"
                      >
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {/* Load More */}
          {hasMoreItems && (
            <div ref={loadMoreRef} className="pt-6 text-center border-t border-border dark:border-dark-border">
              {loadingMore ? (
                <LoadingState message="Loading more..." size="sm" className="py-4" />
              ) : (
                <button
                  onClick={handleLoadMore}
                  className="text-primary-blue hover:text-blue-800 font-medium"
                >
                  Load More Transactions
                </button>
              )}
            </div>
          )}
        </>
      )}
    </ContentCard>
  )
}

export default TransactionListWeb