'use client';

import React, { useState, useEffect } from 'react';
import { Check, X, AlertCircle, DollarSign, Info } from 'lucide-react';
import {
  BankStatementData,
  ParsedTransaction,
  ICategory,
  IAccount,
  TransactionService,
  TransferService,
  CategoryService,
  AccountService,
  type TransactionData,
  type ComprehensiveTransactionFormData
} from '@repo/shared';
import { toast } from 'react-hot-toast';

interface ValidationError {
  field: string;
  message: string;
}

interface EnrichedTransaction extends ParsedTransaction {
  id: string;
  enriched_category_id?: string;
  enriched_account_id?: string;
  enriched_to_account_id?: string; // For transfers
  enriched_description?: string;
  enriched_transaction_type?: 'income' | 'expense' | 'transfer';
  validation_errors: ValidationError[];
  is_valid: boolean;
}

interface PDFTransactionPreviewProps {
  data: BankStatementData;
  onImportComplete: () => void;
  onCancel: () => void;
}

export default function PDFTransactionPreview({ 
  data, 
  onImportComplete, 
  onCancel 
}: PDFTransactionPreviewProps) {
  const [transactions, setTransactions] = useState<EnrichedTransaction[]>([]);
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [accounts, setAccounts] = useState<IAccount[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTransactions, setSelectedTransactions] = useState<Set<string>>(new Set());
  const [globalSourceAccount, setGlobalSourceAccount] = useState<string>('');

  useEffect(() => {
    initializeData();
  }, [data]);

  // Re-validate transactions when global source account changes
  useEffect(() => {
    setTransactions(prev => prev.map(transaction => validateTransaction(transaction, globalSourceAccount)));
  }, [globalSourceAccount]);

  const initializeData = async () => {
    try {
      // Load categories and accounts
      const [categoriesData, accountsData] = await Promise.all([
        CategoryService.getCategories({ is_active: true }),
        AccountService.getAccounts({ is_active: true })
      ]);
      
      setCategories(categoriesData);
      setAccounts(accountsData);

      // Convert parsed transactions to enriched format
      const enrichedTransactions: EnrichedTransaction[] = data.transactions.map((transaction, index) => ({
        ...transaction,
        id: `pdf-${index}`,
        validation_errors: [],
        is_valid: false,
        enriched_description: transaction.narration || '',
        // Use Python script's correct categorization:
        // Credit (deposit) = Income, Debit (withdrawal) = Expense
        // Never default to transfer - let user choose manually
        enriched_transaction_type: transaction.type === 'credit' ? 'income' : 'expense',
      }));

      // Auto-assign categories based on narration keywords
      const autoEnrichedTransactions = enrichedTransactions.map(transaction => 
        autoAssignCategory(transaction, categoriesData)
      );

      // Auto-assign accounts based on statement data
      const accountEnrichedTransactions = autoEnrichedTransactions.map(transaction => 
        autoAssignAccount(transaction, accountsData, data)
      );

      // Set global source account from the first transaction's auto-assigned account
      const firstAccount = accountEnrichedTransactions[0]?.enriched_account_id;
      if (firstAccount) {
        setGlobalSourceAccount(firstAccount);
      }

      // Validate all transactions
      const validatedTransactions = accountEnrichedTransactions.map(transaction => validateTransaction(transaction, firstAccount));
      
      setTransactions(validatedTransactions);
      
      // Select all valid transactions by default
      const validTransactionIds = new Set(
        validatedTransactions
          .filter(t => t.is_valid)
          .map(t => t.id)
      );
      setSelectedTransactions(validTransactionIds);
      
    } catch (error) {
      console.error('Failed to initialize transaction preview:', error);
      toast.error('Failed to load transaction data');
    }
  };

  const autoAssignCategory = (transaction: EnrichedTransaction, categories: ICategory[]): EnrichedTransaction => {
    const narration = transaction.narration?.toLowerCase() || '';

    // Define keyword mappings for common categories
    // Note: No transfer mappings - user must manually choose transfer type
    const categoryMappings = [
      { keywords: ['salary', 'pay', 'payroll'], type: 'income', category: 'Salary' },
      { keywords: ['grocery', 'supermarket', 'food', 'restaurant'], type: 'expense', category: 'Food & Dining' },
      { keywords: ['fuel', 'petrol', 'gas station'], type: 'expense', category: 'Transportation' },
      { keywords: ['atm', 'withdrawal'], type: 'expense', category: 'Cash Withdrawal' },
      { keywords: ['dividend', 'interest'], type: 'income', category: 'Investment Income' },
      { keywords: ['utility', 'electricity', 'water'], type: 'expense', category: 'Utilities' },
      { keywords: ['medical', 'hospital', 'pharmacy'], type: 'expense', category: 'Healthcare' },
    ];

    let updatedTransaction = { ...transaction };

    for (const mapping of categoryMappings) {
      if (mapping.keywords.some(keyword => narration.includes(keyword))) {
        // Only assign category if the mapping type matches the transaction type
        if (mapping.type === updatedTransaction.enriched_transaction_type) {
          const matchedCategory = categories.find(
            cat => cat.name.toLowerCase().includes(mapping.category.toLowerCase()) &&
                   cat.type === mapping.type
          );
          if (matchedCategory) {
            updatedTransaction = {
              ...updatedTransaction,
              enriched_category_id: matchedCategory.id,
              // Keep the transaction type from Python script classification
              // Don't override with mapping.type for transfer-like transactions
              enriched_transaction_type: updatedTransaction.enriched_transaction_type,
            };
            break;
          }
        }
      }
    }

    // Special handling for bank transfer terms - categorize them but keep original type
    const transferKeywords = ['neft', 'imps', 'rtgs', 'transfer'];
    if (transferKeywords.some(keyword => narration.includes(keyword))) {
      const transferCategory = categories.find(
        cat => cat.name.toLowerCase().includes('transfer') || 
               cat.name.toLowerCase().includes('bank') &&
               cat.type === updatedTransaction.enriched_transaction_type
      );
      if (transferCategory && !updatedTransaction.enriched_category_id) {
        updatedTransaction.enriched_category_id = transferCategory.id;
      }
    }

    return updatedTransaction;
  };

  const autoAssignAccount = (
    transaction: EnrichedTransaction, 
    accounts: IAccount[], 
    statementData: BankStatementData
  ): EnrichedTransaction => {
    // Try to match account based on various criteria
    let matchedAccount: IAccount | undefined;
    
    // First, try to match by exact account number
    if (statementData.accountNumber) {
      const statementAccountNumber = statementData.accountNumber;
      matchedAccount = accounts.find(account => 
        account.account_number === statementAccountNumber ||
        account.account_number?.includes(statementAccountNumber.slice(-4)) // Last 4 digits
      );
    }
    
    // If no exact match, look for accounts with 'bank' or 'saving' in the name as fallback
    if (!matchedAccount) {
      matchedAccount = accounts.find(account => 
        account.name.toLowerCase().includes('bank') ||
        account.name.toLowerCase().includes('saving') ||
        account.name.toLowerCase().includes('savings')
      );
    }
    
    console.log('Auto-assigned account for transaction:', {
      transactionId: transaction.id,
      accountName: matchedAccount?.name,
      accountId: matchedAccount?.id,
      statementAccount: statementData.accountNumber
    });
    
    return {
      ...transaction,
      enriched_account_id: matchedAccount?.id,
    };
  };

  const validateTransaction = (transaction: EnrichedTransaction, currentGlobalAccount?: string): EnrichedTransaction => {
    const errors: ValidationError[] = [];
    const accountToCheck = currentGlobalAccount || globalSourceAccount;
    let updatedTransaction = { ...transaction };

    // Check required fields
    if (!transaction.enriched_transaction_type) {
      errors.push({ field: 'transaction_type', message: 'Transaction type is required' });
    }

    // Check if category is selected and valid for current transaction type
    if (updatedTransaction.enriched_category_id && updatedTransaction.enriched_transaction_type) {
      const selectedCategory = categories.find(cat => cat.id === updatedTransaction.enriched_category_id);
      if (selectedCategory && selectedCategory.type !== updatedTransaction.enriched_transaction_type) {
        // Clear invalid category that doesn't match transaction type
        updatedTransaction.enriched_category_id = undefined;
      }
    }
    
    if (!updatedTransaction.enriched_category_id || (typeof updatedTransaction.enriched_category_id === 'string' && updatedTransaction.enriched_category_id.trim() === '')) {
      errors.push({ field: 'category', message: 'Category is required' });
    }


    // All transactions use the global source account
    if (!accountToCheck) {
      errors.push({ field: 'account', message: 'Source account must be selected' });
    }

    // For transfers, destination account is required
    if (updatedTransaction.enriched_transaction_type === 'transfer' && !updatedTransaction.enriched_to_account_id) {
      errors.push({ field: 'to_account', message: 'Destination account is required for transfers' });
    }

    if (!updatedTransaction.enriched_description?.trim()) {
      errors.push({ field: 'description', message: 'Description is required' });
    }

    // Validate amounts
    if (!updatedTransaction.withdrawalAmount && !updatedTransaction.depositAmount) {
      errors.push({ field: 'amount', message: 'Amount is required' });
    }

    // Validate date
    if (!updatedTransaction.date) {
      errors.push({ field: 'date', message: 'Date is required' });
    }

    return {
      ...updatedTransaction,
      validation_errors: errors,
      is_valid: errors.length === 0,
    };
  };

  const updateTransaction = (id: string, updates: Partial<EnrichedTransaction>) => {
    setTransactions(prev => prev.map(transaction => {
      if (transaction.id === id) {
        let updated = { ...transaction, ...updates };

        // If transaction type changed, reset category to force user to select appropriate category
        if (updates.enriched_transaction_type && updates.enriched_transaction_type !== transaction.enriched_transaction_type) {
          updated.enriched_category_id = undefined;
        }

        return validateTransaction(updated);
      }
      return transaction;
    }));
  };

  const updateGlobalSourceAccount = (accountId: string) => {
    setGlobalSourceAccount(accountId);
    // Update all transactions with the new source account
    setTransactions(prev => prev.map(transaction => {
      // Apply global source account to all transactions
      return validateTransaction({ 
        ...transaction, 
        enriched_account_id: accountId 
      }, accountId);
    }));
  };

  const toggleTransactionSelection = (id: string) => {
    setSelectedTransactions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const selectAllValid = () => {
    const validIds = transactions
      .filter(t => t.is_valid)
      .map(t => t.id);
    setSelectedTransactions(new Set(validIds));
  };

  const deselectAll = () => {
    setSelectedTransactions(new Set());
  };

  const handleSubmit = async () => {
    const selectedValidTransactions = transactions.filter(
      t => selectedTransactions.has(t.id) && t.is_valid
    );

    if (selectedValidTransactions.length === 0) {
      toast.error('Please select at least one valid transaction to import');
      return;
    }

    setIsSubmitting(true);
    try {
      for (const transaction of selectedValidTransactions) {
        if (transaction.enriched_transaction_type === 'transfer') {
          // Handle transfers using TransferService
          const transferData = {
            from_account_id: globalSourceAccount || transaction.enriched_account_id!,
            to_account_id: transaction.enriched_to_account_id!,
            amount: transaction.withdrawalAmount || transaction.depositAmount || 0,
            description: transaction.enriched_description || transaction.narration || '',
            category_id: transaction.enriched_category_id!,
            transaction_date: transaction.date,
            fees: 0,
            is_internal: true, // Default to internal transfer
          };

          await TransferService.createTransfer(transferData);
        } else {
          // Handle regular income/expense transactions
          const transactionData: TransactionData & { account_id?: string } = {
            amount: transaction.withdrawalAmount || transaction.depositAmount || 0,
            description: transaction.enriched_description || transaction.narration || '',
            category_id: transaction.enriched_category_id!,
            account_id: globalSourceAccount || transaction.enriched_account_id!,
            transaction_date: new Date(transaction.date),
            transaction_type: transaction.enriched_transaction_type || (transaction.type === 'credit' ? 'income' : 'expense'),
          };

          await TransactionService.createTransaction(transactionData);
        }
      }

      toast.success(`Successfully imported ${selectedValidTransactions.length} transactions!`);
      onImportComplete();
    } catch (error) {
      console.error('Failed to import transactions:', error);
      toast.error('Failed to import transactions. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number | null) => {
    if (amount === null) return '-';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  };


  const validTransactionsCount = transactions.filter(t => t.is_valid).length;
  const selectedValidCount = transactions.filter(t => selectedTransactions.has(t.id) && t.is_valid).length;
  const hasTransferTransactions = transactions.some(t => t.enriched_transaction_type === 'transfer');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
          PDF Transaction Preview
        </h3>
        <p className="text-blue-700 dark:text-blue-300 text-sm">
          Review and edit the extracted transactions below. Fill in missing information and select which transactions to import.
        </p>
        <div className="mt-3 flex flex-wrap gap-4 text-sm">
          <span className="text-blue-700 dark:text-blue-300">
            <span className="font-medium">{data.transactions.length}</span> transactions extracted
          </span>
          <span className="text-green-700 dark:text-green-300">
            <span className="font-medium">{validTransactionsCount}</span> ready to import
          </span>
          <span className="text-purple-700 dark:text-purple-300">
            <span className="font-medium">{selectedValidCount}</span> selected
          </span>
        </div>
      </div>

      {/* Source Account Selection */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-blue-600" />
            <label className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Source Account:
            </label>
          </div>
          <div className="flex-1 max-w-md">
            <select
              value={globalSourceAccount}
              onChange={(e) => updateGlobalSourceAccount(e.target.value)}
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700"
            >
              <option value="">Select source account</option>
              {accounts.map(account => (
                <option key={account.id} value={account.id}>
                  {account.name}
                </option>
              ))}
            </select>
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            This account will be applied to all transactions from this PDF
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      <div className="flex flex-wrap gap-3">
        <button
          onClick={selectAllValid}
          className="px-4 py-2 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-800 transition-colors text-sm font-medium cursor-pointer"
        >
          Select All Valid ({validTransactionsCount})
        </button>
        <button
          onClick={deselectAll}
          className="px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors text-sm font-medium cursor-pointer"
        >
          Deselect All
        </button>
      </div>

      {/* Transactions Table */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
              <tr>
                <th className="w-12 px-4 py-3 text-left">
                  <span className="sr-only">Select</span>
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">Date</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">Description</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">Amount</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">Type</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">Category</th>
                {hasTransferTransactions && (
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">To Account</th>
                )}
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">Status</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
              {transactions.map((transaction, index) => (
                <tr key={transaction.id} className={`${transaction.is_valid ? 'bg-white dark:bg-gray-800' : 'bg-red-50 dark:bg-red-950'}`}>
                  {/* Selection Checkbox */}
                  <td className="px-4 py-3">
                    <input
                      type="checkbox"
                      checked={selectedTransactions.has(transaction.id)}
                      onChange={() => toggleTransactionSelection(transaction.id)}
                      disabled={!transaction.is_valid}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                  </td>

                  {/* Date */}
                  <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                    {new Date(transaction.date).toLocaleDateString('en-IN')}
                  </td>

                  {/* Description */}
                  <td className="px-4 py-3">
                    <input
                      type="text"
                      value={transaction.enriched_description || ''}
                      onChange={(e) => updateTransaction(transaction.id, { 
                        enriched_description: e.target.value 
                      })}
                      className={`w-full px-3 py-2 text-sm border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 ${
                        transaction.validation_errors.some(e => e.field === 'description') 
                          ? 'border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-950' 
                          : 'border-gray-300 dark:border-gray-600'
                      }`}
                      placeholder="Enter description"
                    />
                  </td>

                  {/* Amount */}
                  <td className="px-4 py-3 text-sm whitespace-nowrap">
                    <span className={`font-medium ${
                      transaction.enriched_transaction_type === 'income' ? 'text-green-600' :
                      transaction.enriched_transaction_type === 'expense' ? 'text-red-600' :
                      'text-blue-600' // transfer
                    }`}>
                      {transaction.enriched_transaction_type === 'income' ? '+' :
                       transaction.enriched_transaction_type === 'expense' ? '-' :
                       '→'} {/* transfer arrow */}
                      {formatCurrency(transaction.withdrawalAmount || transaction.depositAmount)}
                    </span>
                  </td>

                  {/* Transaction Type */}
                  <td className="px-4 py-3">
                    <select
                      value={transaction.enriched_transaction_type || ''}
                      onChange={(e) => updateTransaction(transaction.id, {
                        enriched_transaction_type: e.target.value as 'income' | 'expense' | 'transfer' || undefined
                      })}
                      className={`w-full px-3 py-2 text-sm border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 ${
                        transaction.validation_errors.some(e => e.field === 'transaction_type')
                          ? 'border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-950'
                          : 'border-gray-300 dark:border-gray-600'
                      }`}
                    >
                      <option value="">Select type</option>
                      <option value="income">Income</option>
                      <option value="expense">Expense</option>
                      <option value="transfer">Transfer</option>
                    </select>
                  </td>

                  {/* Category */}
                  <td className="px-4 py-3">
                    <select
                      value={transaction.enriched_category_id || ''}
                      onChange={(e) => updateTransaction(transaction.id, {
                        enriched_category_id: e.target.value || undefined
                      })}
                      className={`w-full px-3 py-2 text-sm border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 ${
                        transaction.validation_errors.some(e => e.field === 'category')
                          ? 'border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-950'
                          : 'border-gray-300 dark:border-gray-600'
                      }`}
                    >
                      <option value="">Select category</option>
                      {categories
                        .filter(cat => cat.type === (transaction.enriched_transaction_type || (transaction.type === 'credit' ? 'income' : 'expense')))
                        .map(category => (
                          <option key={category.id} value={category.id}>
                            {category.name}
                          </option>
                        ))}
                    </select>
                  </td>


                  {/* To Account (for transfers) */}
                  {hasTransferTransactions && (
                    <td className="px-4 py-3">
                      {transaction.enriched_transaction_type === 'transfer' ? (
                        <select
                          value={transaction.enriched_to_account_id || ''}
                          onChange={(e) => updateTransaction(transaction.id, {
                            enriched_to_account_id: e.target.value || undefined
                          })}
                          className={`w-full px-3 py-2 text-sm border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 ${
                            transaction.validation_errors.some(e => e.field === 'to_account')
                              ? 'border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-950'
                              : 'border-gray-300 dark:border-gray-600'
                          }`}
                        >
                          <option value="">Select destination</option>
                          {accounts
                            .filter(account => account.id !== globalSourceAccount)
                            .map(account => (
                              <option key={account.id} value={account.id}>
                                {account.name}
                              </option>
                            ))}
                        </select>
                      ) : (
                        <span className="text-gray-400 dark:text-gray-500 text-sm">N/A</span>
                      )}
                    </td>
                  )}

                  {/* Status */}
                  <td className="px-4 py-3">
                    {transaction.is_valid ? (
                      <div className="flex items-center justify-center">
                        <Check className="w-4 h-4 text-green-600" />
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <div
                          className="relative group cursor-help"
                          title={transaction.validation_errors.map(e => e.message).join(', ')}
                        >
                          <Info className="w-4 h-4 text-red-600" />
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                            {transaction.validation_errors.map(e => e.message).join(', ')}
                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                          </div>
                        </div>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>



      {/* Action Buttons */}
      <div className="flex justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={onCancel}
          className="px-6 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors font-medium cursor-pointer"
        >
          Cancel
        </button>
        
        <div className="flex gap-3">
          <div className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
            {selectedValidCount} of {validTransactionsCount} transactions selected
          </div>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || selectedValidCount === 0}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed transition-colors font-medium cursor-pointer"
          >
            {isSubmitting ? 'Importing...' : `Import ${selectedValidCount} Transactions`}
          </button>
        </div>
      </div>
    </div>
  );
}