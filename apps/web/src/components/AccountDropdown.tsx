'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '../contexts/AuthContext'
import { useProfile } from '../contexts/ProfileContext'

interface AccountDropdownProps {
  isCollapsed?: boolean
}

export default function AccountDropdown({ isCollapsed = false }: AccountDropdownProps) {
  const { user, signOut } = useAuth()
  const { profile } = useProfile()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const handleSignOut = async () => {
    try {
      await signOut()
      setIsOpen(false)
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }


  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Get user initials for avatar
  const getUserInitials = () => {
    const name = profile?.display_name || user?.user_metadata?.name || user?.email || ''
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // Avatar component for reuse
  const AvatarComponent = ({ size = 'w-8 h-8' }: { size?: string }) => {
    if (profile?.avatar_url) {
      return (
        <img
          src={profile.avatar_url}
          alt="Profile"
          className={`${size} rounded-full object-cover border-2 border-border-light`}
        />
      )
    }
    
    return (
      <div className={`${size} bg-gradient-to-r from-primary-blue to-primary-purple rounded-full flex items-center justify-center text-white text-sm font-semibold`}>
        {getUserInitials()}
      </div>
    )
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Account Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`w-full flex items-center rounded-lg text-text-primary hover:bg-surface transition-all duration-200 ${
          isCollapsed 
            ? 'justify-center p-2' 
            : 'px-3 py-2'
        }`}
      >
        {/* Avatar */}
        <AvatarComponent />
        
        {/* User name/email - hidden when collapsed */}
        {!isCollapsed && (
          <div className="flex items-center min-w-0 flex-1 ml-3">
            <span className="text-sm font-medium truncate">
              {profile?.display_name || user?.user_metadata?.name || user?.email}
            </span>
          </div>
        )}

        {/* Dropdown arrow - hidden when collapsed */}
        {!isCollapsed && (
          <div className="ml-2 flex-shrink-0">
            <svg 
              className={`w-4 h-4 text-text-secondary transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        )}
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className={`absolute bottom-full mb-2 w-64 bg-surface-elevated border border-border rounded-xl shadow-lg z-50 overflow-hidden left-0`}>
          {/* User Info Section */}
          <div className="px-4 py-3 border-b border-border bg-surface">
            <div className="flex items-center gap-3">
              <AvatarComponent size="w-10 h-10" />
              <div className="flex-1 min-w-0">
                {(profile?.display_name || user?.user_metadata?.name) && (
                  <p className="text-sm font-semibold text-text-primary truncate">
                    {profile?.display_name || user?.user_metadata?.name}
                  </p>
                )}
                <p className="text-xs text-text-secondary truncate">
                  {user?.email}
                </p>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-2">
            {/* Profile Link */}
            <Link
              href="/profile"
              onClick={() => setIsOpen(false)}
              className="flex items-center gap-3 px-4 py-2 text-sm text-text-primary hover:bg-surface transition-colors rounded-lg mx-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              Profile Settings
            </Link>


            {/* Sign Out */}
            <button
              onClick={handleSignOut}
              className="w-full flex items-center gap-3 px-4 py-2 text-sm text-error-red hover:bg-error-red/10 transition-colors rounded-lg mx-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Sign Out
            </button>
          </div>
        </div>
      )}
    </div>
  )
}