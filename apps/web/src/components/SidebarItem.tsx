'use client'

import Link from 'next/link'

interface SidebarItemProps {
  href: string
  icon: React.ReactNode
  label: string
  isActive: boolean
  isCollapsed: boolean
  isChild?: boolean
  onClick?: () => void
}

export default function SidebarItem({
  href,
  icon,
  label,
  isActive,
  isCollapsed,
  isChild = false,
  onClick
}: SidebarItemProps) {
  const baseClasses = `
    flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200
    ${isCollapsed ? 'justify-center' : 'justify-start'}
    ${isChild && !isCollapsed ? 'ml-2 pl-4' : ''}
  `

  const activeClasses = isActive
    ? 'bg-gradient-to-r from-primary-blue to-primary-purple text-white shadow-md'
    : 'text-text-secondary hover:text-text-primary hover:bg-surface'

  return (
    <Link
      href={href}
      onClick={onClick}
      className={`${baseClasses} ${activeClasses}`}
      title={isCollapsed ? label : undefined}
    >
      <div className="flex-shrink-0">
        {icon}
      </div>
      {!isCollapsed && (
        <span className="text-sm font-medium truncate">
          {label}
        </span>
      )}
    </Link>
  )
}