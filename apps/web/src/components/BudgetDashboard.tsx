import React, { useState } from 'react'
import { BudgetForm } from './BudgetForm'
import { BudgetList } from './BudgetList'
import { Modal } from './Modal'
import type { IBudget } from '@repo/shared'

interface BudgetDashboardProps {
  showForm: boolean
  setShowForm: (show: boolean) => void
}

export const BudgetDashboard: React.FC<BudgetDashboardProps> = ({ showForm, setShowForm }) => {
  const [editingBudget, setEditingBudget] = useState<IBudget | null>(null)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const handleCreateNew = () => {
    setEditingBudget(null)
    setShowForm(true)
  }

  const handleEdit = (budget: IBudget) => {
    setEditingBudget(budget)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingBudget(null)
    setRefreshTrigger(prev => prev + 1)
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingBudget(null)
  }

  const handleDelete = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <div className="space-y-8">
      <BudgetList
        onEdit={handleEdit}
        onDelete={handleDelete}
        refreshTrigger={refreshTrigger}
      />

      {/* Budget Form Modal */}
      <Modal
        isOpen={showForm}
        onClose={handleFormCancel}
        title={editingBudget ? "Edit Budget" : "Create Budget"}
        size="md"
      >
        <BudgetForm
          budget={editingBudget || undefined}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      </Modal>
    </div>
  )
}