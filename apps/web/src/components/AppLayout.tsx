'use client'

import { useState } from 'react'
import { Menu } from 'lucide-react'
import Sidebar from './Sidebar'
import { SidebarContext, useSidebarState } from '../hooks/useSidebar'

interface AppLayoutProps {
  children: React.ReactNode
}

export default function AppLayout({ children }: AppLayoutProps) {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)
  const sidebarState = useSidebarState()

  // Don't render until sidebar state is loaded
  if (!sidebarState.isLoaded) {
    return null
  }

  return (
    <SidebarContext.Provider value={sidebarState}>
      <div className="min-h-screen bg-background">
        {/* Sidebar */}
        <Sidebar 
          isMobileOpen={isMobileSidebarOpen}
          onMobileClose={() => setIsMobileSidebarOpen(false)}
        />

        {/* Main Content */}
        <div className={`min-h-screen transition-all duration-300 ease-in-out ${
          sidebarState.isCollapsed ? 'lg:ml-16' : 'lg:ml-64'
        }`}>
        {/* Mobile Header */}
        <div className="lg:hidden bg-surface-elevated border-b border-border-light p-4 flex items-center justify-between">
          <button
            onClick={() => setIsMobileSidebarOpen(true)}
            className="p-2 rounded-lg hover:bg-surface transition-colors"
          >
            <Menu className="w-6 h-6" />
          </button>
          
          <div className="flex items-center gap-2">
            <div className="w-6 h-6">
              <img 
                src="/portfolio-tracker-icon.svg" 
                alt="Portfolio Tracker" 
                className="w-full h-full"
              />
            </div>
            <span className="font-semibold text-text-primary">Portfolio Tracker</span>
          </div>
          
          <div className="w-10" /> {/* Spacer for centering */}
        </div>

        {/* Page Content */}
        <main className="relative">
          {children}
        </main>
        </div>
      </div>
    </SidebarContext.Provider>
  )
}