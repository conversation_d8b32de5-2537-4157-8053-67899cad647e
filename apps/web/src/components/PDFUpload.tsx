'use client';

import React, { useState, useCallback, useRef } from 'react';
import { BankStatementData, StatementExporter } from '@repo/shared';
import PDFTransactionPreview from './PDFTransactionPreview';

interface PDFUploadProps {
  onTransactionsExtracted?: (data: BankStatementData) => void;
  className?: string;
}

interface UploadState {
  isUploading: boolean;
  progress: number;
  error: string | null;
  data: BankStatementData | null;
  showPreview: boolean;
}

export default function PDFUpload({ onTransactionsExtracted, className = '' }: PDFUploadProps) {
  const [state, setState] = useState<UploadState>({
    isUploading: false,
    progress: 0,
    error: null,
    data: null,
    showPreview: false
  });
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleFileSelect = useCallback(async (file: File) => {
    if (!file) return;
    
    setState(prev => ({
      ...prev,
      isUploading: true,
      progress: 0,
      error: null,
      data: null
    }));
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      setState(prev => ({ ...prev, progress: 30 }));
      
      const response = await fetch('/api/pdf/parse', {
        method: 'POST',
        body: formData
      });
      
      setState(prev => ({ ...prev, progress: 70 }));
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to parse PDF');
      }
      
      setState(prev => ({ ...prev, progress: 100 }));
      
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          isUploading: false,
          data: result.data,
          showPreview: false // Start with summary view
        }));
      }, 500);
      
    } catch (error) {
      setState(prev => ({
        ...prev,
        isUploading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  }, [onTransactionsExtracted]);
  
  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    const pdfFile = files.find(file => file.type === 'application/pdf');
    
    if (pdfFile) {
      handleFileSelect(pdfFile);
    } else {
      setState(prev => ({
        ...prev,
        error: 'Please upload a PDF file'
      }));
    }
  }, [handleFileSelect]);
  
  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  }, []);
  
  const handleFileInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);
  
  const handleDownloadText = () => {
    if (state.data) {
      StatementExporter.downloadTextFile(state.data);
    }
  };
  
  const handleDownloadCSV = () => {
    if (state.data) {
      // Use raw CSV content if available, otherwise generate CSV
      if (state.data.rawCsvContent) {
        StatementExporter.downloadRawCSVFile(state.data.rawCsvContent);
      } else {
        StatementExporter.downloadCSVFile(state.data.transactions);
      }
    }
  };
  
  const handleDownloadExcel = () => {
    if (state.data) {
      StatementExporter.downloadExcelFile(state.data);
    }
  };
  
  const resetUpload = () => {
    setState({
      isUploading: false,
      progress: 0,
      error: null,
      data: null,
      showPreview: false
    });
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleShowPreview = () => {
    setState(prev => ({ ...prev, showPreview: true }));
  };

  const handlePreviewCancel = () => {
    setState(prev => ({ ...prev, showPreview: false }));
  };

  const handleImportComplete = () => {
    if (onTransactionsExtracted && state.data) {
      onTransactionsExtracted(state.data);
    }
    resetUpload();
  };
  
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Upload Area */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onClick={() => fileInputRef.current?.click()}
        className={`
          border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300 relative overflow-hidden cursor-pointer
          ${state.isUploading ? 'border-blue-400 bg-blue-50 dark:border-blue-400 dark:bg-blue-950/30' : 'border-gray-300 hover:border-blue-400 dark:border-gray-600 dark:hover:border-blue-400'}
          ${state.error ? 'border-red-400 bg-red-50 dark:border-red-400 dark:bg-red-950/30' : ''}
        `}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".pdf"
          onChange={handleFileInputChange}
          className="hidden"
        />
        
        {state.isUploading ? (
          <div className="space-y-4">
            <div className="text-lg font-medium text-blue-600 dark:text-blue-400">
              Processing PDF...
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-600 dark:bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${state.progress}%` }}
              />
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {state.progress}% Complete
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Modern icon with gradient background */}
            <div className="flex justify-center">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-105 transition-transform duration-200">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            
            <div className="text-center space-y-4">
              <h3 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                Upload Bank Statement PDF
              </h3>
              <p className="max-w-md mx-auto leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                Upload your bank statement PDF for automatic transaction extraction
              </p>
              
              <div className="pt-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    fileInputRef.current?.click();
                  }}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center gap-2 mx-auto cursor-pointer"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  Choose PDF File
                </button>
              </div>
              
              <p className="text-xs mt-3" style={{ color: 'var(--text-tertiary)' }}>
                Drag and drop your PDF here or click to browse
              </p>
            </div>
          </div>
        )}
      </div>
      
      {/* Error Display */}
      {state.error && (
        <div className="bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <div className="text-red-400 mr-3">⚠️</div>
            <div>
              <h4 className="text-red-800 dark:text-red-200 font-medium">Upload Error</h4>
              <p className="text-red-600 dark:text-red-300">{state.error}</p>
            </div>
          </div>
          <button
            onClick={resetUpload}
            className="mt-3 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 underline"
          >
            Try Again
          </button>
        </div>
      )}
      
      {/* Success and Results */}
      {state.data && !state.showPreview && (
        <div className="bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <div className="text-green-400 mr-3 text-2xl">✅</div>
            <div>
              <h4 className="text-green-800 dark:text-green-200 font-medium text-lg">
                PDF Processed Successfully!
              </h4>
              <p className="text-green-600 dark:text-green-300">
                Extracted {state.data.transactions.length} transactions
              </p>
            </div>
          </div>
          
          {/* Statement Summary */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-4">
            <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-3">Statement Summary</h5>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-gray-600 dark:text-gray-400">Account Holder</div>
                <div className="font-medium dark:text-gray-100">{state.data.accountHolder}</div>
              </div>
              <div>
                <div className="text-gray-600 dark:text-gray-400">Account Number</div>
                <div className="font-medium dark:text-gray-100">{state.data.accountNumber}</div>
              </div>
              <div>
                <div className="text-gray-600 dark:text-gray-400">Period</div>
                <div className="font-medium dark:text-gray-100">
                  {state.data.statementPeriod.from} to {state.data.statementPeriod.to}
                </div>
              </div>
              <div>
                <div className="text-gray-600 dark:text-gray-400">Transactions</div>
                <div className="font-medium dark:text-gray-100">{state.data.transactions.length}</div>
              </div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="space-y-4">
            <div className="flex flex-wrap gap-3">
              <button
                onClick={handleShowPreview}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors font-medium"
              >
                📊 Import Transactions
              </button>
            </div>
            
            {/* Download Options */}
            <div className="space-y-3">
              <h5 className="font-medium text-gray-900 dark:text-gray-100">Or Download Raw Data</h5>
              <div className="flex flex-wrap gap-3">
                <button
                  onClick={handleDownloadText}
                  className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 dark:bg-gray-500 dark:hover:bg-gray-600 transition-colors text-sm"
                >
                  📄 Download Text
                </button>
                <button
                  onClick={handleDownloadCSV}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 transition-colors text-sm"
                >
                  📊 Download CSV
                </button>
                <button
                  onClick={handleDownloadExcel}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors text-sm"
                >
                  📈 Download Excel
                </button>
              </div>
            </div>
          </div>
          
          {/* Reset Button */}
          <button
            onClick={resetUpload}
            className="mt-4 text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 underline text-sm"
          >
            Upload Another Statement
          </button>
        </div>
      )}

      {/* Transaction Preview */}
      {state.data && state.showPreview && (
        <PDFTransactionPreview
          data={state.data}
          onImportComplete={handleImportComplete}
          onCancel={handlePreviewCancel}
        />
      )}
    </div>
  );
}
