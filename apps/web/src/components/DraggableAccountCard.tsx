'use client'

import React from 'react'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { useRouter } from 'next/navigation'
import { type IAccount } from '@shared/index'
import { ContentCard } from './ContentCard'
import { Edit2, Trash2, GripVertical, Building2, TrendingUp, DollarSign, CreditCard, Wallet, Home } from 'lucide-react'
import { useCurrencyStore } from '@shared/index'

interface DraggableAccountCardProps {
  account: IAccount
  onEdit: (account: IAccount) => void
  onDelete: (account: IAccount) => void
}

export function DraggableAccountCard({ account, onEdit, onDelete }: DraggableAccountCardProps) {
  const router = useRouter()
  const { formatCurrency } = useCurrencyStore()
  
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
    isOver,
  } = useSortable({ id: account.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isDragging ? 'none' : transition || 'transform 200ms ease-out',
  }

  const getAccountTypeIcon = (type: string) => {
    switch (type) {
      case 'bank':
        return Building2
      case 'investment':
        return TrendingUp
      case 'savings':
        return DollarSign
      case 'credit_card':
        return CreditCard
      case 'cash':
        return Wallet
      case 'loan':
        return Home
      default:
        return Building2
    }
  }

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'bank':
        return 'bg-primary-blue/10 text-primary-blue'
      case 'investment':
        return 'bg-success-green/10 text-success-green'
      case 'savings':
        return 'bg-primary-purple/10 text-primary-purple'
      case 'credit_card':
        return 'bg-error-red/10 text-error-red'
      case 'cash':
        return 'bg-warning-orange/10 text-warning-orange'
      case 'loan':
        return 'bg-warning-orange/10 text-warning-orange'
      default:
        return 'bg-text-secondary/10 text-text-secondary'
    }
  }

  const AccountIcon = getAccountTypeIcon(account.account_type)

  return (
    <div 
      ref={setNodeRef} 
      style={style} 
      className={`
        w-full min-w-0
        ${isDragging ? 'opacity-30 scale-95 pointer-events-none' : 'opacity-100 scale-100'}
        ${isOver && !isDragging ? 'scale-105' : ''}
        transition-all duration-300 ease-out
      `}
    >
      <ContentCard
        hoverable={!isDragging}
        clickable={!isDragging}
        onClick={() => !isDragging && router.push(`/accounts/${account.id}`)}
        className={`
          border border-border relative overflow-hidden
          ${isDragging ? 'border-primary-blue/50 bg-surface-elevated/80' : ''}
          ${isOver && !isDragging ? 'shadow-lg border-primary-blue/40 bg-primary-blue/5' : ''}
          transition-all duration-200 ease-out
        `}
      >
        {/* Drag Handle */}
        <div
          {...attributes}
          {...listeners}
          className={`
            absolute top-3 left-3 p-1 rounded-md
            cursor-grab active:cursor-grabbing touch-none
            text-text-tertiary hover:text-primary-blue hover:bg-primary-blue/10
            transition-all duration-200 ease-out
            ${isDragging ? 'text-primary-blue bg-primary-blue/20' : ''}
            group flex items-center justify-center
          `}
          onClick={(e) => e.stopPropagation()}
          title="Drag to reorder"
        >
          <GripVertical className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" />
        </div>


        <div className="flex items-start justify-between mb-4 ml-8">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-surface rounded-lg flex items-center justify-center">
              <AccountIcon className="w-5 h-5 text-text-secondary" />
            </div>
            <div>
              <h3 className="font-semibold text-text-primary">{account.name}</h3>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getAccountTypeColor(account.account_type)}`}>
                {account.account_type.replace('_', ' ')}
              </span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onEdit(account)
              }}
              className="text-text-secondary hover:text-primary-blue transition-colors"
              title="Edit account"
            >
              <Edit2 className="w-4 h-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onDelete(account)
              }}
              className="text-text-secondary hover:text-error-red transition-colors"
              title="Delete account"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-text-secondary">Current Balance</span>
            <span className="text-lg font-bold text-text-primary">
              {formatCurrency(account.current_balance || 0)}
            </span>
          </div>
          
          {account.institution_name && (
            <div className="flex justify-between items-center">
              <span className="text-sm text-text-secondary">Institution</span>
              <span className="text-sm text-text-primary">{account.institution_name}</span>
            </div>
          )}
          
          {account.account_number && (
            <div className="flex justify-between items-center">
              <span className="text-sm text-text-secondary">Account Number</span>
              <span className="text-sm text-text-primary font-mono">
                ****{account.account_number.slice(-4)}
              </span>
            </div>
          )}
          
          <div className="flex justify-between items-center">
            <span className="text-sm text-text-secondary">Status</span>
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              account.is_active
                ? 'bg-success-green/10 text-success-green'
                : 'bg-error-red/10 text-error-red'
            }`}>
              {account.is_active ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
      </ContentCard>
    </div>
  )
}