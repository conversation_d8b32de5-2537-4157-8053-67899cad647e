import React from 'react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  color?: string
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className = '',
  color = 'primary-blue'
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-4 w-4 border-2'
      case 'lg':
        return 'h-10 w-10 border-4'
      case 'xl':
        return 'h-12 w-12 border-4'
      default:
        return 'h-8 w-8 border-4'
    }
  }

  const getBorderColor = () => {
    switch (color) {
      case 'white':
        return 'border-white/20 border-t-white'
      case 'gray':
        return 'border-border border-t-text-secondary'
      default:
        return 'border-border border-t-primary-blue'
    }
  }

  return (
    <div 
      className={`animate-spin rounded-full ${getSizeClasses()} ${getBorderColor()} ${className}`}
      role="status"
      aria-label="Loading"
    >
      <span className="sr-only">Loading...</span>
    </div>
  )
}

interface LoadingStateProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  message?: string
  children?: React.ReactNode
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  size = 'md',
  className = '',
  message = 'Loading...',
  children
}) => {
  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <LoadingSpinner size={size} />
      {message && (
        <p className="mt-3 text-sm text-text-secondary">{message}</p>
      )}
      {children}
    </div>
  )
}

export default LoadingSpinner