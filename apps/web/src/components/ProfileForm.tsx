import React, { useState, useEffect } from 'react'
import { useProfile } from '../contexts/ProfileContext'
import { IUserProfile } from '@repo/shared'
import { LoadingState } from './LoadingState'

export interface ProfileFormProps {
  onSave?: () => void
  className?: string
}

const COUNTRIES = [
  { code: 'US', name: 'United States' },
  { code: 'CA', name: 'Canada' },
  { code: 'GB', name: 'United Kingdom' },
  { code: 'IN', name: 'India' },
  { code: 'JP', name: 'Japan' },
  { code: 'AU', name: 'Australia' },
  { code: 'DE', name: 'Germany' },
  { code: 'FR', name: 'France' },
  { code: 'IT', name: 'Italy' },
  { code: 'ES', name: 'Spain' },
  { code: 'NL', name: 'Netherlands' },
  { code: 'AT', name: 'Austria' },
  { code: 'BE', name: 'Belgium' },
  { code: 'FI', name: 'Finland' },
  { code: 'IE', name: 'Ireland' },
  { code: 'PT', name: 'Portugal' },
  { code: 'CH', name: 'Switzerland' },
  { code: 'SE', name: 'Sweden' },
  { code: 'NO', name: 'Norway' },
  { code: 'DK', name: 'Denmark' },
  { code: 'SG', name: 'Singapore' },
  { code: 'HK', name: 'Hong Kong' },
  { code: 'CN', name: 'China' },
  { code: 'KR', name: 'South Korea' },
  { code: 'BR', name: 'Brazil' },
  { code: 'MX', name: 'Mexico' },
  { code: 'AR', name: 'Argentina' },
  { code: 'ZA', name: 'South Africa' },
  { code: 'RU', name: 'Russia' },
  { code: 'AE', name: 'United Arab Emirates' },
  { code: 'SA', name: 'Saudi Arabia' },
  { code: 'TR', name: 'Turkey' },
  { code: 'IL', name: 'Israel' },
  { code: 'EG', name: 'Egypt' },
  { code: 'NG', name: 'Nigeria' },
  { code: 'KE', name: 'Kenya' },
  { code: 'TH', name: 'Thailand' },
  { code: 'MY', name: 'Malaysia' },
  { code: 'ID', name: 'Indonesia' },
  { code: 'PH', name: 'Philippines' },
  { code: 'VN', name: 'Vietnam' },
  { code: 'BD', name: 'Bangladesh' },
  { code: 'PK', name: 'Pakistan' },
  { code: 'LK', name: 'Sri Lanka' },
  { code: 'NZ', name: 'New Zealand' },
]

const CURRENCIES = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
  { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF' },
  { code: 'SEK', name: 'Swedish Krona', symbol: 'kr' },
  { code: 'NOK', name: 'Norwegian Krone', symbol: 'kr' },
  { code: 'DKK', name: 'Danish Krone', symbol: 'kr' },
  { code: 'SGD', name: 'Singapore Dollar', symbol: 'S$' },
  { code: 'HKD', name: 'Hong Kong Dollar', symbol: 'HK$' },
  { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
  { code: 'KRW', name: 'South Korean Won', symbol: '₩' },
  { code: 'BRL', name: 'Brazilian Real', symbol: 'R$' },
  { code: 'MXN', name: 'Mexican Peso', symbol: '$' },
  { code: 'ARS', name: 'Argentine Peso', symbol: '$' },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R' },
  { code: 'RUB', name: 'Russian Ruble', symbol: '₽' },
  { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ' },
  { code: 'SAR', name: 'Saudi Riyal', symbol: '﷼' },
  { code: 'TRY', name: 'Turkish Lira', symbol: '₺' },
  { code: 'ILS', name: 'Israeli Shekel', symbol: '₪' },
  { code: 'EGP', name: 'Egyptian Pound', symbol: '£' },
  { code: 'NGN', name: 'Nigerian Naira', symbol: '₦' },
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh' },
  { code: 'THB', name: 'Thai Baht', symbol: '฿' },
  { code: 'MYR', name: 'Malaysian Ringgit', symbol: 'RM' },
  { code: 'IDR', name: 'Indonesian Rupiah', symbol: 'Rp' },
  { code: 'PHP', name: 'Philippine Peso', symbol: '₱' },
  { code: 'VND', name: 'Vietnamese Dong', symbol: '₫' },
  { code: 'BDT', name: 'Bangladeshi Taka', symbol: '৳' },
  { code: 'PKR', name: 'Pakistani Rupee', symbol: '₨' },
  { code: 'LKR', name: 'Sri Lankan Rupee', symbol: '₨' },
  { code: 'NZD', name: 'New Zealand Dollar', symbol: 'NZ$' },
]

export function ProfileForm({ onSave, className = '' }: ProfileFormProps) {
  const { profile, updateProfile, uploadAvatar, loading } = useProfile()
  const [formData, setFormData] = useState({
    display_name: profile?.display_name || '',
    currency_preference: profile?.currency_preference || 'USD',
    country: profile?.country || '',
    notification_preferences: {
      email_notifications: profile?.notification_preferences?.email_notifications ?? true,
      push_notifications: profile?.notification_preferences?.push_notifications ?? true,
      budget_alerts: profile?.notification_preferences?.budget_alerts ?? true,
      weekly_summary: profile?.notification_preferences?.weekly_summary ?? true,
    },
  })
  const [avatarFile, setAvatarFile] = useState<File | null>(null)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  // Update form data when profile loads
  useEffect(() => {
    if (profile) {
      setFormData({
        display_name: profile.display_name || '',
        currency_preference: profile.currency_preference || 'USD',
        country: profile.country || '',
        notification_preferences: {
          email_notifications: profile.notification_preferences?.email_notifications ?? true,
          push_notifications: profile.notification_preferences?.push_notifications ?? true,
          budget_alerts: profile.notification_preferences?.budget_alerts ?? true,
          weekly_summary: profile.notification_preferences?.weekly_summary ?? true,
        },
      })
    }
  }, [profile])

  // Detect user's country on first load (without affecting currency)
  useEffect(() => {
    if (!profile?.country && !formData.country) {
      // Try to detect country using browser's locale
      const userLocale = navigator.language || 'en-US'
      const countryCode = userLocale.split('-')[1]?.toUpperCase() || 'US'
      
      setFormData(prev => ({
        ...prev,
        country: countryCode,
      }))
    }
  }, [profile, formData.country])

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleNotificationChange = (field: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      notification_preferences: {
        ...prev.notification_preferences,
        [field]: value,
      },
    }))
  }

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('Avatar file must be less than 5MB')
        return
      }
      
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setError('Avatar must be an image file')
        return
      }
      
      setAvatarFile(file)
      setError('')
    }
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    setSaving(true)
    setError('')
    setSuccess(false)

    try {
      // Upload avatar if selected
      if (avatarFile) {
        const uploadResult = await uploadAvatar(avatarFile)
        if (uploadResult.error) {
          setError(uploadResult.error)
          setSaving(false)
          return
        }
        // Note: uploadAvatar already updates the profile with the new avatar_url
      }

      // Update profile with form data (only if there are changes beyond avatar)
      const updateResult = await updateProfile(formData)
      if (updateResult.error) {
        setError(updateResult.error)
        setSaving(false)
        return
      }

      setSuccess(true)
      setAvatarFile(null)
      onSave?.()
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000)
    } catch (err) {
      setError('Failed to save profile')
      console.error('Profile save error:', err)
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <LoadingState message="Loading profile..." className={className} />
  }

  return (
    <div className={className}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="bg-error-red/5 border border-error-red/20 rounded-xl p-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-error-red/10 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-error-red" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <p className="text-error-red font-medium">{error}</p>
            </div>
          </div>
        )}
        
        {success && (
          <div className="bg-success-green/5 border border-success-green/20 rounded-xl p-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-success-green/10 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-success-green" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <p className="text-success-green font-medium">Profile updated successfully!</p>
            </div>
          </div>
        )}

        {/* Avatar Section */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Profile Photo
          </label>
          <div className="flex items-center space-x-4">
            {(profile?.avatar_url || avatarFile) ? (
              <img
                src={avatarFile ? URL.createObjectURL(avatarFile) : profile?.avatar_url}
                alt="Avatar"
                className="w-16 h-16 rounded-full object-cover border-2 border-border-light"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                <span className="text-white text-xl font-semibold">
                  {(formData.display_name || 'User').charAt(0).toUpperCase()}
                </span>
              </div>
            )}
            <input
              type="file"
              accept="image/*"
              onChange={handleAvatarChange}
              className="block text-sm text-text-secondary file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-gradient-to-r file:from-blue-500 file:to-purple-600 file:text-white hover:file:shadow-lg file:transition-all file:duration-200"
            />
          </div>
          <p className="text-xs text-text-secondary">JPG, PNG or GIF. Max size 5MB.</p>
          {avatarFile && (
            <p className="text-sm text-green-600">
              ✓ New avatar selected: {avatarFile.name}
            </p>
          )}
        </div>

        {/* Display Name */}
        <div className="space-y-1">
          <label htmlFor="display_name" className="block text-sm font-medium text-text-primary">
            Display Name
          </label>
          <input
            type="text"
            id="display_name"
            value={formData.display_name}
            onChange={(e) => handleInputChange('display_name', e.target.value)}
            className="block w-full px-3 py-2 border border-border-light rounded-lg shadow-sm bg-background text-text-primary placeholder:text-text-secondary focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
            placeholder="Enter your display name"
          />
        </div>

        {/* Country */}
        <div className="space-y-1">
          <label htmlFor="country" className="block text-sm font-medium text-text-primary">
            Country
          </label>
          <select
            id="country"
            value={formData.country}
            onChange={(e) => handleInputChange('country', e.target.value)}
            className="block w-full px-3 py-2 border border-border-light rounded-lg shadow-sm bg-background text-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
          >
            <option value="">Select your country</option>
            {COUNTRIES.map((country) => (
              <option key={country.code} value={country.code}>
                {country.name}
              </option>
            ))}
          </select>
          <p className="text-xs text-text-secondary">
            Choose the country where you reside
          </p>
        </div>

        {/* Currency Preference */}
        <div className="space-y-1">
          <label htmlFor="currency" className="block text-sm font-medium text-text-primary">
            Currency Preference
          </label>
          <select
            id="currency"
            value={formData.currency_preference}
            onChange={(e) => handleInputChange('currency_preference', e.target.value)}
            className="block w-full px-3 py-2 border border-border-light rounded-lg shadow-sm bg-background text-text-primary focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
          >
            {CURRENCIES.map((currency) => (
              <option key={currency.code} value={currency.code}>
                {currency.name} ({currency.symbol})
              </option>
            ))}
          </select>
          <p className="text-xs text-text-secondary">
            Choose your preferred currency for calculations and display (independent of your country)
          </p>
        </div>

        {/* Notification Preferences */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-text-primary">
            Notification Preferences
          </label>
          <div className="space-y-2">
            {[
              { key: 'email_notifications', label: 'Email Notifications', description: 'Receive updates via email' },
              { key: 'push_notifications', label: 'Push Notifications', description: 'Get browser notifications' },
              { key: 'budget_alerts', label: 'Budget Alerts', description: 'Alert when budget limits are reached' },
              { key: 'weekly_summary', label: 'Weekly Summary', description: 'Weekly spending summary reports' },
            ].map((option) => (
              <label key={option.key} className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.notification_preferences[option.key as keyof typeof formData.notification_preferences]}
                  onChange={(e) => handleNotificationChange(option.key, e.target.checked)}
                  className="rounded border-border-light text-primary-blue shadow-sm focus:border-primary-blue focus:ring focus:ring-primary-blue/20 focus:ring-opacity-50"
                />
                <div className="ml-2">
                  <div className="text-sm text-text-primary font-medium">{option.label}</div>
                  <div className="text-xs text-text-secondary">{option.description}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end pt-4">
          <button
            type="submit"
            disabled={saving}
            className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-md flex items-center gap-2"
          >
            {saving ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Saving Changes...
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Save Changes
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  )
}