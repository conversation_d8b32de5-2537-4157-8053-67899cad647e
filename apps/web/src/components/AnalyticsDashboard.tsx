'use client'

import { useState, useEffect } from 'react'
import { useTheme } from '../contexts/ThemeContext'
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { AnalyticsService, type IAnalyticsData, useCurrencyStore } from '@repo/shared'
import { ContentCard } from './ContentCard'
import { LoadingState } from './LoadingState'

interface AnalyticsDashboardProps {
  data: IAnalyticsData
  dateRange: { startDate: string; endDate: string }
  onDateRangeChange: (range: { startDate: string; endDate: string }) => void
}

interface TooltipPayload {
  dataKey: string
  value: number
  color: string
  payload?: {
    name: string
    value: number
    percentage: number
  }
}

interface CustomTooltipProps {
  active?: boolean
  payload?: TooltipPayload[]
  label?: string
}

interface PieTooltipProps {
  active?: boolean
  payload?: TooltipPayload[]
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658']

export default function AnalyticsDashboard({ data, dateRange, onDateRangeChange }: AnalyticsDashboardProps) {
  const [selectedChart, setSelectedChart] = useState<'overview' | 'trends' | 'categories'>('overview')
  const { formatCurrency } = useCurrencyStore()
  const { resolvedTheme } = useTheme()
  const [isClient, setIsClient] = useState(false)

  // Ensure we're on the client side before rendering charts
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Theme-aware chart colors
  const getChartColors = () => {
    return resolvedTheme === 'dark' 
      ? {
          grid: '#27272A',
          text: '#A1A1AA',
          tooltip: '#27272A',
          border: '#3F3F46'
        }
      : {
          grid: '#E5E7EB',
          text: '#6B7280',
          tooltip: '#FFFFFF',
          border: '#E5E7EB'
        }
  }

  const chartColors = getChartColors()

  const handleDateRangeSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const startDate = formData.get('startDate') as string
    const endDate = formData.get('endDate') as string
    
    if (startDate && endDate) {
      onDateRangeChange({ startDate, endDate })
    }
  }

  // Prepare data for charts
  const filteredCategories = data.categoryBreakdown
    .filter(item => item.total > 0)
  
  // Recalculate percentages to sum to 100% for the visible categories
  const totalFilteredAmount = filteredCategories.reduce((sum, item) => sum + item.total, 0)
  
  const pieChartData = filteredCategories.map(item => ({
    name: item.category.name,
    value: item.total,
    percentage: totalFilteredAmount > 0 ? (item.total / totalFilteredAmount) * 100 : 0
  }))

  const trendChartData = data.monthlyTrends.map(item => ({
    month: AnalyticsService.formatMonth(item.month),
    income: item.income,
    expenses: item.expenses,
    net: item.net
  }))

  const topCategoriesData = data.topCategories.map(item => ({
    name: item.category.name,
    amount: item.total,
    transactions: item.transactionCount
  }))

  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-surface-elevated p-4 border border-border rounded-xl shadow-lg backdrop-blur-sm">
          <p className="font-semibold text-text-primary mb-2">{label}</p>
          {payload.map((entry: TooltipPayload, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.dataKey}: ${formatCurrency(entry.value)}`}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  const PieTooltip = ({ active, payload }: PieTooltipProps) => {
    if (active && payload && payload.length && payload[0].payload) {
      const data = payload[0].payload
      return (
        <div className="bg-surface-elevated p-4 border border-border rounded-xl shadow-lg backdrop-blur-sm">
          <p className="font-semibold text-text-primary mb-1">{data.name}</p>
          <p className="text-sm mb-1" style={{ color: payload[0].color }}>
            {`Amount: ${formatCurrency(data.value)}`}
          </p>
          <p className="text-sm text-text-secondary">
            {`${data.percentage.toFixed(1)}% of total`}
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="space-y-6">
      {/* Date Range Selector */}
      <ContentCard>
        <form onSubmit={handleDateRangeSubmit} className="flex flex-col sm:flex-row items-start sm:items-end gap-4">
          <div className="flex-1">
            <label htmlFor="startDate" className="block text-sm font-medium text-text-primary mb-2">
              Start Date
            </label>
            <input
              type="date"
              id="startDate"
              name="startDate"
              defaultValue={dateRange.startDate}
              className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-4 focus:ring-primary-blue/10"
            />
          </div>
          <div className="flex-1">
            <label htmlFor="endDate" className="block text-sm font-medium text-text-primary mb-2">
              End Date
            </label>
            <input
              type="date"
              id="endDate"
              name="endDate"
              defaultValue={dateRange.endDate}
              className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-4 focus:ring-primary-blue/10"
            />
          </div>
          <button
            type="submit"
            className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 whitespace-nowrap"
          >
            Update Range
          </button>
        </form>
      </ContentCard>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <ContentCard>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-success-green/10 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-success-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary mb-1">Total Income</p>
              <p className="text-3xl font-bold text-text-primary">
                {formatCurrency(data.totalIncome)}
              </p>
            </div>
          </div>
        </ContentCard>

        <ContentCard>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-error-red/10 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-error-red" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary mb-1">Total Expenses</p>
              <p className="text-3xl font-bold text-text-primary">
                {formatCurrency(data.totalExpenses)}
              </p>
            </div>
          </div>
        </ContentCard>

        <ContentCard>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={`w-12 h-12 ${data.netIncome >= 0 ? 'bg-primary-blue/10' : 'bg-warning-orange/10'} rounded-lg flex items-center justify-center`}>
                <svg className={`w-6 h-6 ${data.netIncome >= 0 ? 'text-primary-blue' : 'text-warning-orange'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary mb-1">Net Income</p>
              <p className={`text-3xl font-bold ${data.netIncome >= 0 ? 'text-success-green' : 'text-error-red'}`}>
                {formatCurrency(data.netIncome)}
              </p>
            </div>
          </div>
        </ContentCard>
      </div>

      {/* Chart Navigation */}
      <ContentCard>
        <div className="flex flex-wrap gap-2 mb-6">
          <button
            onClick={() => setSelectedChart('overview')}
            className={`px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-200 border ${
              selectedChart === 'overview'
                ? 'border-primary-blue text-white bg-gradient-to-r from-primary-blue to-primary-purple shadow-md'
                : 'bg-surface border-border text-text-secondary hover:text-text-primary hover:bg-surface-elevated hover:border-primary-blue'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setSelectedChart('trends')}
            className={`px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-200 border ${
              selectedChart === 'trends'
                ? 'border-primary-blue text-white bg-gradient-to-r from-primary-blue to-primary-purple shadow-md'
                : 'bg-surface border-border text-text-secondary hover:text-text-primary hover:bg-surface-elevated hover:border-primary-blue'
            }`}
          >
            Monthly Trends
          </button>
          <button
            onClick={() => setSelectedChart('categories')}
            className={`px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-200 border ${
              selectedChart === 'categories'
                ? 'border-primary-blue text-white bg-gradient-to-r from-primary-blue to-primary-purple shadow-md'
                : 'bg-surface border-border text-text-secondary hover:text-text-primary hover:bg-surface-elevated hover:border-primary-blue'
            }`}
          >
            Categories
          </button>
        </div>

        {/* Chart Content */}
        {selectedChart === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Expense Breakdown Pie Chart */}
            <div>
              <h3 className="text-xl font-semibold text-text-primary mb-4">Expense Breakdown</h3>
              {pieChartData.length > 0 ? (
                <div className="space-y-4">
                  {isClient && (
                    <ResponsiveContainer width="100%" height={280}>
                      <PieChart>
                        <Pie
                          data={pieChartData}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {pieChartData.map((_, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip content={<PieTooltip />} />
                      </PieChart>
                    </ResponsiveContainer>
                  )}
                  
                  {/* Custom Legend */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                    {pieChartData.map((entry, index) => (
                      <div key={entry.name} className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full flex-shrink-0" 
                          style={{ backgroundColor: COLORS[index % COLORS.length] }}
                        />
                        <span className="text-text-secondary truncate" title={entry.name}>
                          {entry.name}: {entry.percentage.toFixed(1)}%
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-64 text-text-tertiary">
                  <div className="text-center">
                    <svg className="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <p>No expense data available</p>
                  </div>
                </div>
              )}
            </div>

            {/* Income vs Expense Comparison */}
            <div>
              <h3 className="text-xl font-semibold text-text-primary mb-4">Income vs Expenses</h3>
              {isClient && (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={[{ name: 'Total', income: data.totalIncome, expenses: data.totalExpenses }]}>
                    <CartesianGrid strokeDasharray="3 3" stroke={chartColors.grid} />
                    <XAxis dataKey="name" stroke={chartColors.text} />
                    <YAxis stroke={chartColors.text} />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar dataKey="income" fill="var(--success-green)" name="Income" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="expenses" fill="var(--error-red)" name="Expenses" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </div>
          </div>
        )}

        {selectedChart === 'trends' && (
          <div>
            <h3 className="text-xl font-semibold text-text-primary mb-4">Monthly Trends</h3>
            {trendChartData.length > 0 ? (
              isClient ? (
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={trendChartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke={chartColors.grid} />
                    <XAxis 
                      dataKey="month" 
                      angle={-45}
                      textAnchor="end"
                      height={80}
                      stroke={chartColors.text}
                    />
                    <YAxis stroke={chartColors.text} />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Line type="monotone" dataKey="income" stroke="var(--success-green)" name="Income" strokeWidth={3} dot={{ r: 6 }} />
                    <Line type="monotone" dataKey="expenses" stroke="var(--error-red)" name="Expenses" strokeWidth={3} dot={{ r: 6 }} />
                    <Line type="monotone" dataKey="net" stroke="var(--primary-blue)" name="Net" strokeWidth={3} dot={{ r: 6 }} />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <LoadingState message="Loading chart..." className="h-64" />
              )
            ) : (
              <div className="flex items-center justify-center h-64 text-text-tertiary">
                <div className="text-center">
                  <svg className="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <p>No trend data available</p>
                </div>
              </div>
            )}
          </div>
        )}

        {selectedChart === 'categories' && (
          <div>
            <h3 className="text-xl font-semibold text-text-primary mb-4">Top Categories</h3>
            {topCategoriesData.length > 0 ? (
              isClient ? (
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={topCategoriesData} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" stroke={chartColors.grid} />
                    <XAxis type="number" stroke={chartColors.text} />
                    <YAxis dataKey="name" type="category" width={120} stroke={chartColors.text} />
                    <Tooltip 
                      formatter={(value: number, name: string) => [
                        name === 'amount' ? formatCurrency(value) : value,
                        name === 'amount' ? 'Amount' : 'Transactions'
                      ]}
                      contentStyle={{
                        backgroundColor: chartColors.tooltip,
                        border: `1px solid ${chartColors.border}`,
                        borderRadius: '12px',
                        boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
                        color: resolvedTheme === 'dark' ? '#F9FAFB' : '#111827'
                      }}
                    />
                    <Legend />
                    <Bar dataKey="amount" fill="var(--primary-blue)" name="Amount" radius={[0, 4, 4, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <LoadingState message="Loading chart..." className="h-64" />
              )
            ) : (
              <div className="flex items-center justify-center h-64 text-text-tertiary">
                <div className="text-center">
                  <svg className="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <p>No category data available</p>
                </div>
              </div>
            )}
          </div>
        )}
      </ContentCard>

      {/* Top Categories List */}
      {data.topCategories.length > 0 && (
        <ContentCard>
          <h3 className="text-xl font-semibold text-text-primary mb-6">Top Spending Categories</h3>
          <div className="space-y-4">
            {data.topCategories.map((item, index) => (
              <div key={item.category.id} className="flex items-center justify-between p-4 bg-surface rounded-lg hover:bg-surface-elevated transition-colors border border-border-light">
                <div className="flex items-center">
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <div 
                        className="w-6 h-6 rounded-full shadow-sm"
                        style={{ backgroundColor: item.category.color || '#6B7280' }}
                      />
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-text-tertiary rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium text-surface">#{index + 1}</span>
                      </div>
                    </div>
                    <div>
                      <p className="font-semibold text-text-primary">{item.category.name}</p>
                      <p className="text-sm text-text-secondary flex items-center gap-1">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v8a2 2 0 002 2h2m0 0h2a2 2 0 002-2V7a2 2 0 00-2-2h-2m0 0V5a2 2 0 112 0v.01M9 5a2 2 0 112 0v.01M9 5a2 2 0 00-2 2v3a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H9z" />
                        </svg>
                        {item.transactionCount} transactions
                      </p>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xl font-bold text-text-primary">
                    {formatCurrency(item.total)}
                  </p>
                  <p className="text-sm text-text-secondary">
                    {((item.total / data.totalExpenses) * 100).toFixed(1)}% of expenses
                  </p>
                </div>
              </div>
            ))}
          </div>
        </ContentCard>
      )}
    </div>
  )
}