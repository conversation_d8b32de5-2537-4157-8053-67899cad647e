import React, { useState, useEffect } from 'react'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { transactionFormInputSchema, transactionFormSchema, type TransactionFormInputData, type TransactionFormData, type TransactionData, type ICategory } from '@repo/shared'
// import { PhotoAttachment } from './PhotoAttachment' // Mobile only component

interface TransactionFormProps {
  onSubmit: (data: TransactionData) => Promise<void>
  categories: ICategory[]
  defaultType?: 'income' | 'expense'
  loading?: boolean
  className?: string
  initialData?: TransactionData
  compact?: boolean
}

export const TransactionForm: React.FC<TransactionFormProps> = ({
  onSubmit,
  categories,
  defaultType = 'expense',
  loading = false,
  className = "",
  initialData,
  compact = false
}) => {
  const form = useForm<TransactionFormInputData>({
    resolver: zodResolver(transactionFormInputSchema),
    defaultValues: {
      amount: initialData?.amount?.toString() || '',
      category_id: initialData?.category_id || '',
      description: initialData?.description || '',
      transaction_date: initialData?.transaction_date || new Date(),
      transaction_type: initialData?.transaction_type || defaultType,
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    watch
  } = form

  const transactionType = watch('transaction_type')
  const filteredCategories = categories.filter(cat => cat.type === transactionType)

  // Reset form when initialData changes (for editing)
  useEffect(() => {
    if (initialData) {
      reset({
        amount: initialData.amount.toString(),
        category_id: initialData.category_id,
        description: initialData.description || '',
        transaction_date: initialData.transaction_date,
        transaction_type: initialData.transaction_type,
      })
    }
  }, [initialData, reset])

  const handleFormSubmit = async (data: TransactionFormInputData) => {
    try {
      // Parse and validate with the transform schema
      const validatedData = transactionFormSchema.parse(data)
      
      // Convert to TransactionData format
      const transactionData: TransactionData = {
        amount: validatedData.amount,
        category_id: validatedData.category_id,
        description: validatedData.description,
        transaction_date: validatedData.transaction_date,
        transaction_type: validatedData.transaction_type,
      }
      await onSubmit(transactionData)
      if (!initialData) {
        reset() // Only reset form after successful submission for new transactions
      }
    } catch (error) {
      console.error('Error submitting transaction:', error)
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className={`${compact ? 'space-y-4' : 'space-y-8'} ${className}`}>
      {/* Main Form Fields - Compact Grid Layout */}
      <div className={compact ? 'space-y-4' : 'space-y-6'}>
        {/* Transaction Type Toggle with Colored Background */}
        <div className={`
          -mx-6 -mt-6 mb-6 p-6 rounded-t-xl
          ${transactionType === 'expense' 
            ? 'bg-gradient-to-br from-error-red to-warning-orange' 
            : 'bg-gradient-to-br from-success-green to-success-green'
          }
        `}>
          <div className="space-y-4">
            <label className="block text-sm font-medium text-white">
              Transaction Type
            </label>
            <Controller
              name="transaction_type"
              control={control}
              render={({ field }) => (
                <div className="flex rounded-lg overflow-hidden border border-white/20 shadow-lg">
                  <button
                    type="button"
                    onClick={() => field.onChange('expense')}
                    className={`
                      flex-1 py-3 px-6 text-sm font-medium transition-all duration-200
                      ${field.value === 'expense' 
                        ? 'bg-white text-text-primary shadow-sm' 
                        : 'bg-white/20 text-white hover:bg-white/30'
                      }
                    `}
                  >
                    Expense
                  </button>
                  <button
                    type="button"
                    onClick={() => field.onChange('income')}
                    className={`
                      flex-1 py-3 px-6 text-sm font-medium transition-all duration-200
                      ${field.value === 'income' 
                        ? 'bg-white text-text-primary shadow-sm' 
                        : 'bg-white/20 text-white hover:bg-white/30'
                      }
                    `}
                  >
                    Income
                  </button>
                </div>
              )}
            />
          </div>
          
          {/* Amount Input within colored section */}
          <div className="space-y-2 pt-4">
            <label className="block text-sm font-medium text-white">
              Amount *
            </label>
            <Controller
              name="amount"
              control={control}
              render={({ field }) => (
                <div className="relative">
                  <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-text-primary font-medium text-lg">
                    {transactionType === 'expense' ? '-' : '+'}
                  </span>
                  <input
                    type="text"
                    inputMode="decimal"
                    value={field.value}
                    onChange={(e) => {
                      const formatted = e.target.value.replace(/[^\d.]/g, '')
                      const parts = formatted.split('.')
                      if (parts.length > 2) {
                        field.onChange(parts[0] + '.' + parts.slice(1).join(''))
                      } else if (parts[1] && parts[1].length > 2) {
                        field.onChange(parts[0] + '.' + parts[1].slice(0, 2))
                      } else {
                        field.onChange(formatted)
                      }
                    }}
                    placeholder="0"
                    className={`
                      pl-8 pr-4 py-4 border-2 border-white/30 rounded-xl w-full bg-white/90 text-text-primary text-lg font-medium
                      placeholder:text-text-tertiary focus:ring-2 focus:ring-white focus:border-white focus:bg-white
                      ${errors.amount ? 'border-error-red bg-error-red/10' : ''}
                    `}
                  />
                </div>
              )}
            />
            {errors.amount && (
              <p className="text-sm text-white bg-error-red/20 px-3 py-1 rounded">{errors.amount.message}</p>
            )}
          </div>
        </div>

        {/* Category and other fields */}
        <div className="space-y-6">
          {/* Category Selection */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Category *
            </label>
            <Controller
              name="category_id"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className={`
                    w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary
                    ${errors.category_id ? 'border-error-red' : ''}
                  `}
                >
                  <option value="">Select a category</option>
                  {filteredCategories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              )}
            />
            {errors.category_id && (
              <p className="text-sm text-error-red">{errors.category_id.message}</p>
            )}
          </div>

          {/* Date Picker */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Date *
            </label>
            <Controller
              name="transaction_date"
              control={control}
              render={({ field }) => (
                <input
                  type="date"
                  value={field.value.toISOString().split('T')[0]}
                  onChange={(e) => field.onChange(new Date(e.target.value))}
                  className={`
                    w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary
                    ${errors.transaction_date ? 'border-error-red' : ''}
                  `}
                />
              )}
            />
            {errors.transaction_date && (
              <p className="text-sm text-error-red">{errors.transaction_date.message}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Description
            </label>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <textarea
                  {...field}
                  rows={compact ? 2 : 3}
                  placeholder="Optional notes about this transaction..."
                  className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 resize-none text-text-primary"
                />
              )}
            />
            {errors.description && (
              <p className="text-sm text-error-red">{errors.description.message}</p>
            )}
          </div>
        </div>
      </div>


      {/* Photo Attachment - Disabled for web */}
      {/* <div className={`border-t border-border-light ${compact ? 'pt-4' : 'pt-6'}`}>
        <PhotoAttachment 
          onPhotoUploaded={(url: string) => {
            console.log('Photo uploaded:', url)
          }}
        />
      </div> */}

      {/* Submit Buttons */}
      <div className={`border-t border-border-light ${compact ? 'pt-6 mt-6' : 'pt-8 mt-8'} space-y-4`}>
        <button
          type="submit"
          disabled={isSubmitting || loading}
          className="bg-gradient-to-r from-primary-blue to-primary-purple text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 w-full py-4 disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-primary-blue/20"
        >
        {isSubmitting || loading ? (
          <span className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {initialData ? 'Updating' : 'Adding'} record...
          </span>
        ) : (
          `${initialData ? 'Update record' : 'Add record'}`
        )}
        </button>
        
        {!initialData && (
          <div className="text-center">
            <button
              type="button"
              onClick={handleSubmit(async (data: TransactionFormInputData) => {
                try {
                  // Parse and validate with the transform schema
                  const validatedData = transactionFormSchema.parse(data)
                  
                  // Convert to TransactionData format
                  const transactionData: TransactionData = {
                    amount: validatedData.amount,
                    category_id: validatedData.category_id,
                    description: validatedData.description,
                    transaction_date: validatedData.transaction_date,
                    transaction_type: validatedData.transaction_type,
                  }
                  await onSubmit(transactionData)
                  // Reset form but keep transaction type
                  reset({
                    amount: '',
                    category_id: '',
                    description: '',
                    transaction_date: new Date(),
                    transaction_type: data.transaction_type,
                  })
                } catch (error) {
                  console.error('Error submitting transaction:', error)
                }
              })}
              disabled={isSubmitting || loading}
              className="text-primary-blue hover:text-primary-purple font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Add and create another
            </button>
          </div>
        )}
      </div>
    </form>
  )
}