import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  type IAccount,
  AccountService,
  InvestmentService,
  TransferService,
  useCurrencyStore
} from '@shared/index'
import toast from 'react-hot-toast'

// Investment form input schema (before transformation)
const investmentFormInputSchema = z.object({
  transaction_type: z.enum(['investment_buy', 'investment_sell', 'dividend']),
  amount: z.string().min(1, 'Amount is required'),
  investment_account_id: z.string().uuid('Please select an investment account'),
  funding_account_id: z.string().uuid('Please select a funding account').optional(),
  investment_symbol: z.string().min(1, 'Stock symbol is required').optional(),
  investment_quantity: z.string().optional(),
  investment_price: z.string().optional(),
  description: z.string().optional(),
  transaction_date: z.date(),
  fees: z.string().optional(),
}).refine((data) => {
  if (data.transaction_type === 'investment_buy' || data.transaction_type === 'investment_sell') {
    return data.investment_symbol && data.investment_quantity && data.investment_price
  }
  return true
}, {
  message: "Stock symbol, quantity, and price are required for buy/sell transactions",
  path: ["investment_symbol"]
})

// Investment form schema with transformation
const investmentFormSchema = investmentFormInputSchema.transform((data) => ({
  ...data,
  amount: parseFloat(data.amount),
  investment_quantity: data.investment_quantity ? parseFloat(data.investment_quantity) : undefined,
  investment_price: data.investment_price ? parseFloat(data.investment_price) : undefined,
  fees: data.fees ? parseFloat(data.fees) : 0,
}))

type InvestmentFormInputData = z.infer<typeof investmentFormInputSchema>
type InvestmentFormData = z.infer<typeof investmentFormSchema>

interface InvestmentFormProps {
  onSubmit: (data: any) => Promise<void>
  loading?: boolean
  className?: string
  initialData?: any
  compact?: boolean
}

type InvestmentTabType = 'buy' | 'sell' | 'dividend'

export const InvestmentForm: React.FC<InvestmentFormProps> = ({
  onSubmit,
  loading = false,
  className = "",
  initialData,
  compact = false
}) => {
  const [activeTab, setActiveTab] = useState<InvestmentTabType>(
    initialData?.transaction_type === 'investment_sell' ? 'sell' :
    initialData?.transaction_type === 'dividend' ? 'dividend' : 'buy'
  )
  const [accounts, setAccounts] = useState<IAccount[]>([])
  const [loadingData, setLoadingData] = useState(true)
  const { formatCurrency } = useCurrencyStore()

  const form = useForm<InvestmentFormInputData>({
    resolver: zodResolver(investmentFormInputSchema),
    defaultValues: {
      transaction_type: initialData?.transaction_type || 'investment_buy',
      amount: initialData?.amount?.toString() || '',
      investment_account_id: initialData?.account_id || '',
      funding_account_id: initialData?.funding_account_id || '',
      investment_symbol: initialData?.investment_symbol || '',
      investment_quantity: initialData?.investment_quantity?.toString() || '',
      investment_price: initialData?.investment_price?.toString() || '',
      description: initialData?.description || '',
      transaction_date: initialData?.transaction_date || new Date(),
      fees: initialData?.fees?.toString() || '',
    }
  })

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = form

  const transactionType = watch('transaction_type')

  useEffect(() => {
    const loadAccounts = async () => {
      try {
        const accountsData = await AccountService.getAccounts()
        setAccounts(accountsData)
      } catch (error) {
        console.error('Error loading accounts:', error)
        toast.error('Failed to load accounts')
      } finally {
        setLoadingData(false)
      }
    }

    loadAccounts()
  }, [])

  // Update transaction type when tab changes
  useEffect(() => {
    const transactionTypeMap = {
      'buy': 'investment_buy',
      'sell': 'investment_sell',
      'dividend': 'dividend'
    } as const
    setValue('transaction_type', transactionTypeMap[activeTab])
  }, [activeTab, setValue])

  // Filter accounts based on type
  const getInvestmentAccounts = () => {
    return accounts.filter(acc => acc.account_type === 'investment')
  }

  const getFundingAccounts = () => {
    return accounts.filter(acc => acc.account_type !== 'investment')
  }

  const handleFormSubmit = async (data: InvestmentFormInputData) => {
    try {
      const validatedData = investmentFormSchema.parse(data)

      if (validatedData.transaction_type === 'dividend') {
        // Handle dividend as a transfer from investment account to funding account
        await TransferService.createTransfer({
          amount: validatedData.amount,
          description: validatedData.description || `Dividend payment: ${validatedData.investment_symbol || 'Investment'}`,
          from_account_id: validatedData.investment_account_id,
          to_account_id: validatedData.funding_account_id!,
          transaction_date: validatedData.transaction_date.toISOString().split('T')[0],
          fees: validatedData.fees || 0,
        })

        toast.success('Dividend recorded successfully!')
      } else {
        // Handle investment buy/sell with enhanced tracking
        const result = await InvestmentService.createInvestmentTransactionWithAssets({
          amount: validatedData.amount,
          description: validatedData.description,
          account_id: validatedData.investment_account_id,
          asset_id: validatedData.investment_symbol!, // This should be asset_id in real implementation
          investment_symbol: validatedData.investment_symbol!,
          investment_quantity: validatedData.investment_quantity!,
          investment_price: validatedData.investment_price!,
          transaction_type: validatedData.transaction_type as 'investment_buy' | 'investment_sell',
          transaction_date: validatedData.transaction_date.toISOString().split('T')[0],
          fees: validatedData.fees || 0,
        }, validatedData.transaction_type === 'investment_buy' ? validatedData.funding_account_id : undefined)

        // Show tax information for sell transactions
        if (validatedData.transaction_type === 'investment_sell' && result.taxCalculation) {
          const tax = result.taxCalculation
          toast.success(
            `Sale completed! ${tax.is_long_term ? 'LTCG' : 'STCG'}: ${formatCurrency(tax.taxable_capital_gain_loss)}, Tax: ${formatCurrency(tax.total_tax_liability)}`,
            { duration: 6000 }
          )
        } else {
          toast.success(`${validatedData.transaction_type === 'investment_buy' ? 'Purchase' : 'Sale'} completed successfully!`)
        }
      }

      await onSubmit(validatedData)
      if (!initialData) {
        reset()
      }
    } catch (error) {
      console.error('Error submitting investment transaction:', error)
      toast.error('Failed to submit investment transaction')
    }
  }

  if (loadingData) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-4 border-border border-t-primary-blue"></div>
      </div>
    )
  }

  const tabs = [
    { id: 'buy' as InvestmentTabType, label: 'Buy', icon: '📈' },
    { id: 'sell' as InvestmentTabType, label: 'Sell', icon: '📉' },
    { id: 'dividend' as InvestmentTabType, label: 'Dividend', icon: '💰' }
  ]

  return (
    <div className={`${className}`}>
      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-surface rounded-lg p-1 mb-6">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            type="button"
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-md text-sm font-medium transition-all ${
              activeTab === tab.id
                ? 'bg-primary-blue text-white shadow-sm'
                : 'text-text-secondary hover:text-text-primary hover:bg-surface'
            }`}
          >
            <span>{tab.icon}</span>
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Form Content */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className={`${compact ? 'space-y-4' : 'space-y-6'}`}>
        {/* Investment Account */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Investment Account *
          </label>
          <Controller
            name="investment_account_id"
            control={control}
            render={({ field }) => (
              <select
                {...field}
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.investment_account_id ? 'border-error-red' : ''}`}
              >
                <option value="">Select investment account</option>
                {getInvestmentAccounts().map((account) => (
                  <option key={account.id} value={account.id}>
                    {account.name} - {formatCurrency(account.current_balance || 0)}
                  </option>
                ))}
              </select>
            )}
          />
          {errors.investment_account_id && (
            <p className="text-sm text-error-red">{errors.investment_account_id.message}</p>
          )}
        </div>

        {/* Funding Account (for buy transactions and dividends) */}
        {(activeTab === 'buy' || activeTab === 'dividend') && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              {activeTab === 'buy' ? 'Funding Account *' : 'Receiving Account *'}
            </label>
            <Controller
              name="funding_account_id"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.funding_account_id ? 'border-error-red' : ''}`}
                >
                  <option value="">Select {activeTab === 'buy' ? 'funding' : 'receiving'} account</option>
                  {getFundingAccounts().map((account) => (
                    <option key={account.id} value={account.id}>
                      {account.name} ({account.account_type}) - {formatCurrency(account.current_balance || 0)}
                    </option>
                  ))}
                </select>
              )}
            />
            {errors.funding_account_id && (
              <p className="text-sm text-error-red">{errors.funding_account_id.message}</p>
            )}
          </div>
        )}

        {/* Stock Symbol (not required for dividend) */}
        {activeTab !== 'dividend' && (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">
              Stock Symbol *
            </label>
            <Controller
              name="investment_symbol"
              control={control}
              render={({ field }) => (
                <input
                  type="text"
                  {...field}
                  placeholder="e.g., AAPL, GOOGL"
                  className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.investment_symbol ? 'border-error-red' : ''}`}
                />
              )}
            />
            {errors.investment_symbol && (
              <p className="text-sm text-error-red">{errors.investment_symbol.message}</p>
            )}
          </div>
        )}

        {/* Quantity and Price (not required for dividend) */}
        {activeTab !== 'dividend' && (
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-text-primary">
                Quantity *
              </label>
              <Controller
                name="investment_quantity"
                control={control}
                render={({ field }) => (
                  <input
                    type="text"
                    inputMode="decimal"
                    {...field}
                    placeholder="0"
                    className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.investment_quantity ? 'border-error-red' : ''}`}
                  />
                )}
              />
              {errors.investment_quantity && (
                <p className="text-sm text-error-red">{errors.investment_quantity.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-text-primary">
                Price per Share *
              </label>
              <Controller
                name="investment_price"
                control={control}
                render={({ field }) => (
                  <input
                    type="text"
                    inputMode="decimal"
                    {...field}
                    placeholder="0.00"
                    className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.investment_price ? 'border-error-red' : ''}`}
                  />
                )}
              />
              {errors.investment_price && (
                <p className="text-sm text-error-red">{errors.investment_price.message}</p>
              )}
            </div>
          </div>
        )}

        {/* Amount */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            {activeTab === 'dividend' ? 'Dividend Amount *' : 'Total Amount *'}
          </label>
          <Controller
            name="amount"
            control={control}
            render={({ field }) => (
              <input
                type="text"
                inputMode="decimal"
                {...field}
                placeholder="0.00"
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.amount ? 'border-error-red' : ''}`}
              />
            )}
          />
          {errors.amount && (
            <p className="text-sm text-error-red">{errors.amount.message}</p>
          )}
        </div>

        {/* Fees */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Fees
          </label>
          <Controller
            name="fees"
            control={control}
            render={({ field }) => (
              <input
                type="text"
                inputMode="decimal"
                {...field}
                placeholder="0.00"
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.fees ? 'border-error-red' : ''}`}
              />
            )}
          />
          {errors.fees && (
            <p className="text-sm text-error-red">{errors.fees.message}</p>
          )}
        </div>

        {/* Description */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Description
          </label>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <textarea
                {...field}
                rows={3}
                placeholder="Add a note about this investment transaction..."
                className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary resize-none"
              />
            )}
          />
        </div>

        {/* Transaction Date */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">
            Date *
          </label>
          <Controller
            name="transaction_date"
            control={control}
            render={({ field }) => (
              <input
                type="date"
                {...field}
                value={field.value instanceof Date ? field.value.toISOString().split('T')[0] : field.value}
                onChange={(e) => field.onChange(new Date(e.target.value))}
                className={`w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20 text-text-primary ${errors.transaction_date ? 'border-error-red' : ''}`}
              />
            )}
          />
          {errors.transaction_date && (
            <p className="text-sm text-error-red">{errors.transaction_date.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            disabled={isSubmitting || loading}
            className="w-full bg-primary-blue hover:bg-primary-blue/90 disabled:bg-primary-blue/50 text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            {isSubmitting || loading ? 'Processing...' : 
             initialData ? 'Update Investment' : 
             activeTab === 'buy' ? 'Buy Investment' :
             activeTab === 'sell' ? 'Sell Investment' : 'Record Dividend'}
          </button>
        </div>
      </form>
    </div>
  )
}
