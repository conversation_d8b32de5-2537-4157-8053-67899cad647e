'use client'

import { useState, useEffect, createContext, useContext } from 'react'

interface SidebarContextType {
  isCollapsed: boolean
  setIsCollapsed: (collapsed: boolean) => void
}

export const SidebarContext = createContext<SidebarContextType | undefined>(undefined)

export function useSidebar() {
  const context = useContext(SidebarContext)
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider')
  }
  return context
}

export function useSidebarState() {
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false)
  const [isLoaded, setIsLoaded] = useState<boolean>(false)

  // Load collapsed state from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('sidebar-collapsed')
    setIsCollapsed(saved ? JSON.parse(saved) : false)
    setIsLoaded(true)
  }, [])

  // Save collapsed state to localStorage
  useEffect(() => {
    if (isLoaded) {
      localStorage.setItem('sidebar-collapsed', JSON.stringify(isCollapsed))
    }
  }, [isCollapsed, isLoaded])

  return { isCollapsed, setIsCollapsed, isLoaded }
}