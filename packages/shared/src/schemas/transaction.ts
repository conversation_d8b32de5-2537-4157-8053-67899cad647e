import { z } from 'zod'

export const transactionSchema = z.object({
  amount: z.number().positive('Amount must be greater than 0'),
  category_id: z.string().uuid('Please select a category'),
  description: z.string().optional(),
  transaction_date: z.date(),
  transaction_type: z.enum(['income', 'expense']),
  attachments: z.array(z.any()).optional(),
})

// Base form schema without transform for form state
export const transactionFormInputSchema = z.object({
  amount: z.string().min(1, 'Amount is required'),
  category_id: z.string().min(1, 'Please select a category'),
  description: z.string().optional(),
  transaction_date: z.date(),
  transaction_type: z.enum(['income', 'expense']),
})

// Schema with transform for final validation
export const transactionFormSchema = transactionFormInputSchema.transform((data) => ({
  ...data,
  amount: (() => {
    const num = parseFloat(data.amount.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num <= 0) {
      throw new Error('Amount must be a positive number')
    }
    return num
  })(),
}))

// Comprehensive transaction schema that supports transfers and investments
export const comprehensiveTransactionFormInputSchema = z.object({
  amount: z.string().min(1, 'Amount is required'),
  category_id: z.string().optional(),
  account_id: z.string().optional(),
  to_account_id: z.string().optional(),
  description: z.string().optional(),
  transaction_date: z.date(),
  transaction_type: z.enum(['income', 'expense', 'transfer', 'investment_buy', 'investment_sell', 'dividend']),
  fees: z.string().optional(),
  investment_symbol: z.string().optional(),
  investment_quantity: z.string().optional(),
  investment_price: z.string().optional(),
  funding_account_id: z.string().optional(),
  is_internal: z.boolean().optional(), // For transfers: true = exclude from analytics
}).superRefine((data, ctx) => {
  // Validation for basic transactions (income/expense)
  if (data.transaction_type === 'income' || data.transaction_type === 'expense') {
    if (!data.category_id) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Category is required for income and expense transactions',
        path: ['category_id']
      })
    }
    if (!data.account_id) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Account is required for income and expense transactions',
        path: ['account_id']
      })
    }
  }

  // Validation for transfers
  if (data.transaction_type === 'transfer') {
    if (!data.category_id) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Category is required for transfers',
        path: ['category_id']
      })
    }
    if (!data.account_id) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'From account is required for transfers',
        path: ['account_id']
      })
    }
    if (!data.to_account_id) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'To account is required for transfers',
        path: ['to_account_id']
      })
    }
    if (data.account_id === data.to_account_id) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'From and to accounts must be different',
        path: ['to_account_id']
      })
    }
  }

  // Validation for investments
  if (data.transaction_type === 'investment_buy' || data.transaction_type === 'investment_sell') {
    if (!data.investment_symbol) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Investment symbol is required',
        path: ['investment_symbol']
      })
    }
    if (!data.investment_quantity) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Investment quantity is required',
        path: ['investment_quantity']
      })
    }
    if (!data.investment_price) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Investment price is required',
        path: ['investment_price']
      })
    }
    if (!data.account_id) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Investment account is required',
        path: ['account_id']
      })
    }
  }
})

// Schema with transform for final validation
export const comprehensiveTransactionFormSchema = comprehensiveTransactionFormInputSchema.transform((data) => ({
  ...data,
  amount: (() => {
    const num = parseFloat(data.amount.replace(/[^\d.-]/g, ''))
    if (isNaN(num) || num <= 0) {
      throw new Error('Amount must be a positive number')
    }
    return num
  })(),
  fees: data.fees ? (() => {
    const num = parseFloat(data.fees.replace(/[^\d.-]/g, ''))
    return isNaN(num) ? 0 : num
  })() : 0,
  investment_quantity: data.investment_quantity ? parseFloat(data.investment_quantity) : undefined,
  investment_price: data.investment_price ? parseFloat(data.investment_price) : undefined,
}))

export type TransactionFormInputData = z.infer<typeof transactionFormInputSchema>
export type TransactionFormData = z.infer<typeof transactionFormSchema>
export type TransactionData = z.infer<typeof transactionSchema>
export type ComprehensiveTransactionFormInputData = z.infer<typeof comprehensiveTransactionFormInputSchema>
export type ComprehensiveTransactionFormData = z.infer<typeof comprehensiveTransactionFormSchema>