import { supabase } from './supabase'
import type { ICategory, ICategoryForm } from '../types'
import type { TablesInsert, TablesUpdate } from '../database.types'

// Helper function to normalize category data from database
function normalizeCategoryFromDB(category: any): ICategory {
  return {
    ...category,
    // Convert type from database format: null -> 'transfer', keep 'income'/'expense' as-is
    type: category.type === null ? 'transfer' : category.type
  }
}

export class CategoryService {
  /**
   * Get all categories for the current user (including system categories)
   */
  static async getCategories(options?: {
    type?: 'income' | 'expense'
    is_active?: boolean
    include_system?: boolean
    parent_id?: string | null
  }): Promise<ICategory[]> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    // Use a simple query without recursive joins to avoid stack depth issues
    let query = supabase
      .from('categories')
      .select('*')
      .or(`user_id.eq.${user.id},and(is_system.eq.true,user_id.is.null)`)
      .order('sort_order')
      .order('name')

    if (options?.type) {
      query = query.eq('type', options.type.toUpperCase())
    }

    if (options?.is_active !== undefined) {
      query = query.eq('is_active', options.is_active)
    }

    if (options?.include_system === false) {
      query = query.eq('is_system', false)
    }

    if (options?.parent_id !== undefined) {
      if (options.parent_id === null) {
        query = query.is('parent_category_id', null)
      } else {
        query = query.eq('parent_category_id', options.parent_id)
      }
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch categories: ${error.message}`)
    }

    return data.map(normalizeCategoryFromDB)
  }


  /**
   * Get a specific category by ID
   */
  static async getCategory(id: string): Promise<ICategory> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    // Use simple query without recursive joins
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .or(`user_id.eq.${user.id},and(is_system.eq.true,user_id.is.null)`)
      .single()

    if (error) {
      throw new Error(`Failed to fetch category: ${error.message}`)
    }

    return normalizeCategoryFromDB(data)
  }

  /**
   * Create a new category
   */
  static async createCategory(categoryData: ICategoryForm): Promise<ICategory> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }


    // Check if category name already exists for this user within the same category type
    const dbType = categoryData.type === 'transfer' ? null : categoryData.type
    let existingCategoryQuery = supabase
      .from('categories')
      .select('id')
      .eq('user_id', user.id)
      .eq('name', categoryData.name)

    // Check within the same category type
    if (dbType === null) {
      existingCategoryQuery = existingCategoryQuery.is('type', null)
    } else {
      existingCategoryQuery = existingCategoryQuery.eq('type', dbType!)
    }

    const { data: existingCategory } = await existingCategoryQuery.single()

    if (existingCategory) {
      throw new Error(`Category name already exists in ${categoryData.type} categories`)
    }

    // Validate parent category if specified
    if (categoryData.parent_category_id) {
      const { data: parentCategory } = await supabase
        .from('categories')
        .select('id, user_id, is_system')
        .eq('id', categoryData.parent_category_id)
        .single()

      if (!parentCategory || (parentCategory.user_id !== user.id && !parentCategory.is_system)) {
        throw new Error('Invalid parent category specified')
      }
    }

    const insertData: TablesInsert<'categories'> = {
      name: categoryData.name,
      icon: categoryData.icon,
      color: categoryData.color,
      description: categoryData.description,
      parent_category_id: categoryData.parent_category_id,
      // Database constraint only allows 'income' and 'expense' (lowercase)
      // Transfer categories are stored as null type
      type: categoryData.type === 'transfer' ? null : categoryData.type,
      user_id: user.id,
      is_default: false,
      is_system: false,
      is_active: true,
      sort_order: categoryData.sort_order || 0,
    }


    const { data, error } = await supabase
      .from('categories')
      .insert(insertData)
      .select('*')
      .single()

    if (error) {
      throw new Error(`Failed to create category: ${error.message}`)
    }

    return normalizeCategoryFromDB(data)
  }

  /**
   * Update an existing category
   */
  static async updateCategory(id: string, updates: Partial<ICategoryForm>): Promise<ICategory> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Check if category exists and belongs to user (can't update system categories)
    const { data: existingCategory } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .eq('is_system', false)
      .single()

    if (!existingCategory) {
      throw new Error('Category not found or cannot be modified')
    }

    // Check for name conflicts if name is being updated
    if (updates.name && updates.name !== existingCategory.name) {
      // Use the updated type if provided, otherwise use the existing type
      const categoryType = updates.type || existingCategory.type
      const dbType = categoryType === 'transfer' ? null : categoryType
      
      let nameConflictQuery = supabase
        .from('categories')
        .select('id')
        .eq('user_id', user.id)
        .eq('name', updates.name)
        .neq('id', id)

      // Check within the same category type
      if (dbType === null) {
        nameConflictQuery = nameConflictQuery.is('type', null)
      } else {
        nameConflictQuery = nameConflictQuery.eq('type', dbType!)
      }

      const { data: nameConflict } = await nameConflictQuery.single()

      if (nameConflict) {
        const displayType = categoryType === null ? 'transfer' : categoryType
        throw new Error(`Category name already exists in ${displayType} categories`)
      }
    }

    // Validate parent category if being updated
    if (updates.parent_category_id) {
      const { data: parentCategory } = await supabase
        .from('categories')
        .select('id, user_id, is_system')
        .eq('id', updates.parent_category_id)
        .single()

      if (!parentCategory || (parentCategory.user_id !== user.id && !parentCategory.is_system)) {
        throw new Error('Invalid parent category specified')
      }

      // Prevent circular references
      if (updates.parent_category_id === id) {
        throw new Error('Category cannot be its own parent')
      }
    }

    const updateData: TablesUpdate<'categories'> = {
      name: updates.name,
      icon: updates.icon,
      color: updates.color,
      description: updates.description,
      parent_category_id: updates.parent_category_id,
      sort_order: updates.sort_order,
      // Database constraint only allows 'income' and 'expense' (lowercase)
      // Transfer categories are stored as null type
      type: updates.type === 'transfer' ? null : updates.type,
      updated_at: new Date().toISOString(),
    }

    const { data, error } = await supabase
      .from('categories')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select('*')
      .single()

    if (error) {
      throw new Error(`Failed to update category: ${error.message}`)
    }

    return normalizeCategoryFromDB(data)
  }

  /**
   * Delete a category (soft delete by setting is_active to false)
   */
  static async deleteCategory(id: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Check if category exists and belongs to user (can't delete system categories)
    const { data: category } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .eq('is_system', false)
      .single()

    if (!category) {
      throw new Error('Category not found or cannot be deleted')
    }

    // Check if category has transactions
    const { data: transactions } = await supabase
      .from('transactions')
      .select('id')
      .eq('category_id', id)
      .limit(1)

    // Check if category has subcategories
    const { data: subcategories } = await supabase
      .from('categories')
      .select('id')
      .eq('parent_category_id', id)
      .eq('is_active', true)
      .limit(1)

    if (transactions && transactions.length > 0) {
      // Soft delete if category has transactions
      const { error } = await supabase
        .from('categories')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('user_id', user.id)

      if (error) {
        throw new Error(`Failed to deactivate category: ${error.message}`)
      }
    } else if (subcategories && subcategories.length > 0) {
      throw new Error('Cannot delete category with active subcategories')
    } else {
      // Hard delete if no transactions or subcategories
      const { error } = await supabase
        .from('categories')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id)

      if (error) {
        throw new Error(`Failed to delete category: ${error.message}`)
      }
    }
  }

  /**
   * Get category hierarchy (parent categories with their subcategories)
   */
  static async getCategoryHierarchy(type?: 'income' | 'expense'): Promise<ICategory[]> {
    const categories = await this.getCategories({ 
      type, 
      is_active: true,
      include_system: true 
    })

    // Build hierarchy
    const categoryMap = new Map<string, ICategory>()
    const rootCategories: ICategory[] = []

    // First pass: create map and identify root categories
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, subcategories: [] })
      if (!category.parent_category_id) {
        rootCategories.push(categoryMap.get(category.id)!)
      }
    })

    // Second pass: build parent-child relationships
    categories.forEach(category => {
      if (category.parent_category_id) {
        const parent = categoryMap.get(category.parent_category_id)
        const child = categoryMap.get(category.id)
        if (parent && child) {
          parent.subcategories = parent.subcategories || []
          parent.subcategories.push(child)
        }
      }
    })

    return rootCategories
  }

  /**
   * Get category usage statistics
   */
  static async getCategoryStats(options?: {
    startDate?: string
    endDate?: string
  }): Promise<Array<{
    category: ICategory
    transactionCount: number
    totalAmount: number
    averageAmount: number
  }>> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    let query = supabase
      .from('transactions')
      .select(`
        category_id,
        amount,
        category:categories(*)
      `)
      .eq('user_id', user.id)
      .not('category_id', 'is', null)

    if (options?.startDate) {
      query = query.gte('transaction_date', options.startDate)
    }

    if (options?.endDate) {
      query = query.lte('transaction_date', options.endDate)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch category statistics: ${error.message}`)
    }

    // Group by category and calculate stats
    const statsMap = new Map<string, {
      category: ICategory
      transactionCount: number
      totalAmount: number
    }>()

    data?.forEach(transaction => {
      if (transaction.category) {
        const categoryId = transaction.category_id!
        const existing = statsMap.get(categoryId) || {
          category: transaction.category as ICategory,
          transactionCount: 0,
          totalAmount: 0,
        }

        existing.transactionCount++
        existing.totalAmount += transaction.amount

        statsMap.set(categoryId, existing)
      }
    })

    // Convert to array and add average
    return Array.from(statsMap.values()).map(stat => ({
      ...stat,
      averageAmount: stat.transactionCount > 0 ? stat.totalAmount / stat.transactionCount : 0,
    })).sort((a, b) => b.totalAmount - a.totalAmount)
  }

  /**
   * Reorder categories
   */
  static async reorderCategories(categoryOrders: { id: string; sort_order: number }[]): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Update sort orders in batch
    for (const { id, sort_order } of categoryOrders) {
      const { error } = await supabase
        .from('categories')
        .update({ 
          sort_order,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('user_id', user.id)
        .eq('is_system', false) // Only allow reordering user categories

      if (error) {
        console.error(`Failed to update sort order for category ${id}:`, error)
      }
    }
  }
}
