import { supabase } from './supabase'
import { AssetClassService, HoldingService } from './assets'
import type { IHolding, IAssetClassInfo } from '../types/assets'

export interface IRealizedGainLoss {
  transaction_id: string
  asset_id: string
  asset_symbol: string
  asset_name: string
  quantity_sold: number
  purchase_price: number
  sale_price: number
  purchase_date: string
  sale_date: string
  holding_period_days: number
  holding_period_months: number
  is_long_term: boolean
  gross_gain_loss: number
  applicable_tax_rate: number
  tax_amount: number
  net_gain_loss: number
  asset_class: IAssetClassInfo
}

export interface IUnrealizedGainLoss {
  holding_id: string
  asset_id: string
  asset_symbol: string
  asset_name: string
  quantity: number
  average_cost: number
  current_price: number
  total_invested: number
  current_value: number
  unrealized_gain_loss: number
  unrealized_percentage: number
  days_held: number
  months_held: number
  asset_class: IAssetClassInfo
}

export interface IPortfolioSummary {
  total_invested: number
  current_value: number
  total_unrealized_gain_loss: number
  total_unrealized_percentage: number
  total_realized_gain_loss: number
  total_tax_paid: number
  net_portfolio_gain_loss: number
  holdings_count: number
  asset_classes_breakdown: {
    [key: string]: {
      invested: number
      current_value: number
      gain_loss: number
      percentage: number
    }
  }
}

export class ProfitLossService {
  /**
   * Calculate realized gain/loss for a sell transaction
   */
  static async calculateRealizedGainLoss(
    assetId: string,
    quantitySold: number,
    salePrice: number,
    saleDate: string,
    averageCost: number,
    purchaseDate: string
  ): Promise<IRealizedGainLoss> {
    // Get asset information
    const { AssetService } = await import('./assets')
    const asset = await AssetService.getAsset(assetId)
    
    if (!asset.asset_class) {
      throw new Error('Asset class information not found')
    }

    // Calculate holding period
    const purchaseDateObj = new Date(purchaseDate)
    const saleDateObj = new Date(saleDate)
    const holdingPeriodDays = Math.floor((saleDateObj.getTime() - purchaseDateObj.getTime()) / (1000 * 60 * 60 * 24))
    const holdingPeriodMonths = Math.floor(holdingPeriodDays / 30.44) // Average days per month

    // Determine if it's long-term based on asset class
    const isLongTerm = holdingPeriodMonths >= asset.asset_class.ltcg_period_months

    // Calculate gross gain/loss
    const grossGainLoss = (salePrice - averageCost) * quantitySold

    // Determine applicable tax rate
    let applicableTaxRate = 0
    if (grossGainLoss > 0) { // Only tax on gains
      applicableTaxRate = isLongTerm 
        ? (asset.asset_class.ltcg_tax_rate || 0)
        : (asset.asset_class.stcg_tax_rate || 0)
    }

    // Calculate tax amount
    const taxAmount = Math.max(0, grossGainLoss * (applicableTaxRate / 100))

    // Calculate net gain/loss
    const netGainLoss = grossGainLoss - taxAmount

    return {
      transaction_id: '', // Will be set by caller
      asset_id: assetId,
      asset_symbol: asset.symbol,
      asset_name: asset.name,
      quantity_sold: quantitySold,
      purchase_price: averageCost,
      sale_price: salePrice,
      purchase_date: purchaseDate,
      sale_date: saleDate,
      holding_period_days: holdingPeriodDays,
      holding_period_months: holdingPeriodMonths,
      is_long_term: isLongTerm,
      gross_gain_loss: grossGainLoss,
      applicable_tax_rate: applicableTaxRate,
      tax_amount: taxAmount,
      net_gain_loss: netGainLoss,
      asset_class: asset.asset_class
    }
  }

  /**
   * Calculate unrealized gain/loss for current holdings
   */
  static async calculateUnrealizedGainLoss(holdings: IHolding[]): Promise<IUnrealizedGainLoss[]> {
    const unrealizedGainLoss: IUnrealizedGainLoss[] = []

    for (const holding of holdings) {
      if (!holding.asset || !holding.asset.asset_class) {
        continue
      }

      // Get current price (for now, use the stored current_price or average_cost as fallback)
      const currentPrice = holding.asset.current_price || holding.average_cost

      // Calculate values
      const currentValue = holding.quantity * currentPrice
      const unrealizedGain = currentValue - holding.total_invested
      const unrealizedPercentage = holding.total_invested > 0 
        ? (unrealizedGain / holding.total_invested) * 100 
        : 0

      // Calculate holding period
      const purchaseDate = new Date(holding.created_at)
      const currentDate = new Date()
      const daysHeld = Math.floor((currentDate.getTime() - purchaseDate.getTime()) / (1000 * 60 * 60 * 24))
      const monthsHeld = Math.floor(daysHeld / 30.44)

      unrealizedGainLoss.push({
        holding_id: holding.id,
        asset_id: holding.asset.id,
        asset_symbol: holding.asset.symbol,
        asset_name: holding.asset.name,
        quantity: holding.quantity,
        average_cost: holding.average_cost,
        current_price: currentPrice,
        total_invested: holding.total_invested,
        current_value: currentValue,
        unrealized_gain_loss: unrealizedGain,
        unrealized_percentage: unrealizedPercentage,
        days_held: daysHeld,
        months_held: monthsHeld,
        asset_class: holding.asset.asset_class
      })
    }

    return unrealizedGainLoss
  }

  /**
   * Get portfolio summary with realized and unrealized gains
   */
  static async getPortfolioSummary(accountId?: string): Promise<IPortfolioSummary> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Get current holdings
    const holdings = await HoldingService.getHoldings(accountId ? { account_id: accountId } : undefined)
    
    // Calculate unrealized gains
    const unrealizedGains = await this.calculateUnrealizedGainLoss(holdings)

    // Get realized gains from completed transactions
    let realizedQuery = supabase
      .from('investment_transactions')
      .select(`
        *,
        asset:assets(*,asset_class:asset_classes(*))
      `)
      .eq('user_id', user.id)
      .eq('transaction_type', 'investment_sell')

    if (accountId) {
      realizedQuery = realizedQuery.eq('account_id', accountId)
    }

    const { data: sellTransactions, error } = await realizedQuery

    if (error) {
      throw new Error(`Failed to fetch sell transactions: ${error.message}`)
    }

    // Calculate totals
    const totalInvested = holdings.reduce((sum, h) => sum + h.total_invested, 0)
    const currentValue = unrealizedGains.reduce((sum, u) => sum + u.current_value, 0)
    const totalUnrealizedGainLoss = unrealizedGains.reduce((sum, u) => sum + u.unrealized_gain_loss, 0)
    const totalUnrealizedPercentage = totalInvested > 0 ? (totalUnrealizedGainLoss / totalInvested) * 100 : 0

    // Calculate realized gains (simplified - would need more complex logic for FIFO/LIFO)
    const totalRealizedGainLoss = (sellTransactions || []).reduce((sum, t) => {
      // This is a simplified calculation - in reality, you'd need to track specific lots
      return sum + ((t.investment_price || 0) - (t.average_cost || 0)) * (t.investment_quantity || 0)
    }, 0)

    // Calculate asset class breakdown
    const assetClassesBreakdown: { [key: string]: any } = {}
    
    unrealizedGains.forEach(u => {
      const className = u.asset_class.name
      if (!assetClassesBreakdown[className]) {
        assetClassesBreakdown[className] = {
          invested: 0,
          current_value: 0,
          gain_loss: 0,
          percentage: 0
        }
      }
      
      assetClassesBreakdown[className].invested += u.total_invested
      assetClassesBreakdown[className].current_value += u.current_value
      assetClassesBreakdown[className].gain_loss += u.unrealized_gain_loss
    })

    // Calculate percentages for each asset class
    Object.keys(assetClassesBreakdown).forEach(className => {
      const breakdown = assetClassesBreakdown[className]
      breakdown.percentage = breakdown.invested > 0 
        ? (breakdown.gain_loss / breakdown.invested) * 100 
        : 0
    })

    return {
      total_invested: totalInvested,
      current_value: currentValue,
      total_unrealized_gain_loss: totalUnrealizedGainLoss,
      total_unrealized_percentage: totalUnrealizedPercentage,
      total_realized_gain_loss: totalRealizedGainLoss,
      total_tax_paid: 0, // Would need to track this separately
      net_portfolio_gain_loss: totalUnrealizedGainLoss + totalRealizedGainLoss,
      holdings_count: holdings.length,
      asset_classes_breakdown: assetClassesBreakdown
    }
  }

  /**
   * Get detailed profit/loss report
   */
  static async getProfitLossReport(options?: {
    account_id?: string
    start_date?: string
    end_date?: string
    asset_class_id?: string
  }) {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Get holdings for unrealized gains
    const holdings = await HoldingService.getHoldings(options?.account_id ? { account_id: options.account_id } : undefined)
    const unrealizedGains = await this.calculateUnrealizedGainLoss(holdings)

    // Get realized gains from sell transactions
    let realizedQuery = supabase
      .from('investment_transactions')
      .select(`
        *,
        asset:assets(*,asset_class:asset_classes(*))
      `)
      .eq('user_id', user.id)
      .eq('transaction_type', 'investment_sell')

    if (options?.account_id) {
      realizedQuery = realizedQuery.eq('account_id', options.account_id)
    }

    if (options?.start_date) {
      realizedQuery = realizedQuery.gte('transaction_date', options.start_date)
    }

    if (options?.end_date) {
      realizedQuery = realizedQuery.lte('transaction_date', options.end_date)
    }

    const { data: sellTransactions, error } = await realizedQuery

    if (error) {
      throw new Error(`Failed to fetch transactions: ${error.message}`)
    }

    // Filter by asset class if specified
    const filteredUnrealized = options?.asset_class_id 
      ? unrealizedGains.filter(u => u.asset_class.id === options.asset_class_id)
      : unrealizedGains

    const filteredRealized = options?.asset_class_id 
      ? (sellTransactions || []).filter(t => t.asset?.asset_class_id === options.asset_class_id)
      : (sellTransactions || [])

    return {
      unrealized_gains: filteredUnrealized,
      realized_transactions: filteredRealized,
      summary: await this.getPortfolioSummary(options?.account_id)
    }
  }
}
