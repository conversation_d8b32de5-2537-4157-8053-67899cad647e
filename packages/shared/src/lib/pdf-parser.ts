// TypeScript interfaces for bank statement data
export interface ParsedTransaction {
  date: string;
  narration: string;
  refNo: string;
  valueDate: string;
  withdrawalAmount: number | null;
  depositAmount: number | null;
  closingBalance: number;
  type: 'credit' | 'debit';
}

export interface BankStatementData {
  accountHolder: string;
  accountNumber: string;
  statementPeriod: {
    from: string;
    to: string;
  };
  openingBalance: number;
  closingBalance: number;
  transactions: ParsedTransaction[];
  summary: {
    totalDebits: number;
    totalCredits: number;
    debitCount: number;
    creditCount: number;
  };
  rawCsvContent?: string; // Raw CSV content from Python script
}

// CSV parsing utilities to convert Python script output to TypeScript objects
export class CSVParser {
  static parseCSVToBankStatementData(csvContent: string): BankStatementData {
    const lines = csvContent.split('\n').filter(line => line.trim());
    if (lines.length < 2) {
      throw new Error('Invalid CSV format: missing header or data');
    }

    const header = this.parseCSVLine(lines[0]);
    const transactions: ParsedTransaction[] = [];

    // Parse transactions and filter out empty rows
    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCSVLine(lines[i]);
      if (values.length < header.length) continue;

      const transaction = this.parseTransactionFromCSV(header, values);
      if (transaction && transaction.date && transaction.date !== '') {
        transactions.push(transaction);
      }
    }

    // Extract basic statement information (same as original)
    const accountHolder = 'Bank Statement Account';
    const accountNumber = 'N/A';

    // Calculate statement period from transaction dates with proper date parsing
    const dates = transactions.map(t => {
      return this.parseDate(t.date);
    }).filter(d => d !== null);

    const minDate = dates.length > 0 ? new Date(Math.min(...dates.map(d => d.getTime()))) : new Date();
    const maxDate = dates.length > 0 ? new Date(Math.max(...dates.map(d => d.getTime()))) : new Date();

    const statementPeriod = {
      from: minDate.toLocaleDateString('en-IN'),
      to: maxDate.toLocaleDateString('en-IN')
    };

    // Calculate balances (same as original)
    const balances = transactions.map(t => t.closingBalance).filter(b => b > 0);
    const openingBalance = balances.length > 0 ? balances[0] : 0;
    const closingBalance = balances.length > 0 ? balances[balances.length - 1] : 0;

    return {
      accountHolder,
      accountNumber,
      statementPeriod,
      openingBalance,
      closingBalance,
      transactions,
      summary: this.calculateSummary(transactions),
      rawCsvContent: csvContent
    };
  }

  private static parseDate(dateStr: string): Date | null {
    if (!dateStr) return null;

    // Try parsing different date formats (same as original)
    let parsedDate = null;

    // Try YYYY-MM-DD format
    if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
      parsedDate = new Date(dateStr);
    }
    // Try DD-MM-YYYY format
    else if (dateStr.match(/^\d{2}-\d{2}-\d{4}$/)) {
      const [day, month, year] = dateStr.split('-');
      parsedDate = new Date(`${year}-${month}-${day}`);
    }
    // Try DD/MM/YY format
    else if (dateStr.match(/^\d{2}\/\d{2}\/\d{2}$/)) {
      const [day, month, year] = dateStr.split('/');
      const fullYear = `20${year}`;
      parsedDate = new Date(`${fullYear}-${month}-${day}`);
    }
    // Try DD/MM/YYYY format
    else if (dateStr.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
      const [day, month, year] = dateStr.split('/');
      parsedDate = new Date(`${year}-${month}-${day}`);
    }

    return parsedDate && !isNaN(parsedDate.getTime()) ? parsedDate : null;
  }

  private static parseCSVLine(line: string): string[] {
    const values: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        values.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    values.push(current.trim());
    return values;
  }

  private static parseTransactionFromCSV(header: string[], values: string[]): ParsedTransaction | null {
    try {
      const getField = (field: string): string => {
        const index = header.findIndex(h => h.toLowerCase().includes(field.toLowerCase()));
        return index >= 0 ? values[index]?.replace(/"/g, '') || '' : '';
      };

      const date = getField('date');
      const narration = getField('narration');
      const refNo = getField('ref');
      const valueDate = getField('value') || date;
      const withdrawalStr = getField('withdrawal');
      const depositStr = getField('deposit');
      const closingBalanceStr = getField('closing');

      // Parse amounts
      const withdrawalAmount = withdrawalStr ? parseFloat(withdrawalStr.replace(/,/g, '')) : null;
      const depositAmount = depositStr ? parseFloat(depositStr.replace(/,/g, '')) : null;
      const closingBalance = closingBalanceStr ? parseFloat(closingBalanceStr.replace(/,/g, '')) : 0;

      // Determine transaction type
      const type: 'credit' | 'debit' = depositAmount !== null && depositAmount > 0 ? 'credit' : 'debit';

      return {
        date,
        narration,
        refNo,
        valueDate,
        withdrawalAmount,
        depositAmount,
        closingBalance,
        type
      };
    } catch (error) {
      console.warn('Failed to parse transaction from CSV:', error);
      return null;
    }
  }

  private static calculateSummary(transactions: ParsedTransaction[]) {
    let totalDebits = 0;
    let totalCredits = 0;
    let debitCount = 0;
    let creditCount = 0;

    transactions.forEach(transaction => {
      if (transaction.type === 'debit' && transaction.withdrawalAmount) {
        totalDebits += transaction.withdrawalAmount;
        debitCount++;
      } else if (transaction.type === 'credit' && transaction.depositAmount) {
        totalCredits += transaction.depositAmount;
        creditCount++;
      }
    });

    return {
      totalDebits,
      totalCredits,
      debitCount,
      creditCount
    };
  }
}
