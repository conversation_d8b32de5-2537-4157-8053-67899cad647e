import { supabase } from './supabase'
import type { IAccount, IAccountForm, AccountType } from '../types'
import type { TablesInsert, TablesUpdate } from '../database.types'

export class AccountService {
  /**
   * Get all accounts for the current user
   */
  static async getAccounts(options?: {
    account_type?: AccountType
    is_active?: boolean
  }): Promise<IAccount[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    let query = supabase
      .from('accounts')
      .select('*')
      .eq('user_id', user.id)
      .order('is_primary', { ascending: false })
      .order('account_type')
      .order('display_order', { ascending: true })
      .order('name')

    if (options?.account_type) {
      query = query.eq('account_type', options.account_type)
    }

    if (options?.is_active !== undefined) {
      query = query.eq('is_active', options.is_active)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch accounts: ${error.message}`)
    }

    return data as IAccount[]
  }

  /**
   * Get a specific account by ID
   */
  static async getAccount(id: string): Promise<IAccount> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('accounts')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (error) {
      throw new Error(`Failed to fetch account: ${error.message}`)
    }

    return data as IAccount
  }

  /**
   * Create a new account
   */
  static async createAccount(accountData: IAccountForm): Promise<IAccount> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Check if account name already exists for this user
    const { data: existingAccount } = await supabase
      .from('accounts')
      .select('id')
      .eq('user_id', user.id)
      .eq('name', accountData.name)
      .single()

    if (existingAccount) {
      throw new Error('Account name already exists')
    }

    // Validate credit limit constraint
    if (accountData.credit_limit && accountData.account_type !== 'credit_card') {
      throw new Error('Only credit card accounts can have a credit limit')
    }

    // Validate balance constraint
    if ((accountData.current_balance || 0) < 0 && accountData.account_type !== 'credit_card') {
      throw new Error('Account balance cannot be negative unless it is a credit card')
    }

    // If this is set as primary, unset other primary accounts of the same type
    if (accountData.is_primary) {
      await supabase
        .from('accounts')
        .update({ is_primary: false })
        .eq('user_id', user.id)
        .eq('account_type', accountData.account_type)
    }

    // Get the next display order for this account type
    const { data: existingAccounts } = await supabase
      .from('accounts')
      .select('display_order')
      .eq('user_id', user.id)
      .eq('account_type', accountData.account_type)
      .order('display_order', { ascending: false })
      .limit(1)

    const nextDisplayOrder = existingAccounts && existingAccounts.length > 0 
      ? (existingAccounts[0].display_order || 0) + 1 
      : 1

    const insertData: TablesInsert<'accounts'> = {
      ...accountData,
      user_id: user.id,
      current_balance: accountData.current_balance || 0,
      is_active: true,
      is_primary: accountData.is_primary || false,
      display_order: nextDisplayOrder,
    }

    const { data, error } = await supabase
      .from('accounts')
      .insert(insertData)
      .select('*')
      .single()

    if (error) {
      throw new Error(`Failed to create account: ${error.message}`)
    }

    return data as IAccount
  }

  /**
   * Update an existing account
   */
  static async updateAccount(id: string, updates: Partial<IAccountForm>): Promise<IAccount> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // If updating to primary, unset other primary accounts of the same type
    if (updates.is_primary) {
      const account = await this.getAccount(id)
      await supabase
        .from('accounts')
        .update({ is_primary: false })
        .eq('user_id', user.id)
        .eq('account_type', account.account_type)
        .neq('id', id)
    }

    const updateData: TablesUpdate<'accounts'> = {
      ...updates,
      updated_at: new Date().toISOString(),
    }

    const { data, error } = await supabase
      .from('accounts')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select('*')
      .single()

    if (error) {
      throw new Error(`Failed to update account: ${error.message}`)
    }

    return data as IAccount
  }

  /**
   * Update display order for accounts within the same type
   */
  static async updateAccountDisplayOrder(
    accountId: string, 
    newOrder: number, 
    accountType: AccountType
  ): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Update the specific account's display order
    const { error } = await supabase
      .from('accounts')
      .update({ 
        display_order: newOrder,
        updated_at: new Date().toISOString()
      })
      .eq('id', accountId)
      .eq('user_id', user.id)
      .eq('account_type', accountType)

    if (error) {
      throw new Error(`Failed to update account display order: ${error.message}`)
    }
  }

  /**
   * Reorder multiple accounts at once (for drag and drop)
   */
  static async reorderAccounts(accountUpdates: Array<{
    id: string
    display_order: number
    account_type: AccountType
  }>): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Update all accounts in a single transaction
    const updates = accountUpdates.map(({ id, display_order }) => 
      supabase
        .from('accounts')
        .update({ 
          display_order,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('user_id', user.id)
    )

    // Execute all updates
    const results = await Promise.all(updates)
    
    // Check if any update failed
    const errors = results.filter(result => result.error)
    if (errors.length > 0) {
      throw new Error(`Failed to reorder accounts: ${errors[0].error?.message}`)
    }
  }

  /**
   * Delete an account (soft delete by setting is_active to false)
   */
  static async deleteAccount(id: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Check if account has transactions
    const { data: transactions } = await supabase
      .from('transactions')
      .select('id')
      .or(`account_id.eq.${id},to_account_id.eq.${id}`)
      .limit(1)

    if (transactions && transactions.length > 0) {
      // Soft delete if account has transactions
      const { error } = await supabase
        .from('accounts')
        .update({ 
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('user_id', user.id)

      if (error) {
        throw new Error(`Failed to deactivate account: ${error.message}`)
      }
    } else {
      // Hard delete if no transactions
      const { error } = await supabase
        .from('accounts')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id)

      if (error) {
        throw new Error(`Failed to delete account: ${error.message}`)
      }
    }
  }

  /**
   * Get account balance history
   */
  static async getAccountBalanceHistory(
    accountId: string,
    options?: {
      startDate?: string
      endDate?: string
      limit?: number
    }
  ): Promise<any[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    let query = supabase
      .from('account_balance_history')
      .select(`
        *,
        transaction:transactions(*)
      `)
      .eq('account_id', accountId)
      .order('balance_date', { ascending: false })

    if (options?.startDate) {
      query = query.gte('balance_date', options.startDate)
    }

    if (options?.endDate) {
      query = query.lte('balance_date', options.endDate)
    }

    if (options?.limit) {
      query = query.limit(options.limit)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch balance history: ${error.message}`)
    }

    return data || []
  }

  /**
   * Create default accounts for a new user
   */
  static async createDefaultAccounts(userId?: string): Promise<IAccount[]> {
    let user
    
    if (userId) {
      // Use provided user ID (for immediate post-signup creation)
      user = { id: userId }
    } else {
      // Get current user from session
      const { data: { user: currentUser } } = await supabase.auth.getUser()
      user = currentUser
    }
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // First, check if user already has any accounts to prevent duplicates
    const { data: existingAccountsCheck, error: checkError } = await supabase
      .from('accounts')
      .select('id')
      .eq('user_id', user.id)
      .limit(1)
    
    if (checkError) {
      console.error('Error checking existing accounts:', checkError)
    }
    
    if (existingAccountsCheck && existingAccountsCheck.length > 0) {
      console.log('User already has accounts, skipping default account creation')
      // Return existing accounts using the proper method
      return await this.getAccounts()
    }

    const defaultAccounts: IAccountForm[] = [
      {
        name: 'Cash',
        account_type: 'cash',
        current_balance: 0, // Start with zero balance
        is_primary: true,
      }
    ]

    const createdAccounts: IAccount[] = []

    for (const accountData of defaultAccounts) {
      try {
        // Double-check for specific account name to prevent race conditions
        const { data: existingAccount } = await supabase
          .from('accounts')
          .select('id')
          .eq('user_id', user.id)
          .eq('name', accountData.name)
          .single()

        if (existingAccount) {
          console.log(`Account "${accountData.name}" already exists, skipping creation`)
          continue
        }

        const account = await this.createAccount(accountData)
        createdAccounts.push(account)
        console.log(`Successfully created default account: ${accountData.name}`)
      } catch (error) {
        // Only log error if it's not a duplicate constraint error
        if (error instanceof Error && !error.message.includes('duplicate key value')) {
          console.error('Failed to create default account:', error)
        } else {
          console.log(`Account "${accountData.name}" already exists (detected via constraint)`)
        }
      }
    }

    return createdAccounts
  }

  /**
   * Get account summary with balances by type
   */
  static async getAccountSummary(): Promise<{
    totalAssets: number
    totalLiabilities: number
    netWorth: number
    accountsByType: Record<AccountType, { count: number; balance: number }>
  }> {
    const accounts = await this.getAccounts({ is_active: true })

    let totalAssets = 0
    let totalLiabilities = 0
    const accountsByType: Record<AccountType, { count: number; balance: number }> = {
      bank: { count: 0, balance: 0 },
      investment: { count: 0, balance: 0 },
      savings: { count: 0, balance: 0 },
      credit_card: { count: 0, balance: 0 },
      cash: { count: 0, balance: 0 },
      loan: { count: 0, balance: 0 },
    }

    accounts.forEach(account => {
      const balance = account.current_balance || 0
      
      accountsByType[account.account_type].count++
      accountsByType[account.account_type].balance += balance

      if (account.account_type === 'credit_card') {
        // Credit card balances are liabilities (negative is debt)
        totalLiabilities += Math.abs(balance)
      } else {
        totalAssets += balance
      }
    })

    return {
      totalAssets,
      totalLiabilities,
      netWorth: totalAssets - totalLiabilities,
      accountsByType,
    }
  }
}
