import { supabase } from './supabase'
import type { ITransaction, ICategory, IAnalyticsData, IDateRange } from '../types'

export class AnalyticsService {
  static async getAnalyticsData(dateRange?: IDateRange): Promise<IAnalyticsData> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Default to last 6 months if no range provided
    const endDate = dateRange?.endDate || new Date().toISOString().split('T')[0]
    const startDate = dateRange?.startDate || new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*)
      `)
      .eq('user_id', user.id)
      .gte('transaction_date', startDate)
      .lte('transaction_date', endDate)
      .order('transaction_date', { ascending: true })

    if (error) {
      throw new Error(`Failed to fetch analytics data: ${error.message}`)
    }

    const transactions = data as ITransaction[]

    // Debug logging for fees analysis
    const transactionsWithFees = transactions.filter(t => t.fees && t.fees > 0)
    if (transactionsWithFees.length > 0) {
      console.log('💰 Fees analysis (NEW LOGIC - all transfer fees are expenses):')
      transactionsWithFees.forEach(t => {
        const fees = t.fees || 0
        if (t.transaction_type === 'expense') {
          console.log(`✅ Expense: $${t.amount} + $${fees} fees = $${t.amount + fees} total`)
        } else if (t.transaction_type === 'transfer' && !t.is_internal) {
          console.log(`✅ Non-internal transfer: $${t.amount} + $${fees} fees = $${t.amount + fees} total expense`)
        } else if (t.transaction_type === 'transfer' && t.is_internal) {
          console.log(`✅ Internal transfer: $0 amount + $${fees} fees = $${fees} expense (amount excluded, fees included)`)
        }
      })
    } else {
      console.log('ℹ️ No transactions with fees found in dataset')
    }

    // Debug logging for transfer analysis
    const transfers = transactions.filter(t => t.transaction_type === 'transfer')
    const nonInternalTransfers = transfers.filter(t => !t.is_internal)
    if (transfers.length > 0) {
      console.log('🔍 Transfer Analysis:', {
        totalTransfers: transfers.length,
        internalTransfers: transfers.filter(t => t.is_internal).length,
        nonInternalTransfers: nonInternalTransfers.length,
      })
    }

    return this.processAnalyticsData(transactions)
  }

  private static processAnalyticsData(transactions: ITransaction[]): IAnalyticsData {
    // Calculate totals - include non-internal transfers as expenses
    const totalIncome = transactions
      .filter(t => t.transaction_type === 'income')
      .reduce((sum, t) => sum + t.amount, 0)

    // Calculate expenses: include all expenses + non-internal transfer amounts + ALL transfer fees
    const expenseTransactions = transactions.filter(t => t.transaction_type === 'expense')
    const nonInternalTransfers = transactions.filter(t => t.transaction_type === 'transfer' && !t.is_internal)
    const allTransfers = transactions.filter(t => t.transaction_type === 'transfer')
    
    console.log('💸 Expense calculation breakdown:')
    console.log('- Regular expenses:', expenseTransactions.length)
    console.log('- Non-internal transfers:', nonInternalTransfers.length) 
    console.log('- All transfers (for fees):', allTransfers.length)
    
    // Calculate total expenses
    const expenseAmounts = expenseTransactions.reduce((sum, t) => sum + t.amount + (t.fees || 0), 0)
    const nonInternalTransferAmounts = nonInternalTransfers.reduce((sum, t) => sum + t.amount, 0) // Amount only, fees handled separately
    const allTransferFees = allTransfers.reduce((sum, t) => sum + (t.fees || 0), 0) // ALL transfer fees (internal + non-internal)
    
    const totalExpenses = expenseAmounts + nonInternalTransferAmounts + allTransferFees

    const netIncome = totalIncome - totalExpenses

    // Debug logging for totals
    if (allTransferFees > 0 || expenseTransactions.some(t => t.fees && t.fees > 0)) {
      console.log('💰 Expense breakdown with fees:', {
        expenseAmounts: `$${expenseAmounts.toFixed(2)}`,
        nonInternalTransferAmounts: `$${nonInternalTransferAmounts.toFixed(2)}`, 
        allTransferFees: `$${allTransferFees.toFixed(2)} (includes internal transfer fees)`,
        totalExpenses: `$${totalExpenses.toFixed(2)}`
      })
    }

    // Category breakdown with special handling for internal transfer fees
    const categoryMap: Record<string, { category: ICategory; total: number; count: number }> = {}
    let internalTransferFeesTotal = 0
    let internalTransferFeesCount = 0
    
    transactions.forEach(transaction => {
      // Handle internal transfer fees separately, regardless of category
      if (transaction.transaction_type === 'transfer' && transaction.is_internal && transaction.fees && transaction.fees > 0) {
        internalTransferFeesTotal += transaction.fees
        internalTransferFeesCount += 1
        return // Skip adding to regular categories
      }
      
      if (transaction.category) {
        const categoryId = transaction.category.id
        if (!categoryMap[categoryId]) {
          categoryMap[categoryId] = {
            category: transaction.category,
            total: 0,
            count: 0
          }
        }
        
        // Handle different transaction types
        if (transaction.transaction_type === 'expense') {
          // Regular expenses: include amount + fees
          categoryMap[categoryId].total += transaction.amount + (transaction.fees || 0)
          categoryMap[categoryId].count += 1
        } else if (transaction.transaction_type === 'transfer' && !transaction.is_internal) {
          // Non-internal transfers: include amount + fees
          categoryMap[categoryId].total += transaction.amount + (transaction.fees || 0)
          categoryMap[categoryId].count += 1
        } else if (transaction.transaction_type === 'income') {
          // Income transactions
          categoryMap[categoryId].total += transaction.amount
          categoryMap[categoryId].count += 1
        }
      }
    })
    
    // Add virtual "Internal transfer fees" category if there are any fees
    if (internalTransferFeesTotal > 0) {
      const virtualCategoryId = 'internal-transfer-fees'
      categoryMap[virtualCategoryId] = {
        category: {
          id: virtualCategoryId,
          name: 'Internal transfer fees',
          type: 'expense',
          icon: 'transfer',
          color: '#6B7280'
        } as ICategory,
        total: internalTransferFeesTotal,
        count: internalTransferFeesCount
      }
      
      console.log(`🏷️ Created "Internal transfer fees" category: $${internalTransferFeesTotal.toFixed(2)} from ${internalTransferFeesCount} transactions`)
    }

    const categoryBreakdown = Object.values(categoryMap)
      .map(item => ({
        category: item.category,
        total: item.total,
        percentage: (item.category.type === 'expense' || item.category.type === 'transfer') && totalExpenses > 0 
          ? (item.total / totalExpenses) * 100 
          : item.category.type === 'income' && totalIncome > 0 
          ? (item.total / totalIncome) * 100 
          : 0
      }))
      .sort((a, b) => b.total - a.total)

    // Debug logging for category breakdown
    console.log('📊 Category Breakdown:', {
      totalCategories: categoryBreakdown.length,
      categories: categoryBreakdown.map(item => ({
        name: item.category.name,
        type: item.category.type,
        total: item.total,
        percentage: item.percentage
      }))
    })

    // Monthly trends - include all relevant transactions 
    const monthlyMap: Record<string, { income: number; expenses: number }> = {}
    
    transactions.forEach(transaction => {
      const monthKey = transaction.transaction_date.substring(0, 7) // YYYY-MM
      if (!monthlyMap[monthKey]) {
        monthlyMap[monthKey] = { income: 0, expenses: 0 }
      }
      
      if (transaction.transaction_type === 'income') {
        monthlyMap[monthKey].income += transaction.amount
      } else if (transaction.transaction_type === 'expense') {
        monthlyMap[monthKey].expenses += transaction.amount + (transaction.fees || 0)
      } else if (transaction.transaction_type === 'transfer') {
        if (!transaction.is_internal) {
          // Non-internal transfers: include amount + fees as expenses
          monthlyMap[monthKey].expenses += transaction.amount + (transaction.fees || 0)
        } else {
          // Internal transfers: include only fees as expenses (not the transfer amount)
          monthlyMap[monthKey].expenses += (transaction.fees || 0)
        }
      }
    })

    const monthlyTrends = Object.entries(monthlyMap)
      .map(([month, data]) => ({
        month,
        income: data.income,
        expenses: data.expenses,
        net: data.income - data.expenses
      }))
      .sort((a, b) => a.month.localeCompare(b.month))

    // Top categories
    const topCategories = Object.values(categoryMap)
      .map(item => ({
        category: item.category,
        total: item.total,
        transactionCount: item.count
      }))
      .sort((a, b) => b.total - a.total)
      .slice(0, 5)

    return {
      totalIncome,
      totalExpenses,
      netIncome,
      categoryBreakdown,
      monthlyTrends,
      topCategories
    }
  }

  static async getSpendingTrends(months: number = 12): Promise<Array<{
    month: string
    expenses: number
    income: number
  }>> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const endDate = new Date()
    const startDate = new Date()
    startDate.setMonth(startDate.getMonth() - months)

    const { data, error } = await supabase
      .from('transactions')
      .select('amount, transaction_type, transaction_date, is_internal, fees')
      .eq('user_id', user.id)
      .gte('transaction_date', startDate.toISOString().split('T')[0])
      .lte('transaction_date', endDate.toISOString().split('T')[0])
      .order('transaction_date', { ascending: true })

    if (error) {
      throw new Error(`Failed to fetch spending trends: ${error.message}`)
    }

    const transactions = data as Array<{
      amount: number
      transaction_type: 'income' | 'expense' | 'transfer'
      transaction_date: string
      is_internal?: boolean
      fees?: number
    }>

    const monthlyData: Record<string, { income: number; expenses: number }> = {}
    
    transactions.forEach(transaction => {
      const monthKey = transaction.transaction_date.substring(0, 7)
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { income: 0, expenses: 0 }
      }
      
      if (transaction.transaction_type === 'income') {
        monthlyData[monthKey].income += transaction.amount
      } else if (transaction.transaction_type === 'expense') {
        monthlyData[monthKey].expenses += transaction.amount + (transaction.fees || 0)
      } else if (transaction.transaction_type === 'transfer') {
        if (!transaction.is_internal) {
          // Non-internal transfers: include amount + fees as expenses
          monthlyData[monthKey].expenses += transaction.amount + (transaction.fees || 0)
        } else {
          // Internal transfers: include only fees as expenses (not the transfer amount)
          monthlyData[monthKey].expenses += (transaction.fees || 0)
        }
      }
    })

    return Object.entries(monthlyData)
      .map(([month, data]) => ({
        month,
        expenses: data.expenses,
        income: data.income
      }))
      .sort((a, b) => a.month.localeCompare(b.month))
  }


  static formatMonth(monthString: string): string {
    const date = new Date(monthString + '-01')
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      year: 'numeric' 
    })
  }
}