import { supabase } from './supabase'
import type { ITransaction, ITransferForm, ITransferTransaction, IAccount, ICategory } from '../types'
import type { TablesInsert } from '../database.types'
import { v4 as uuidv4 } from 'uuid'

export class TransferService {
  /**
   * Get categories suitable for transfers (includes transfer type and null type categories)
   */
  static async getTransferCategories(): Promise<ICategory[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data: categories, error } = await supabase
      .from('categories')
      .select('*')
      .or(`user_id.eq.${user.id},and(is_system.eq.true,user_id.is.null)`)
      .eq('is_active', true)
      .is('type', null) // Only get categories with null type (transfer categories)
      .order('name')

    if (error) {
      throw new Error(`Failed to fetch transfer categories: ${error.message}`)
    }

    // Normalize categories from database format (convert null type to 'transfer')
    return (categories || []).map(category => ({
      ...category,
      type: category.type === null ? 'transfer' as const : category.type
    })) as ICategory[]
  }
  /**
   * Create a transfer between two accounts
   * This creates a single transaction representing the transfer from source to destination
   */
  static async createTransfer(transferData: ITransferForm): Promise<ITransferTransaction> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Validate accounts exist and belong to user
    const { data: fromAccount, error: fromError } = await supabase
      .from('accounts')
      .select('*')
      .eq('id', transferData.from_account_id)
      .eq('user_id', user.id)
      .single()

    const { data: toAccount, error: toError } = await supabase
      .from('accounts')
      .select('*')
      .eq('id', transferData.to_account_id)
      .eq('user_id', user.id)
      .single()

    if (!fromAccount) {
      throw new Error(`Source account not found or not accessible: ${transferData.from_account_id}`)
    }
    
    if (!toAccount) {
      throw new Error(`Destination account not found or not accessible: ${transferData.to_account_id}`)
    }

    if (fromAccount.id === toAccount.id) {
      throw new Error('Cannot transfer to the same account')
    }

    // Note: Removed balance validation to allow negative balances for user reconciliation

    // Validate that the selected category exists and is accessible to the user
    let selectedCategory: any = null
    
    if (transferData.category_id) {
      const { data: categoryData, error: categoryError } = await supabase
        .from('categories')
        .select('*')
        .eq('id', transferData.category_id)
        .or(`user_id.eq.${user.id},and(is_system.eq.true,user_id.is.null)`)
        .single()
      
      if (categoryData && !categoryError) {
        selectedCategory = categoryData
      }
    }

    // If no category provided or category not found, try to find or create a default transfer category
    if (!selectedCategory) {
      // First try to find an existing transfer category (user or system)
      const { data: existingTransferCategory } = await supabase
        .from('categories')
        .select('*')
        .or(`user_id.eq.${user.id},and(is_system.eq.true,user_id.is.null)`)
        .is('type', null) // null type represents transfer categories
        .eq('is_active', true)
        .limit(1)
        .single()

      if (existingTransferCategory) {
        selectedCategory = existingTransferCategory
      } else {
        // Create a default transfer category
        const { data: newCategory, error: createError } = await supabase
          .from('categories')
          .insert({
            name: 'Internal Transfer',
            description: 'Default category for account transfers',
            type: null, // null type for transfer categories
            icon: 'internal-transfer',
            color: '#3B82F6',
            user_id: user.id,
            is_system: false,
            is_active: true,
            sort_order: 0
          })
          .select('*')
          .single()

        if (createError || !newCategory) {
          throw new Error('Failed to create default transfer category. Please create a transfer category first.')
        }
        
        selectedCategory = newCategory
      }
    }

    // Ensure the category is suitable for transfers (transfer type or null type, but not income/expense)
    if (selectedCategory.type === 'income' || selectedCategory.type === 'expense') {
      throw new Error('Cannot use income or expense categories for transfers. Please select a transfer category.')
    }

    // Create a single transaction representing the transfer
    const transferTransaction: TablesInsert<'transactions'> = {
      amount: transferData.amount,
      description: transferData.description || `Transfer from ${fromAccount.name} to ${toAccount.name}`,
      category_id: selectedCategory.id,
      account_id: transferData.from_account_id, // Source account
      to_account_id: transferData.to_account_id, // Destination account
      is_internal: transferData.is_internal ?? true, // Default to true (exclude from analytics)
      transaction_type: 'transfer',
      transaction_date: transferData.transaction_date,
      transaction_status: 'completed',
      fees: transferData.fees || 0,
      user_id: user.id,
    }

    // Insert the single transfer transaction
    const { data: transactionData, error: transactionError } = await supabase
      .from('transactions')
      .insert(transferTransaction)
      .select(`
        *,
        category:categories(*),
        account:accounts!transactions_account_id_fkey(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .single()

    if (transactionError) {
      throw new Error(`Failed to create transfer transaction: ${transactionError.message}`)
    }

    // Update account balances after successful transaction creation
    try {
      // Update source account balance (subtract amount and fees)
      const totalDeduction = transferData.amount + (transferData.fees || 0)
      const { error: fromBalanceError } = await supabase
        .from('accounts')
        .update({ 
          current_balance: (fromAccount.current_balance || 0) - totalDeduction,
          updated_at: new Date().toISOString()
        })
        .eq('id', transferData.from_account_id)
        .eq('user_id', user.id)

      if (fromBalanceError) {
        // Rollback the transaction
        await supabase.from('transactions').delete().eq('id', transactionData.id)
        throw new Error(`Failed to update source account balance: ${fromBalanceError.message}`)
      }

      // Update destination account balance (add amount - no fees on incoming)
      const { error: toBalanceError } = await supabase
        .from('accounts')
        .update({ 
          current_balance: (toAccount.current_balance || 0) + transferData.amount,
          updated_at: new Date().toISOString()
        })
        .eq('id', transferData.to_account_id)
        .eq('user_id', user.id)

      if (toBalanceError) {
        // Rollback transaction and revert source account balance
        await supabase.from('transactions').delete().eq('id', transactionData.id)
        await supabase
          .from('accounts')
          .update({ 
            current_balance: fromAccount.current_balance,
            updated_at: new Date().toISOString()
          })
          .eq('id', transferData.from_account_id)
          .eq('user_id', user.id)
        
        throw new Error(`Failed to update destination account balance: ${toBalanceError.message}`)
      }

    } catch (balanceError) {
      // If balance update fails, rollback the transaction
      await supabase.from('transactions').delete().eq('id', transactionData.id)
      throw balanceError
    }

    // Return a transfer object based on the single transaction
    return {
      id: transactionData.id,
      amount: transferData.amount,
      description: transferData.description,
      from_account_id: transferData.from_account_id,
      to_account_id: transferData.to_account_id,
      transfer_id: transactionData.id, // Use transaction ID as transfer ID
      transaction_date: transferData.transaction_date,
      fees: transferData.fees,
      user_id: user.id,
      from_account: fromAccount as IAccount,
      to_account: toAccount as IAccount,
    }
  }

  /**
   * Get all transfers for the current user
   */
  static async getTransfers(options?: {
    account_id?: string
    startDate?: string
    endDate?: string
    limit?: number
    offset?: number
  }): Promise<{ data: ITransferTransaction[]; count: number }> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Get transfer transactions directly (single transaction model)
    let transferQuery = supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*),
        account:accounts!transactions_account_id_fkey(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `, { count: 'exact' })
      .eq('user_id', user.id)
      .eq('transaction_type', 'transfer')
      .neq('transaction_status', 'cancelled') // Exclude cancelled transfers
      .order('transaction_date', { ascending: false })

    if (options?.account_id) {
      transferQuery = transferQuery.or(`account_id.eq.${options.account_id},to_account_id.eq.${options.account_id}`)
    }

    if (options?.startDate) {
      transferQuery = transferQuery.gte('transaction_date', options.startDate)
    }

    if (options?.endDate) {
      transferQuery = transferQuery.lte('transaction_date', options.endDate)
    }

    if (options?.limit) {
      transferQuery = transferQuery.limit(options.limit)
    }

    if (options?.offset) {
      transferQuery = transferQuery.range(options.offset, options.offset + (options.limit || 20) - 1)
    }

    const { data: transactions, error: transferError, count } = await transferQuery

    if (transferError) {
      throw new Error(`Failed to fetch transfers: ${transferError.message}`)
    }

    if (!transactions || transactions.length === 0) {
      return { data: [], count: count || 0 }
    }

    // Convert transactions to transfer objects
    const transfers: ITransferTransaction[] = transactions.map(transaction => ({
      id: transaction.id,
      amount: transaction.amount,
      description: transaction.description,
      from_account_id: transaction.account_id!,
      to_account_id: transaction.to_account_id!,
      transfer_id: transaction.id, // Use transaction ID as transfer ID
      transaction_date: transaction.transaction_date,
      fees: transaction.fees,
      user_id: user.id,
      from_account: transaction.account as any,
      to_account: transaction.to_account as any,
    }))

    return { data: transfers, count: count || 0 }
  }

  /**
   * Get a specific transfer by transfer ID (transaction ID)
   */
  static async getTransfer(transferId: string): Promise<ITransferTransaction> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data: transaction, error } = await supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*),
        account:accounts!transactions_account_id_fkey(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .eq('id', transferId)
      .eq('user_id', user.id)
      .eq('transaction_type', 'transfer')
      .neq('transaction_status', 'cancelled') // Exclude cancelled transfers
      .single()

    if (error) {
      throw new Error(`Failed to fetch transfer: ${error.message}`)
    }

    if (!transaction) {
      throw new Error('Transfer not found')
    }
    
    return {
      id: transaction.id,
      amount: transaction.amount,
      description: transaction.description,
      from_account_id: transaction.account_id!,
      to_account_id: transaction.to_account_id!,
      transfer_id: transaction.id, // Use transaction ID as transfer ID
      transaction_date: transaction.transaction_date,
      fees: transaction.fees,
      user_id: user.id,
      from_account: transaction.account as IAccount,
      to_account: transaction.to_account as IAccount,
    }
  }

  /**
   * Update an existing transfer
   */
  static async updateTransfer(transferId: string, updateData: Partial<ITransferForm>): Promise<ITransferTransaction> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Get the current transfer details
    const currentTransfer = await this.getTransfer(transferId)
    
    // Prepare update object for transaction
    const updateTransaction: any = {}
    
    if (updateData.amount !== undefined) updateTransaction.amount = updateData.amount
    if (updateData.description !== undefined) updateTransaction.description = updateData.description
    if (updateData.transaction_date !== undefined) updateTransaction.transaction_date = updateData.transaction_date
    if (updateData.fees !== undefined) updateTransaction.fees = updateData.fees
    if (updateData.is_internal !== undefined) updateTransaction.is_internal = updateData.is_internal
    if (updateData.category_id !== undefined) updateTransaction.category_id = updateData.category_id
    
    // Add updated timestamp
    updateTransaction.updated_at = new Date().toISOString()

    // Update the transaction
    const { data: updatedTransaction, error: updateError } = await supabase
      .from('transactions')
      .update(updateTransaction)
      .eq('id', transferId)
      .eq('user_id', user.id)
      .eq('transaction_type', 'transfer')
      .select(`
        *,
        category:categories(*),
        account:accounts!transactions_account_id_fkey(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .single()

    if (updateError) {
      throw new Error(`Failed to update transfer: ${updateError.message}`)
    }

    // If amount or fees changed, need to update account balances
    if (updateData.amount !== undefined || updateData.fees !== undefined) {
      const oldAmount = currentTransfer.amount
      const oldFees = currentTransfer.fees || 0
      const newAmount = updateData.amount ?? oldAmount
      const newFees = updateData.fees ?? oldFees

      const oldTotalDeduction = oldAmount + oldFees
      const newTotalDeduction = newAmount + newFees
      const balanceChange = oldTotalDeduction - newTotalDeduction

      // Update account balances
      try {
        // Update source account (if balance change needed)
        if (balanceChange !== 0) {
          const { error: fromBalanceError } = await supabase
            .from('accounts')
            .update({ 
              current_balance: (currentTransfer.from_account?.current_balance || 0) + balanceChange,
              updated_at: new Date().toISOString()
            })
            .eq('id', currentTransfer.from_account_id)
            .eq('user_id', user.id)

          if (fromBalanceError) {
            throw new Error(`Failed to update source account balance: ${fromBalanceError.message}`)
          }

          // Update destination account
          const amountChange = oldAmount - newAmount
          const { error: toBalanceError } = await supabase
            .from('accounts')
            .update({ 
              current_balance: (currentTransfer.to_account?.current_balance || 0) + amountChange,
              updated_at: new Date().toISOString()
            })
            .eq('id', currentTransfer.to_account_id)
            .eq('user_id', user.id)

          if (toBalanceError) {
            // Revert source account balance change
            await supabase
              .from('accounts')
              .update({ 
                current_balance: currentTransfer.from_account?.current_balance || 0,
                updated_at: new Date().toISOString()
              })
              .eq('id', currentTransfer.from_account_id)
              .eq('user_id', user.id)
            
            throw new Error(`Failed to update destination account balance: ${toBalanceError.message}`)
          }
        }
      } catch (balanceError) {
        // Rollback transaction update - we need to get the original transaction data
        const { data: originalTxn } = await supabase
          .from('transactions')
          .select('*')
          .eq('id', transferId)
          .single()
        
        if (originalTxn) {
          await supabase
            .from('transactions')
            .update({
              amount: currentTransfer.amount,
              description: currentTransfer.description,
              transaction_date: currentTransfer.transaction_date,
              fees: currentTransfer.fees,
              is_internal: originalTxn.is_internal, // Use original is_internal value
              category_id: originalTxn.category_id, // Use original category_id
              updated_at: new Date().toISOString()
            })
            .eq('id', transferId)
            .eq('user_id', user.id)
        }
        
        throw balanceError
      }
    }

    // Return updated transfer object
    return {
      id: updatedTransaction.id,
      amount: updatedTransaction.amount,
      description: updatedTransaction.description,
      from_account_id: updatedTransaction.account_id!,
      to_account_id: updatedTransaction.to_account_id!,
      transfer_id: updatedTransaction.id,
      transaction_date: updatedTransaction.transaction_date,
      fees: updatedTransaction.fees,
      user_id: user.id,
      from_account: updatedTransaction.account as IAccount,
      to_account: updatedTransaction.to_account as IAccount,
    }
  }

  /**
   * Cancel a transfer (mark transaction as cancelled and revert balances)
   */
  static async cancelTransfer(transferId: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // First get the transfer details to revert balances
    const transfer = await this.getTransfer(transferId)
    
    // Get current account balances
    const { data: fromAccount, error: fromError } = await supabase
      .from('accounts')
      .select('id, current_balance')
      .eq('id', transfer.from_account_id)
      .eq('user_id', user.id)
      .single()

    const { data: toAccount, error: toError } = await supabase
      .from('accounts')
      .select('id, current_balance')
      .eq('id', transfer.to_account_id)
      .eq('user_id', user.id)
      .single()

    if (fromError || !fromAccount) {
      throw new Error('Source account not found')
    }
    
    if (toError || !toAccount) {
      throw new Error('Destination account not found')
    }

    // Mark transaction as cancelled
    const { error: cancelError } = await supabase
      .from('transactions')
      .update({ 
        transaction_status: 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('id', transferId)
      .eq('user_id', user.id)
      .eq('transaction_type', 'transfer')

    if (cancelError) {
      throw new Error(`Failed to cancel transfer: ${cancelError.message}`)
    }

    // Revert balance changes
    try {
      // Restore source account balance (add back amount and fees)
      const totalRestoration = transfer.amount + (transfer.fees || 0)
      const { error: fromBalanceError } = await supabase
        .from('accounts')
        .update({ 
          current_balance: (fromAccount.current_balance || 0) + totalRestoration,
          updated_at: new Date().toISOString()
        })
        .eq('id', transfer.from_account_id)
        .eq('user_id', user.id)

      if (fromBalanceError) {
        throw new Error(`Failed to restore source account balance: ${fromBalanceError.message}`)
      }

      // Restore destination account balance (subtract amount)
      const { error: toBalanceError } = await supabase
        .from('accounts')
        .update({ 
          current_balance: (toAccount.current_balance || 0) - transfer.amount,
          updated_at: new Date().toISOString()
        })
        .eq('id', transfer.to_account_id)
        .eq('user_id', user.id)

      if (toBalanceError) {
        // Revert the source account balance change
        await supabase
          .from('accounts')
          .update({ 
            current_balance: fromAccount.current_balance,
            updated_at: new Date().toISOString()
          })
          .eq('id', transfer.from_account_id)
          .eq('user_id', user.id)
        
        throw new Error(`Failed to restore destination account balance: ${toBalanceError.message}`)
      }

    } catch (balanceError) {
      // If balance restoration fails, we should still keep the transaction cancelled
      // but log the error for manual investigation
      console.error('Balance restoration failed for cancelled transfer:', balanceError)
      throw new Error('Transfer cancelled but balance restoration failed. Please check account balances manually.')
    }
  }
}
