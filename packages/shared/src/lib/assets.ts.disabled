import { supabase } from './supabase'
import type { 
  IAssetClassInfo, 
  IAsset, 
  IHolding,
  IAssetClassForm,
  IAssetForm,
  DEFAULT_ASSET_CLASSES
} from '../types/assets'
import type { TablesInsert, TablesUpdate } from '../database.types'

export class AssetClassService {
  /**
   * Get all asset classes
   */
  static async getAssetClasses(): Promise<IAssetClassInfo[]> {
    const { data, error } = await supabase
      .from('asset_classes')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) {
      throw new Error(`Failed to fetch asset classes: ${error.message}`)
    }

    return data as IAssetClassInfo[]
  }

  /**
   * Get a specific asset class by ID
   */
  static async getAssetClass(id: string): Promise<IAssetClassInfo> {
    const { data, error } = await supabase
      .from('asset_classes')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      throw new Error(`Failed to fetch asset class: ${error.message}`)
    }

    return data as IAssetClassInfo
  }

  /**
   * Create a new asset class
   */
  static async createAssetClass(assetClassData: IAssetClassForm): Promise<IAssetClassInfo> {
    const insertData: TablesInsert<'asset_classes'> = {
      ...assetClassData,
      is_active: true
    }

    const { data, error } = await supabase
      .from('asset_classes')
      .insert(insertData)
      .select('*')
      .single()

    if (error) {
      throw new Error(`Failed to create asset class: ${error.message}`)
    }

    return data as IAssetClassInfo
  }

  /**
   * Update an existing asset class
   */
  static async updateAssetClass(id: string, updates: Partial<IAssetClassForm>): Promise<IAssetClassInfo> {
    const updateData: TablesUpdate<'asset_classes'> = {
      ...updates,
      updated_at: new Date().toISOString()
    }

    const { data, error } = await supabase
      .from('asset_classes')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single()

    if (error) {
      throw new Error(`Failed to update asset class: ${error.message}`)
    }

    return data as IAssetClassInfo
  }

  /**
   * Initialize default asset classes
   */
  static async initializeDefaultAssetClasses(): Promise<void> {
    const { DEFAULT_ASSET_CLASSES } = await import('../types/assets')
    
    for (const assetClass of DEFAULT_ASSET_CLASSES) {
      try {
        await this.createAssetClass(assetClass)
      } catch (error) {
        // Skip if already exists
        console.log(`Asset class ${assetClass.name} may already exist`)
      }
    }
  }
}

export class AssetService {
  /**
   * Get all assets
   */
  static async getAssets(options?: {
    asset_class_id?: string
    is_active?: boolean
  }): Promise<IAsset[]> {
    let query = supabase
      .from('assets')
      .select(`
        *,
        asset_class:asset_classes(*)
      `)
      .order('symbol')

    if (options?.asset_class_id) {
      query = query.eq('asset_class_id', options.asset_class_id)
    }

    if (options?.is_active !== undefined) {
      query = query.eq('is_active', options.is_active)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch assets: ${error.message}`)
    }

    return data as IAsset[]
  }

  /**
   * Get a specific asset by ID
   */
  static async getAsset(id: string): Promise<IAsset> {
    const { data, error } = await supabase
      .from('assets')
      .select(`
        *,
        asset_class:asset_classes(*)
      `)
      .eq('id', id)
      .single()

    if (error) {
      throw new Error(`Failed to fetch asset: ${error.message}`)
    }

    return data as IAsset
  }

  /**
   * Get asset by symbol
   */
  static async getAssetBySymbol(symbol: string): Promise<IAsset | null> {
    const { data, error } = await supabase
      .from('assets')
      .select(`
        *,
        asset_class:asset_classes(*)
      `)
      .eq('symbol', symbol.toUpperCase())
      .eq('is_active', true)
      .single()

    if (error && error.code !== 'PGRST116') { // Not found error
      throw new Error(`Failed to fetch asset: ${error.message}`)
    }

    return data as IAsset | null
  }

  /**
   * Create a new asset
   */
  static async createAsset(assetData: IAssetForm): Promise<IAsset> {
    const insertData: TablesInsert<'assets'> = {
      ...assetData,
      symbol: assetData.symbol.toUpperCase(),
      is_active: true
    }

    const { data, error } = await supabase
      .from('assets')
      .insert(insertData)
      .select(`
        *,
        asset_class:asset_classes(*)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to create asset: ${error.message}`)
    }

    return data as IAsset
  }

  /**
   * Update an existing asset
   */
  static async updateAsset(id: string, updates: Partial<IAssetForm>): Promise<IAsset> {
    const updateData: TablesUpdate<'assets'> = {
      ...updates,
      updated_at: new Date().toISOString()
    }

    if (updates.symbol) {
      updateData.symbol = updates.symbol.toUpperCase()
    }

    const { data, error } = await supabase
      .from('assets')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        asset_class:asset_classes(*)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to update asset: ${error.message}`)
    }

    return data as IAsset
  }

  /**
   * Search assets by symbol or name
   */
  static async searchAssets(query: string): Promise<IAsset[]> {
    const { data, error } = await supabase
      .from('assets')
      .select(`
        *,
        asset_class:asset_classes(*)
      `)
      .or(`symbol.ilike.%${query.toUpperCase()}%,name.ilike.%${query}%`)
      .eq('is_active', true)
      .limit(10)

    if (error) {
      throw new Error(`Failed to search assets: ${error.message}`)
    }

    return data as IAsset[]
  }
}

export class HoldingService {
  /**
   * Get all holdings for the current user
   */
  static async getHoldings(options?: {
    account_id?: string
    asset_class_id?: string
  }): Promise<IHolding[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    let query = supabase
      .from('holdings')
      .select(`
        *,
        asset:assets(*,asset_class:asset_classes(*)),
        account:accounts(*)
      `)
      .eq('user_id', user.id)
      .gt('quantity', 0)
      .order('created_at', { ascending: false })

    if (options?.account_id) {
      query = query.eq('account_id', options.account_id)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch holdings: ${error.message}`)
    }

    // Filter by asset class if specified
    let holdings = data as IHolding[]
    if (options?.asset_class_id) {
      holdings = holdings.filter(h => h.asset?.asset_class_id === options.asset_class_id)
    }

    return holdings
  }

  /**
   * Get holding for a specific asset in an account
   */
  static async getHolding(accountId: string, assetId: string): Promise<IHolding | null> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('holdings')
      .select(`
        *,
        asset:assets(*,asset_class:asset_classes(*)),
        account:accounts(*)
      `)
      .eq('user_id', user.id)
      .eq('account_id', accountId)
      .eq('asset_id', assetId)
      .single()

    if (error && error.code !== 'PGRST116') { // Not found error
      throw new Error(`Failed to fetch holding: ${error.message}`)
    }

    return data as IHolding | null
  }

  /**
   * Update or create holding after a transaction
   */
  static async updateHolding(
    accountId: string,
    assetId: string,
    transactionType: 'buy' | 'sell',
    quantity: number,
    pricePerUnit: number
  ): Promise<IHolding> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const existingHolding = await this.getHolding(accountId, assetId)

    if (transactionType === 'buy') {
      if (existingHolding) {
        // Update existing holding
        const newQuantity = existingHolding.quantity + quantity
        const newTotalInvested = existingHolding.total_invested + (quantity * pricePerUnit)
        const newAverageCost = newTotalInvested / newQuantity

        const { data, error } = await supabase
          .from('holdings')
          .update({
            quantity: newQuantity,
            average_cost: newAverageCost,
            total_invested: newTotalInvested,
            last_updated: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', existingHolding.id)
          .select(`
            *,
            asset:assets(*,asset_class:asset_classes(*)),
            account:accounts(*)
          `)
          .single()

        if (error) {
          throw new Error(`Failed to update holding: ${error.message}`)
        }

        return data as IHolding
      } else {
        // Create new holding
        const { data, error } = await supabase
          .from('holdings')
          .insert({
            user_id: user.id,
            account_id: accountId,
            asset_id: assetId,
            quantity,
            average_cost: pricePerUnit,
            total_invested: quantity * pricePerUnit,
            last_updated: new Date().toISOString()
          })
          .select(`
            *,
            asset:assets(*,asset_class:asset_classes(*)),
            account:accounts(*)
          `)
          .single()

        if (error) {
          throw new Error(`Failed to create holding: ${error.message}`)
        }

        return data as IHolding
      }
    } else {
      // Sell transaction
      if (!existingHolding) {
        throw new Error('Cannot sell asset that is not held')
      }

      if (existingHolding.quantity < quantity) {
        throw new Error('Cannot sell more than current holding')
      }

      const newQuantity = existingHolding.quantity - quantity
      const soldValue = quantity * existingHolding.average_cost
      const newTotalInvested = existingHolding.total_invested - soldValue

      if (newQuantity === 0) {
        // Delete holding if quantity becomes zero
        const { error } = await supabase
          .from('holdings')
          .delete()
          .eq('id', existingHolding.id)

        if (error) {
          throw new Error(`Failed to delete holding: ${error.message}`)
        }

        return { ...existingHolding, quantity: 0, total_invested: 0 }
      } else {
        // Update holding
        const { data, error } = await supabase
          .from('holdings')
          .update({
            quantity: newQuantity,
            total_invested: newTotalInvested,
            last_updated: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', existingHolding.id)
          .select(`
            *,
            asset:assets(*,asset_class:asset_classes(*)),
            account:accounts(*)
          `)
          .single()

        if (error) {
          throw new Error(`Failed to update holding: ${error.message}`)
        }

        return data as IHolding
      }
    }
  }
}
