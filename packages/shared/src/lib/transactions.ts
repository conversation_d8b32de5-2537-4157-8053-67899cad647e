import { supabase } from './supabase'
import type { ITransaction, ICategory, TransactionType } from '../types'
import type { TransactionData, ComprehensiveTransactionFormData } from '../schemas/transaction'

export class TransactionService {
  static async createComprehensiveTransaction(data: ComprehensiveTransactionFormData): Promise<ITransaction | ITransaction[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Handle different transaction types
    if (data.transaction_type === 'income' || data.transaction_type === 'expense') {
      // Validate account_id is provided
      if (!data.account_id) {
        throw new Error('Account ID is required for income and expense transactions')
      }

      // Check if account exists and has sufficient balance for expense transactions
      const { data: account, error: accountError } = await supabase
        .from('accounts')
        .select('id, current_balance, account_type, name')
        .eq('id', data.account_id)
        .single()

      if (accountError) {
        console.error('Account fetch error:', accountError)
        throw new Error(`Failed to fetch account details: ${accountError.message}`)
      }

      if (!account) {
        throw new Error('Account not found')
      }

      // Note: Removed balance validation to allow negative balances for user reconciliation

      // Regular transaction
      const transactionData = {
        amount: data.amount,
        description: data.description || null,
        category_id: data.category_id || null,
        account_id: data.account_id,
        transaction_type: data.transaction_type,
        transaction_date: data.transaction_date instanceof Date 
          ? data.transaction_date.toISOString().split('T')[0]
          : data.transaction_date,
        fees: data.fees || 0,
        user_id: user.id,
      }

      const { data: transaction, error } = await supabase
        .from('transactions')
        .insert(transactionData)
        .select('*')
        .single()

      if (error) {
        throw new Error(`Failed to create transaction: ${error.message}`)
      }

      // Update account balance after successful transaction creation
      try {
        const balanceChange = data.transaction_type === 'income' ? data.amount : -data.amount
        const totalChange = balanceChange - (data.fees || 0) // Fees always reduce balance
        
        const { error: balanceError } = await supabase
          .from('accounts')
          .update({ 
            current_balance: (account?.current_balance || 0) + totalChange,
            updated_at: new Date().toISOString()
          })
          .eq('id', data.account_id)
          .eq('user_id', user.id)

        if (balanceError) {
          // Rollback the transaction
          await supabase.from('transactions').delete().eq('id', transaction.id)
          throw new Error(`Failed to update account balance: ${balanceError.message}`)
        }
      } catch (balanceError) {
        // If balance update fails, rollback the transaction
        await supabase.from('transactions').delete().eq('id', transaction.id)
        throw balanceError
      }

      // Fetch related data separately to avoid complex joins that might trigger functions
      const enrichedTransaction: any = { ...transaction }

      // Fetch category if exists
      if (transaction.category_id) {
        const { data: category } = await supabase
          .from('categories')
          .select('*')
          .eq('id', transaction.category_id)
          .single()
        
        if (category) {
          enrichedTransaction.category = category
        }
      }

      // Fetch account if exists
      if (transaction.account_id) {
        const { data: account } = await supabase
          .from('accounts')
          .select('*')
          .eq('id', transaction.account_id)
          .single()
        
        if (account) {
          enrichedTransaction.account = account
        }
      }

      return enrichedTransaction as ITransaction
    } else if (data.transaction_type === 'transfer') {
      // Create transfer transactions (handled by transfer service if available)
      throw new Error('Transfer transactions should use TransferService.createTransfer')
    } else if (data.transaction_type === 'investment_buy' || data.transaction_type === 'investment_sell' || data.transaction_type === 'dividend') {
      // Investment transactions (handled by investment service if available)
      throw new Error('Investment transactions should use InvestmentService')
    }

    throw new Error('Unsupported transaction type')
  }

  static async createTransaction(data: TransactionData & { account_id?: string }): Promise<ITransaction> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    // Get account information if account_id is provided
    let account = null;
    if (data.account_id) {
      const { data: accountData, error: accountError } = await supabase
        .from('accounts')
        .select('*')
        .eq('id', data.account_id)
        .eq('user_id', user.id)
        .single()

      if (accountError) {
        throw new Error(`Failed to fetch account: ${accountError.message}`)
      }
      account = accountData;
    }

    const transactionData = {
      amount: data.amount,
      description: data.description || null,
      category_id: data.category_id,
      account_id: data.account_id || null,
      transaction_type: data.transaction_type,
      transaction_date: data.transaction_date.toISOString().split('T')[0],
      user_id: user.id,
    }

    const { data: transaction, error } = await supabase
      .from('transactions')
      .insert(transactionData)
      .select(`
        *,
        category:categories(*),
        account:accounts!transactions_account_id_fkey(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to create transaction: ${error.message}`)
    }

    // Update account balance if account is provided
    if (account) {
      try {
        const balanceChange = data.transaction_type === 'income' ? data.amount : -data.amount

        const { error: balanceError } = await supabase
          .from('accounts')
          .update({
            current_balance: (account.current_balance || 0) + balanceChange,
            updated_at: new Date().toISOString()
          })
          .eq('id', data.account_id!)
          .eq('user_id', user.id)

        if (balanceError) {
          // Rollback the transaction
          await supabase.from('transactions').delete().eq('id', transaction.id)
          throw new Error(`Failed to update account balance: ${balanceError.message}`)
        }
      } catch (balanceError) {
        // If balance update fails, rollback the transaction
        await supabase.from('transactions').delete().eq('id', transaction.id)
        throw balanceError
      }
    }

    return transaction as ITransaction
  }

  static async getTransactions(options?: {
    limit?: number
    offset?: number
    categoryId?: string
    accountId?: string
    startDate?: string
    endDate?: string
    searchQuery?: string
    transactionType?: 'income' | 'expense'
    includeTransfers?: boolean
    includeInvestments?: boolean
  }): Promise<{ data: ITransaction[]; count: number }> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    // Include category and account joins to support search and display
    let query = supabase
      .from('transactions')
      .select(`
        *,
        category:categories(*),
        account:accounts!transactions_account_id_fkey(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `, { count: 'exact' })
      .eq('user_id', user.id)
      .neq('transaction_status', 'cancelled') // Exclude cancelled transactions
      .order('transaction_date', { ascending: false })
      .order('created_at', { ascending: false })

    // Apply filters
    if (options?.categoryId) {
      query = query.eq('category_id', options.categoryId)
    }

    if (options?.accountId) {
      // Filter by account_id OR to_account_id to include both source and destination transactions
      query = query.or(`account_id.eq.${options.accountId},to_account_id.eq.${options.accountId}`)
    }

    if (options?.startDate) {
      query = query.gte('transaction_date', options.startDate)
    }

    if (options?.endDate) {
      query = query.lte('transaction_date', options.endDate)
    }

    if (options?.transactionType) {
      query = query.eq('transaction_type', options.transactionType)
    }

    // Filter transaction types based on options
    const allowedTypes = ['income', 'expense']
    if (options?.includeTransfers) {
      allowedTypes.push('transfer')
    }
    if (options?.includeInvestments) {
      allowedTypes.push('investment_buy', 'investment_sell', 'dividend')
    }
    query = query.in('transaction_type', allowedTypes)

    if (options?.searchQuery) {
      // Search in description only to avoid complex joins
      query = query.ilike('description', `%${options.searchQuery}%`)
    }

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit)
    }

    if (options?.offset) {
      query = query.range(options.offset, (options.offset + (options.limit || 10)) - 1)
    }

    const { data, error, count } = await query

    if (error) {
      throw new Error(`Failed to fetch transactions: ${error.message}`)
    }

    return {
      data: data as ITransaction[],
      count: count || 0
    }
  }

  static async updateTransaction(id: string, data: Partial<TransactionData>): Promise<ITransaction> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    // First, get the original transaction to calculate balance changes
    const { data: originalTransaction, error: fetchError } = await supabase
      .from('transactions')
      .select(`
        *,
        account:accounts!transactions_account_id_fkey(*)
      `)
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (fetchError) {
      throw new Error(`Failed to fetch original transaction: ${fetchError.message}`)
    }

    if (!originalTransaction) {
      throw new Error('Transaction not found')
    }

    const updateData: any = {}

    if (data.amount !== undefined) updateData.amount = data.amount
    if (data.description !== undefined) updateData.description = data.description
    if (data.category_id !== undefined) updateData.category_id = data.category_id
    if (data.transaction_type !== undefined) updateData.transaction_type = data.transaction_type
    if (data.transaction_date !== undefined) {
      updateData.transaction_date = data.transaction_date.toISOString().split('T')[0]
    }

    updateData.updated_at = new Date().toISOString()

    // Calculate balance changes if amount or transaction type changed
    let balanceChange = 0
    if (originalTransaction.account_id && (data.amount !== undefined || data.transaction_type !== undefined)) {
      const originalAmount = originalTransaction.amount
      const newAmount = data.amount ?? originalAmount
      const originalType = originalTransaction.transaction_type
      const newType = data.transaction_type ?? originalType
      const originalFees = originalTransaction.fees || 0
      const newFees = 0 // Fees are not editable in basic transactions

      // Calculate the change needed to account balance
      // First, reverse the original transaction's effect
      let reverseChange = 0
      if (originalType === 'income') {
        reverseChange = -originalAmount + originalFees // Remove income and add back fees
      } else if (originalType === 'expense') {
        reverseChange = originalAmount + originalFees // Add back expense and fees
      }

      // Then apply the new transaction's effect
      let applyChange = 0
      if (newType === 'income') {
        applyChange = newAmount - newFees // Add income and subtract fees
      } else if (newType === 'expense') {
        applyChange = -newAmount - newFees // Subtract expense and fees
      }

      balanceChange = reverseChange + applyChange

      // Note: Removed balance validation to allow negative balances for user reconciliation
    }

    const { data: transaction, error } = await supabase
      .from('transactions')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select(`
        *,
        category:categories(*),
        account:accounts!transactions_account_id_fkey(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .single()

    if (error) {
      throw new Error(`Failed to update transaction: ${error.message}`)
    }

    // Update account balance if there was a change
    if (originalTransaction.account_id && originalTransaction.account && balanceChange !== 0) {
      const account = originalTransaction.account as any
      const { error: balanceError } = await supabase
        .from('accounts')
        .update({ 
          current_balance: (account.current_balance || 0) + balanceChange,
          updated_at: new Date().toISOString()
        })
        .eq('id', originalTransaction.account_id)
        .eq('user_id', user.id)

      if (balanceError) {
        // Rollback the transaction update
        await supabase
          .from('transactions')
          .update({
            amount: originalTransaction.amount,
            description: originalTransaction.description,
            category_id: originalTransaction.category_id,
            transaction_type: originalTransaction.transaction_type,
            transaction_date: originalTransaction.transaction_date,
            updated_at: originalTransaction.updated_at
          })
          .eq('id', id)
          .eq('user_id', user.id)
        
        throw new Error(`Failed to update account balance: ${balanceError.message}`)
      }
    }

    return transaction as ITransaction
  }

  static async deleteTransaction(id: string): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // First, get the transaction details to check if deletion would violate balance constraints
    const { data: transaction, error: fetchError } = await supabase
      .from('transactions')
      .select(`
        *,
        account:accounts!transactions_account_id_fkey(*)
      `)
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (fetchError) {
      throw new Error(`Failed to fetch transaction for deletion: ${fetchError.message}`)
    }

    if (!transaction) {
      throw new Error('Transaction not found')
    }

    // Check if deleting this transaction would violate account balance constraints
    if (transaction.account_id && transaction.account) {
      const account = transaction.account as any
      let newBalance = account.current_balance || 0

      // Calculate what the new balance would be after deletion
      if (transaction.transaction_type === 'income') {
        newBalance -= transaction.amount // Remove the income
      } else if (transaction.transaction_type === 'expense') {
        newBalance += transaction.amount // Add back the expense
      }

      // Note: Removed balance validation to allow negative balances for user reconciliation
    }

    const { error } = await supabase
      .from('transactions')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      throw new Error(`Failed to delete transaction: ${error.message}`)
    }

    // Update account balance after successful transaction deletion
    if (transaction.account_id && transaction.account) {
      const account = transaction.account as any
      let balanceChange = 0
      
      // Calculate balance change (opposite of creation)
      if (transaction.transaction_type === 'income') {
        balanceChange = -transaction.amount // Remove the income
      } else if (transaction.transaction_type === 'expense') {
        balanceChange = transaction.amount // Add back the expense
      }
      
      // Add back fees that were deducted
      balanceChange += (transaction.fees || 0)
      
      const { error: balanceError } = await supabase
        .from('accounts')
        .update({ 
          current_balance: (account.current_balance || 0) + balanceChange,
          updated_at: new Date().toISOString()
        })
        .eq('id', transaction.account_id)
        .eq('user_id', user.id)

      if (balanceError) {
        console.error('Failed to update account balance after transaction deletion:', balanceError)
        // Note: Transaction is already deleted, so we can't easily rollback
        // This should be handled by a background job or manual correction
        throw new Error(`Transaction deleted but account balance update failed: ${balanceError.message}`)
      }
    }
  }

  static async getCategories(): Promise<ICategory[]> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .or(`user_id.eq.${user.id},and(is_default.eq.true,user_id.is.null)`)
      .order('name')

    if (error) {
      throw new Error(`Failed to fetch categories: ${error.message}`)
    }

    // If no categories exist for the user, create default categories
    if (!data || data.length === 0) {
      await this.createDefaultCategories()
      // Fetch categories again after creating defaults
      const { data: newData, error: newError } = await supabase
        .from('categories')
        .select('*')
        .or(`user_id.eq.${user.id},and(is_default.eq.true,user_id.is.null)`)
        .order('name')

      if (newError) {
        throw new Error(`Failed to fetch categories after creating defaults: ${newError.message}`)
      }

      return newData as ICategory[]
    }

    return data as ICategory[]
  }

  static async createDefaultCategories(): Promise<void> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Check if user already has categories to prevent duplicates
    const { data: existingCategories, error: checkError } = await supabase
      .from('categories')
      .select('id')
      .eq('user_id', user.id)
      .limit(1)

    if (checkError) {
      throw new Error(`Failed to check existing categories: ${checkError.message}`)
    }

    // If user already has categories, don't create defaults
    if (existingCategories && existingCategories.length > 0) {
      return
    }

    const defaultCategories = [
      // Expense categories
      { name: 'Food & Dining', icon: '🍽️', color: '#FF6B6B', type: 'expense' },
      { name: 'Transportation', icon: '🚗', color: '#4ECDC4', type: 'expense' },
      { name: 'Shopping', icon: '🛍️', color: '#45B7D1', type: 'expense' },
      { name: 'Entertainment', icon: '🎬', color: '#96CEB4', type: 'expense' },
      { name: 'Bills & Utilities', icon: '💡', color: '#FFEAA7', type: 'expense' },
      { name: 'Healthcare', icon: '🏥', color: '#DDA0DD', type: 'expense' },
      { name: 'Education', icon: '📚', color: '#98D8C8', type: 'expense' },
      { name: 'Travel', icon: '✈️', color: '#F7DC6F', type: 'expense' },
      { name: 'Groceries', icon: '🛒', color: '#82E0AA', type: 'expense' },
      { name: 'Other', icon: '📦', color: '#BDC3C7', type: 'expense' },
      
      // Income categories
      { name: 'Salary', icon: '💰', color: '#27AE60', type: 'income' },
      { name: 'Freelance', icon: '💻', color: '#2ECC71', type: 'income' },
      { name: 'Business', icon: '🏢', color: '#58D68D', type: 'income' },
      { name: 'Investment', icon: '📈', color: '#85C1E9', type: 'income' },
      { name: 'Gift', icon: '🎁', color: '#F8C471', type: 'income' },
      { name: 'Other Income', icon: '💎', color: '#D5DBDB', type: 'income' },
    ]

    const categoriesToInsert = defaultCategories.map(category => ({
      ...category,
      user_id: user.id,
      is_default: false,
    }))

    const { error } = await supabase
      .from('categories')
      .insert(categoriesToInsert)

    if (error) {
      throw new Error(`Failed to create default categories: ${error.message}`)
    }
  }

  static async createCategory(categoryData: {
    name: string
    icon: string
    color: string
    type: 'income' | 'expense'
  }): Promise<ICategory> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('categories')
      .insert({
        ...categoryData,
        user_id: user.id,
        is_default: false,
      })
      .select('*')
      .single()

    if (error) {
      throw new Error(`Failed to create category: ${error.message}`)
    }

    return data as ICategory
  }

  static async getMonthlySpending(year: number, month: number): Promise<{
    totalIncome: number
    totalExpenses: number
    categoryBreakdown: Array<{ category: ICategory; total: number }>
  }> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const startDate = new Date(year, month - 1, 1).toISOString().split('T')[0]
    const endDate = new Date(year, month, 0).toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', user.id)
      .gte('transaction_date', startDate)
      .lte('transaction_date', endDate)

    if (error) {
      throw new Error(`Failed to fetch monthly spending: ${error.message}`)
    }

    const transactions = data as ITransaction[]
    
    const totalIncome = transactions
      .filter(t => t.transaction_type === 'income')
      .reduce((sum, t) => sum + t.amount, 0)

    const totalExpenses = transactions
      .filter(t => t.transaction_type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0)

    // Group by category
    const categoryMap: Record<string, { category: ICategory; total: number }> = {}
    
    transactions.forEach(transaction => {
      if (transaction.category) {
        const categoryId = transaction.category.id
        if (!categoryMap[categoryId]) {
          categoryMap[categoryId] = {
            category: transaction.category,
            total: 0
          }
        }
        categoryMap[categoryId].total += transaction.amount
      }
    })

    const categoryBreakdown = Object.values(categoryMap)

    return {
      totalIncome,
      totalExpenses,
      categoryBreakdown
    }
  }
}