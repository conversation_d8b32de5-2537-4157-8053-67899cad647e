import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import type { BankStatementData, ParsedTransaction } from './pdf-parser';

export class StatementExporter {
  static generateTextContent(data: BankStatementData): string {
    let content = '';
    
    // Header
    content += `BANK STATEMENT SUMMARY\n`;
    content += `========================\n\n`;
    content += `Account Holder: ${data.accountHolder}\n`;
    content += `Account Number: ${data.accountNumber}\n`;
    content += `Statement Period: ${data.statementPeriod.from} to ${data.statementPeriod.to}\n`;
    content += `Opening Balance: ${data.openingBalance.toFixed(2)}\n`;
    content += `Closing Balance: ${data.closingBalance.toFixed(2)}\n\n`;
    
    // Summary
    content += `TRANSACTION SUMMARY\n`;
    content += `===================\n`;
    content += `Total Credits: ${data.summary.totalCredits.toFixed(2)} (${data.summary.creditCount} transactions)\n`;
    content += `Total Debits: ${data.summary.totalDebits.toFixed(2)} (${data.summary.debitCount} transactions)\n\n`;
    
    // Transactions
    content += `TRANSACTIONS\n`;
    content += `============\n\n`;
    content += `Date\t\tNarration\t\t\t\t\tWithdrawal\tDeposit\t\tBalance\n`;
    content += `${'='.repeat(100)}\n`;
    
    data.transactions.forEach(transaction => {
      const withdrawal = transaction.withdrawalAmount ? transaction.withdrawalAmount.toFixed(2) : '-';
      const deposit = transaction.depositAmount ? transaction.depositAmount.toFixed(2) : '-';
      
      content += `${transaction.date}\t${transaction.narration.substring(0, 40).padEnd(40)}\t${withdrawal.padStart(10)}\t${deposit.padStart(10)}\t${transaction.closingBalance.toFixed(2).padStart(12)}\n`;
    });
    
    return content;
  }

  static generateCSVContent(transactions: ParsedTransaction[]): string {
    // Use same headers as Python script for consistency
    const headers = [
      'Date',
      'Narration',
      'Chq./Ref.No.',
      'Value Dt',
      'Withdrawal Amt.',
      'Deposit Amt.',
      'Closing Balance'
    ];
    
    let csvContent = headers.join(',') + '\n';
    
    transactions.forEach(transaction => {
      const row = [
        transaction.date,
        `"${transaction.narration.replace(/"/g, '""')}"`,
        transaction.refNo,
        transaction.valueDate,
        transaction.withdrawalAmount || '',
        transaction.depositAmount || '',
        transaction.closingBalance
      ];
      
      csvContent += row.join(',') + '\n';
    });
    
    return csvContent;
  }

  static downloadTextFile(data: BankStatementData, filename?: string) {
    const content = this.generateTextContent(data);
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const defaultFilename = `bank_statement_${data.accountNumber}_${Date.now()}.txt`;
    saveAs(blob, filename || defaultFilename);
  }

  static downloadCSVFile(transactions: ParsedTransaction[], filename?: string) {
    const content = this.generateCSVContent(transactions);
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8' });
    const defaultFilename = `transactions_${Date.now()}.csv`;
    saveAs(blob, filename || defaultFilename);
  }

  static downloadRawCSVFile(rawCsvContent: string, filename?: string) {
    const blob = new Blob([rawCsvContent], { type: 'text/csv;charset=utf-8' });
    const defaultFilename = `raw_transactions_${Date.now()}.csv`;
    saveAs(blob, filename || defaultFilename);
  }

  static downloadExcelFile(data: BankStatementData, filename?: string) {
    const workbook = XLSX.utils.book_new();
    
    // Summary sheet
    const summaryData = [
      ['Account Holder', data.accountHolder],
      ['Account Number', data.accountNumber],
      ['Statement From', data.statementPeriod.from],
      ['Statement To', data.statementPeriod.to],
      ['Opening Balance', data.openingBalance],
      ['Closing Balance', data.closingBalance],
      ['', ''],
      ['Total Credits', data.summary.totalCredits],
      ['Total Debits', data.summary.totalDebits],
      ['Credit Count', data.summary.creditCount],
      ['Debit Count', data.summary.debitCount]
    ];
    
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
    
    // Transactions sheet - use same headers as CSV for consistency
    const transactionHeaders = [
      'Date',
      'Narration',
      'Chq./Ref.No.',
      'Value Dt',
      'Withdrawal Amt.',
      'Deposit Amt.',
      'Closing Balance'
    ];
    
    const transactionData = [
      transactionHeaders,
      ...data.transactions.map(transaction => [
        transaction.date,
        transaction.narration,
        transaction.refNo,
        transaction.valueDate,
        transaction.withdrawalAmount || '',
        transaction.depositAmount || '',
        transaction.closingBalance
      ])
    ];
    
    const transactionSheet = XLSX.utils.aoa_to_sheet(transactionData);
    XLSX.utils.book_append_sheet(workbook, transactionSheet, 'Transactions');
    
    // Generate and download
    const defaultFilename = `bank_statement_${data.accountNumber}_${Date.now()}.xlsx`;
    XLSX.writeFile(workbook, filename || defaultFilename);
  }
}
