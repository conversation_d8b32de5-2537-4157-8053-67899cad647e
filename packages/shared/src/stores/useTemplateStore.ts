import { create } from 'zustand'

export interface TemplateData {
  amount: string
  category_id: string
  description: string
  transaction_type: 'income' | 'expense'
  template_name: string
  transaction_date: Date
  account_id: string
  to_account_id: string
  fees: string
  investment_symbol: string
  investment_quantity: string
  investment_price: string
  funding_account_id: string
}

interface TemplateStore {
  templateData: TemplateData | null
  isNavigatingWithTemplate: boolean
  setTemplateData: (data: TemplateData) => void
  clearTemplateData: () => void
  setNavigatingWithTemplate: (isNavigating: boolean) => void
}

export const useTemplateStore = create<TemplateStore>((set) => ({
  templateData: null,
  isNavigatingWithTemplate: false,
  setTemplateData: (data) => set({ templateData: data }),
  clearTemplateData: () => set({ templateData: null, isNavigatingWithTemplate: false }),
  setNavigatingWithTemplate: (isNavigating) => set({ isNavigatingWithTemplate: isNavigating }),
}))