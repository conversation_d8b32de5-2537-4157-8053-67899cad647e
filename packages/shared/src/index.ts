// Shared business logic and utilities
export * from './types';
export * from './utils';
export * from './validators';
export * from './database.types';
export * from './lib/supabase';
export * from './lib/budget';
export * from './lib/analytics';
export * from './lib/recurring-transactions';
// CSV import is now imported directly to avoid bundling xlsx unnecessarily
// export * from './lib/csv-import';
export * from './lib/accounts';
export * from './lib/transactions';
export * from './lib/categories';
export * from './lib/investments';
export * from './lib/transfers';
export * from './lib/pdf-parser';
export * from './lib/statement-export';
// export * from './lib/assets'; // Temporarily disabled - missing tables
// export * from './lib/profit-loss'; // Temporarily disabled - missing tables
// export * from './lib/tax-calculator'; // Temporarily disabled - missing tables
// Platform-specific biometric exports - conditionally export based on environment
// Note: This will need platform-specific bundling configuration
export * from './lib/biometric.web';
export * from './schemas/auth';
export * from './schemas/transaction';
export * from './schemas/budget';
export * from './stores/currencyStore';
export * from './stores/useTemplateStore';

// Platform-specific exports (explicit re-export to avoid naming conflicts)
export { supabase as supabaseMobile } from './lib/supabase.mobile';