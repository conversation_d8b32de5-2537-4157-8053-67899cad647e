{"name": "@repo/shared", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "react-native": "./src/index.mobile.ts", "scripts": {"dev": "tsc --watch", "build": "tsc", "lint": "eslint \"src/**/*.ts*\"", "type-check": "tsc --noEmit"}, "devDependencies": {"@types/node": "^18.17.0", "eslint": "^8.57.0", "typescript": "^5.5.0"}, "dependencies": {"pdf-parse": "^1.1.1", "zod": "^3.23.8", "xlsx": "^0.18.5"}}