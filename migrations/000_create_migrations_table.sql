-- Migration: Create migrations tracking table
-- Version: 000
-- Description: Create table to track applied migrations for deployment consistency
-- Date: 2025-06-25

-- Create migrations table to track applied migrations
CREATE TABLE IF NOT EXISTS migrations (
  id SERIAL PRIMARY KEY,
  version VARCHAR(20) NOT NULL UNIQUE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  checksum VARCHAR(64), -- For integrity checking
  execution_time_ms INTEGER DEFAULT 0
);

-- Create index on version for faster lookups
CREATE INDEX IF NOT EXISTS idx_migrations_version ON migrations(version);

-- Add comment to table for documentation
COMMENT ON TABLE migrations IS 'Tracks database migrations applied to this instance';
COMMENT ON COLUMN migrations.version IS 'Migration version number (e.g., 001, 002, etc.)';
COMMENT ON COLUMN migrations.name IS 'Migration filename without extension';
COMMENT ON COLUMN migrations.description IS 'Human-readable description of what the migration does';
COMMENT ON COLUMN migrations.checksum IS 'SHA256 hash of migration file for integrity verification';
COMMENT ON COLUMN migrations.execution_time_ms IS 'Time taken to execute migration in milliseconds';