-- Migration: Remove negative balance validation
-- Version: 005
-- Description: Remove any database triggers or functions that prevent negative account balances
-- Date: 2025-07-09

-- Drop any existing balance validation triggers
DROP TRIGGER IF EXISTS check_negative_balance_trigger ON transactions;
DROP TRIGGER IF EXISTS validate_account_balance_trigger ON accounts;
DROP TRIGGER IF EXISTS prevent_negative_balance_trigger ON transactions;

-- Drop any existing balance validation functions
DROP FUNCTION IF EXISTS check_account_balance();
DROP FUNCTION IF EXISTS validate_negative_balance();
DROP FUNCTION IF EXISTS prevent_negative_balance();
DROP FUNCTION IF EXISTS check_sufficient_balance();

-- Remove any check constraints on account balance
ALTER TABLE accounts DROP CONSTRAINT IF EXISTS accounts_current_balance_check;
ALTER TABLE accounts DROP CONSTRAINT IF EXISTS accounts_balance_positive_check;
ALTER TABLE accounts DROP CONSTRAINT IF EXISTS check_positive_balance;

-- Add comment to document the change
COMMENT ON COLUMN accounts.current_balance IS 'Account current balance - can be negative for user reconciliation';