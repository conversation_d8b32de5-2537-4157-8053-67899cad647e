-- Migration: Fix missing fields and type mismatches
-- Version: 002
-- Description: Add missing transaction template fields and fix type inconsistencies
-- Date: 2025-06-25

-- Fix accounts table: ensure loan account type is supported
-- (No schema change needed, just ensuring consistency)

-- Fix user_profiles table: ensure currency_preference has a default
ALTER TABLE user_profiles 
ALTER COLUMN currency_preference SET DEFAULT 'USD';

-- Update existing user profiles without currency preference
UPDATE user_profiles 
SET currency_preference = 'USD' 
WHERE currency_preference IS NULL;

-- Add missing indexes for better performance
CREATE INDEX IF NOT EXISTS idx_transaction_templates_user_id ON transaction_templates(user_id);
CREATE INDEX IF NOT EXISTS idx_transaction_templates_type ON transaction_templates(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_user_type ON transactions(user_id, transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_budgets_user_id ON budgets(user_id);
CREATE INDEX IF NOT EXISTS idx_budgets_period ON budgets(period);
CREATE INDEX IF NOT EXISTS idx_categories_user_type ON categories(user_id, type);
CREATE INDEX IF NOT EXISTS idx_investment_holdings_account ON investment_holdings(account_id);
CREATE INDEX IF NOT EXISTS idx_investment_holdings_symbol ON investment_holdings(symbol);

-- Add helpful constraints if they don't exist
DO $$ 
BEGIN
    -- Ensure account types are valid
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'accounts_account_type_check'
    ) THEN
        ALTER TABLE accounts 
        ADD CONSTRAINT accounts_account_type_check 
        CHECK (account_type IN ('bank', 'investment', 'savings', 'credit_card', 'cash', 'loan'));
    END IF;

    -- Ensure transaction types are valid
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'transactions_transaction_type_check'
    ) THEN
        ALTER TABLE transactions 
        ADD CONSTRAINT transactions_transaction_type_check 
        CHECK (transaction_type IN ('income', 'expense', 'transfer', 'investment_buy', 'investment_sell', 'dividend'));
    END IF;

    -- Ensure transaction status values are valid
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'transactions_status_check'
    ) THEN
        ALTER TABLE transactions 
        ADD CONSTRAINT transactions_status_check 
        CHECK (transaction_status IN ('pending', 'completed', 'cancelled', 'failed'));
    END IF;

    -- Ensure budget periods are valid
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'budgets_period_check'
    ) THEN
        ALTER TABLE budgets 
        ADD CONSTRAINT budgets_period_check 
        CHECK (period IN ('weekly', 'monthly', 'yearly'));
    END IF;

    -- Ensure category types are valid
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'categories_type_check'
    ) THEN
        ALTER TABLE categories 
        ADD CONSTRAINT categories_type_check 
        CHECK (type IN ('income', 'expense', 'transfer'));
    END IF;
END $$;

-- Add helpful comments for documentation
COMMENT ON TABLE transaction_templates IS 'Templates for recurring and frequently used transactions';
COMMENT ON COLUMN transaction_templates.frequency IS 'How often the template should auto-create transactions';
COMMENT ON COLUMN transaction_templates.auto_create IS 'Whether to automatically create transactions from this template';
COMMENT ON COLUMN transaction_templates.next_due_date IS 'When the next transaction should be created';
COMMENT ON COLUMN transaction_templates.last_created_date IS 'When a transaction was last created from this template';

COMMENT ON COLUMN accounts.account_type IS 'Type of account: bank, investment, savings, credit_card, cash, loan';
COMMENT ON COLUMN accounts.is_primary IS 'Whether this is the primary account of its type for the user';

COMMENT ON COLUMN transactions.transaction_type IS 'Type of transaction: income, expense, transfer, investment_buy, investment_sell, dividend';
COMMENT ON COLUMN transactions.transaction_status IS 'Status: pending, completed, cancelled, failed';
COMMENT ON COLUMN transactions.transfer_id IS 'Links related transfer transactions together';

COMMENT ON COLUMN budgets.period IS 'Budget period: weekly, monthly, yearly';

COMMENT ON COLUMN categories.type IS 'Category type: income, expense, transfer';
COMMENT ON COLUMN categories.is_system IS 'System categories cannot be deleted by users';
COMMENT ON COLUMN categories.is_default IS 'Default categories shown to all new users';