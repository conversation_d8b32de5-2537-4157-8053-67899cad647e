-- Consolidate double transfer transactions into single transactions
-- Migration: 004_consolidate_transfer_transactions.sql

-- This migration consolidates pairs of transfer transactions (created by the old system)
-- into single transfer transactions (new system)

-- Step 1: Create a temporary table to identify transfer pairs
CREATE TEMPORARY TABLE transfer_pairs AS
SELECT 
    t1.transfer_id,
    t1.id as outgoing_id,
    t2.id as incoming_id,
    t1.amount,
    t1.description,
    t1.category_id,
    t1.account_id as from_account_id,
    t1.to_account_id,
    t1.transaction_date,
    t1.fees,
    t1.user_id,
    t1.is_internal,
    t1.created_at
FROM transactions t1
JOIN transactions t2 ON t1.transfer_id = t2.transfer_id AND t1.id != t2.id
WHERE t1.transaction_type = 'transfer' 
  AND t2.transaction_type = 'transfer'
  AND t1.transfer_id IS NOT NULL
  AND t1.account_id = t2.to_account_id  -- t1 is outgoing, t2 is incoming
  AND t1.to_account_id = t2.account_id
  AND t1.created_at <= t2.created_at;   -- Keep the first (outgoing) transaction

-- Step 2: Update the outgoing transactions to be the consolidated transfer
UPDATE transactions 
SET 
    description = COALESCE(
        CASE 
            WHEN description LIKE 'Transfer to %' THEN description
            WHEN description LIKE 'Transfer from %' THEN 
                REPLACE(description, 'Transfer from', 'Transfer to')
            ELSE description
        END,
        'Transfer'
    ),
    updated_at = NOW()
WHERE id IN (SELECT outgoing_id FROM transfer_pairs);

-- Step 3: Delete the incoming (duplicate) transactions
DELETE FROM transactions 
WHERE id IN (SELECT incoming_id FROM transfer_pairs);

-- Step 4: Clean up any orphaned transfers (transfers without pairs)
-- These might be transfers that were already single transactions or incomplete transfers
UPDATE transactions 
SET description = COALESCE(description, 'Transfer')
WHERE transaction_type = 'transfer' 
  AND description IS NULL;

-- Step 5: Update any remaining transfer_id references to use the transaction ID
-- (This ensures consistency with the new single-transaction model)
UPDATE transactions 
SET transfer_id = id
WHERE transaction_type = 'transfer' 
  AND transfer_id IS NOT NULL;

-- Add a comment documenting the change
COMMENT ON TABLE transactions IS 'Transfers now use single transactions instead of paired transactions. The to_account_id field indicates the destination account for transfers.';