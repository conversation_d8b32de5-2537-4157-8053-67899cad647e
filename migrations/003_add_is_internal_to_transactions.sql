-- Add is_internal field to transactions table for transfer tracking control
-- Migration: 003_add_is_internal_to_transactions.sql

-- Add is_internal column to transactions table
ALTER TABLE transactions 
ADD COLUMN is_internal BOOLEAN DEFAULT NULL;

-- Set existing transfers as internal (maintains current analytics behavior)
UPDATE transactions 
SET is_internal = true 
WHERE transaction_type = 'transfer';

-- Add comment for documentation
COMMENT ON COLUMN transactions.is_internal IS 'Indicates if a transfer is internal (excluded from analytics). Only applies to transfer transactions.';