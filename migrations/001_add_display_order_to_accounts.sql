-- Migration: Add display_order field to accounts table
-- Version: 001
-- Description: Add display_order column to support custom ordering of accounts within each account type
-- Date: 2025-06-25

-- Add display_order column to accounts table
ALTER TABLE accounts ADD COLUMN IF NOT EXISTS display_order INTEGER DEFAULT 0;

-- Update existing accounts to have display_order based on their creation order within each account type
UPDATE accounts 
SET display_order = subquery.row_num
FROM (
  SELECT 
    id,
    row_number() OVER (PARTITION BY user_id, account_type ORDER BY created_at) as row_num
  FROM accounts
  WHERE display_order = 0 OR display_order IS NULL
) AS subquery
WHERE accounts.id = subquery.id
  AND (accounts.display_order = 0 OR accounts.display_order IS NULL);

-- Create index for better performance on ordering queries
CREATE INDEX IF NOT EXISTS idx_accounts_user_type_order 
ON accounts(user_id, account_type, display_order);

-- Add comment to column for documentation
COMMENT ON COLUMN accounts.display_order IS 'Custom ordering position within account type for each user';