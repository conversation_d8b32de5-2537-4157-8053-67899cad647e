#!/usr/bin/env node

/**
 * Database Migration Runner
 * 
 * This script runs database migrations in order, tracking which ones have been applied.
 * It ensures that deployments have consistent database schema across environments.
 * 
 * Usage:
 *   npm run migrate              # Run all pending migrations
 *   npm run migrate --dry-run    # Show what would be migrated without applying
 *   npm run migrate --reset      # Reset migration state (dangerous!)
 */

const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

const crypto = require('crypto');
const { createClient } = require('@supabase/supabase-js');

// Configuration
const MIGRATIONS_DIR = path.join(__dirname, '../migrations');
const isDryRun = process.argv.includes('--dry-run');
const isReset = process.argv.includes('--reset');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL or EXPO_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Calculate SHA256 checksum of a file
 */
function calculateChecksum(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return crypto.createHash('sha256').update(content).digest('hex');
}

/**
 * Get all migration files sorted by version
 */
function getMigrationFiles() {
  if (!fs.existsSync(MIGRATIONS_DIR)) {
    console.error(`❌ Migrations directory not found: ${MIGRATIONS_DIR}`);
    process.exit(1);
  }

  const files = fs.readdirSync(MIGRATIONS_DIR)
    .filter(file => file.endsWith('.sql'))
    .sort();

  return files.map(file => {
    const filePath = path.join(MIGRATIONS_DIR, file);
    const version = file.split('_')[0];
    const name = file.replace('.sql', '');
    
    // Extract description from file if available
    const content = fs.readFileSync(filePath, 'utf8');
    const descriptionMatch = content.match(/-- Description: (.+)/);
    const description = descriptionMatch ? descriptionMatch[1] : 'No description';

    return {
      version,
      name,
      description,
      filePath,
      checksum: calculateChecksum(filePath)
    };
  });
}

/**
 * Get applied migrations from database
 */
async function getAppliedMigrations() {
  try {
    const { data, error } = await supabase
      .from('migrations')
      .select('*')
      .order('version');

    if (error) {
      // If migrations table doesn't exist, return empty array
      if (error.code === '42P01') {
        return [];
      }
      throw error;
    }

    return data || [];
  } catch (error) {
    console.warn(`⚠️  Could not fetch applied migrations: ${error.message}`);
    return [];
  }
}

/**
 * Execute a single migration
 */
async function executeMigration(migration) {
  const startTime = Date.now();
  
  try {
    const sql = fs.readFileSync(migration.filePath, 'utf8');
    
    console.log(`📝 Executing migration ${migration.version}: ${migration.name}`);
    
    if (isDryRun) {
      console.log(`   [DRY RUN] Would execute:\n${sql.substring(0, 200)}...`);
      return;
    }

    // Execute the migration SQL
    const { error: execError } = await supabase.rpc('exec_sql', { sql });
    
    if (execError) {
      throw execError;
    }

    const executionTime = Date.now() - startTime;

    // Record the migration in the migrations table
    const { error: insertError } = await supabase
      .from('migrations')
      .insert({
        version: migration.version,
        name: migration.name,
        description: migration.description,
        checksum: migration.checksum,
        execution_time_ms: executionTime
      });

    if (insertError) {
      throw insertError;
    }

    console.log(`✅ Migration ${migration.version} completed in ${executionTime}ms`);
  } catch (error) {
    console.error(`❌ Migration ${migration.version} failed: ${error.message}`);
    throw error;
  }
}

/**
 * Reset migration state (dangerous!)
 */
async function resetMigrations() {
  if (!isReset) return;
  
  console.log('🔥 RESETTING MIGRATION STATE...');
  console.log('⚠️  This will delete all migration records (but not the actual schema changes)');
  
  try {
    const { error } = await supabase
      .from('migrations')
      .delete()
      .neq('id', 0); // Delete all records

    if (error) throw error;
    
    console.log('✅ Migration state reset');
  } catch (error) {
    console.error(`❌ Failed to reset migrations: ${error.message}`);
    process.exit(1);
  }
}

/**
 * Main migration runner
 */
async function runMigrations() {
  console.log('🚀 Database Migration Runner');
  console.log('============================');

  if (isDryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be applied');
  }

  if (isReset) {
    await resetMigrations();
    return;
  }

  try {
    // Get all available migrations
    const allMigrations = getMigrationFiles();
    console.log(`📁 Found ${allMigrations.length} migration files`);

    // Get applied migrations
    const appliedMigrations = await getAppliedMigrations();
    console.log(`📊 ${appliedMigrations.length} migrations already applied`);

    // Find pending migrations
    const appliedVersions = new Set(appliedMigrations.map(m => m.version));
    const pendingMigrations = allMigrations.filter(m => !appliedVersions.has(m.version));

    if (pendingMigrations.length === 0) {
      console.log('✅ No pending migrations - database is up to date');
      return;
    }

    console.log(`📋 ${pendingMigrations.length} pending migrations:`);
    pendingMigrations.forEach(m => {
      console.log(`   ${m.version}: ${m.description}`);
    });

    if (isDryRun) {
      console.log('🔍 DRY RUN: Would apply the above migrations');
      return;
    }

    // Execute pending migrations
    console.log('\n🔄 Applying migrations...');
    for (const migration of pendingMigrations) {
      await executeMigration(migration);
    }

    console.log('\n🎉 All migrations completed successfully!');
  } catch (error) {
    console.error(`\n💥 Migration failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the migration
runMigrations().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});