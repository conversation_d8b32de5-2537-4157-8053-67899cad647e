#!/usr/bin/env node

/**
 * Simple Database Migration Runner for Supabase
 * 
 * This script runs database migrations using Supabase client.
 * It's designed to work with Supabase's security model and RLS policies.
 * 
 * Usage:
 *   node scripts/migrate-simple.js              # Run all pending migrations
 *   node scripts/migrate-simple.js --dry-run    # Show what would be migrated
 */

const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
require('dotenv').config({ path: path.join(__dirname, '../.env.local') });

const crypto = require('crypto');
const { createClient } = require('@supabase/supabase-js');

// Configuration
const MIGRATIONS_DIR = path.join(__dirname, '../migrations');
const isDryRun = process.argv.includes('--dry-run');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL or EXPO_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  console.log('\n💡 For local development, add these to your .env.local file:');
  console.log('   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

/**
 * Calculate SHA256 checksum of a file
 */
function calculateChecksum(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return crypto.createHash('sha256').update(content).digest('hex');
}

/**
 * Get all migration files sorted by version
 */
function getMigrationFiles() {
  if (!fs.existsSync(MIGRATIONS_DIR)) {
    console.error(`❌ Migrations directory not found: ${MIGRATIONS_DIR}`);
    process.exit(1);
  }

  const files = fs.readdirSync(MIGRATIONS_DIR)
    .filter(file => file.endsWith('.sql'))
    .sort();

  return files.map(file => {
    const filePath = path.join(MIGRATIONS_DIR, file);
    const version = file.split('_')[0];
    const name = file.replace('.sql', '');
    
    // Extract description from file if available
    const content = fs.readFileSync(filePath, 'utf8');
    const descriptionMatch = content.match(/-- Description: (.+)/);
    const description = descriptionMatch ? descriptionMatch[1] : 'No description';

    return {
      version,
      name,
      description,
      filePath,
      checksum: calculateChecksum(filePath),
      sql: content
    };
  });
}

/**
 * Check if migrations table exists and create it if not
 */
async function ensureMigrationsTable() {
  try {
    const { data, error } = await supabase
      .from('migrations')
      .select('id')
      .limit(1);

    if (error && error.code === '42P01') {
      // Table doesn't exist, create it
      console.log('📦 Creating migrations table...');
      
      const createTableSQL = `
        CREATE TABLE migrations (
          id SERIAL PRIMARY KEY,
          version VARCHAR(20) NOT NULL UNIQUE,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          checksum VARCHAR(64),
          execution_time_ms INTEGER DEFAULT 0
        );
        
        CREATE INDEX idx_migrations_version ON migrations(version);
      `;

      // For Supabase, we need to use raw SQL
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'apikey': supabaseServiceKey,
        },
        body: JSON.stringify({ sql: createTableSQL })
      });

      if (!response.ok) {
        throw new Error(`Failed to create migrations table: ${response.statusText}`);
      }

      console.log('✅ Migrations table created');
    }
  } catch (error) {
    console.error(`❌ Error ensuring migrations table: ${error.message}`);
    console.log('\n💡 You may need to manually create the migrations table in Supabase SQL editor:');
    console.log(fs.readFileSync(path.join(MIGRATIONS_DIR, '000_create_migrations_table.sql'), 'utf8'));
    process.exit(1);
  }
}

/**
 * Get applied migrations from database
 */
async function getAppliedMigrations() {
  try {
    const { data, error } = await supabase
      .from('migrations')
      .select('*')
      .order('version');

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error(`❌ Could not fetch applied migrations: ${error.message}`);
    return [];
  }
}

/**
 * Execute a single migration using direct SQL
 */
async function executeMigration(migration) {
  const startTime = Date.now();
  
  try {
    console.log(`📝 Executing migration ${migration.version}: ${migration.name}`);
    
    if (isDryRun) {
      console.log(`   [DRY RUN] Would execute migration ${migration.version}`);
      console.log(`   Description: ${migration.description}`);
      return;
    }

    // Execute migration using Supabase REST API
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey,
      },
      body: JSON.stringify({ sql: migration.sql })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`SQL execution failed: ${response.statusText} - ${errorText}`);
    }

    const executionTime = Date.now() - startTime;

    // Record the migration in the migrations table
    const { error: insertError } = await supabase
      .from('migrations')
      .insert({
        version: migration.version,
        name: migration.name,
        description: migration.description,
        checksum: migration.checksum,
        execution_time_ms: executionTime
      });

    if (insertError) {
      throw insertError;
    }

    console.log(`✅ Migration ${migration.version} completed in ${executionTime}ms`);
  } catch (error) {
    console.error(`❌ Migration ${migration.version} failed: ${error.message}`);
    throw error;
  }
}

/**
 * Main migration runner
 */
async function runMigrations() {
  console.log('🚀 Database Migration Runner (Supabase)');
  console.log('========================================');

  if (isDryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be applied');
  }

  try {
    // Ensure migrations table exists
    await ensureMigrationsTable();

    // Get all available migrations
    const allMigrations = getMigrationFiles();
    console.log(`📁 Found ${allMigrations.length} migration files`);

    // Get applied migrations
    const appliedMigrations = await getAppliedMigrations();
    console.log(`📊 ${appliedMigrations.length} migrations already applied`);

    // Find pending migrations
    const appliedVersions = new Set(appliedMigrations.map(m => m.version));
    const pendingMigrations = allMigrations.filter(m => !appliedVersions.has(m.version));

    if (pendingMigrations.length === 0) {
      console.log('✅ No pending migrations - database is up to date');
      return;
    }

    console.log(`📋 ${pendingMigrations.length} pending migrations:`);
    pendingMigrations.forEach(m => {
      console.log(`   ${m.version}: ${m.description}`);
    });

    if (isDryRun) {
      console.log('\n🔍 DRY RUN: Would apply the above migrations');
      return;
    }

    // Execute pending migrations
    console.log('\n🔄 Applying migrations...');
    for (const migration of pendingMigrations) {
      await executeMigration(migration);
    }

    console.log('\n🎉 All migrations completed successfully!');
  } catch (error) {
    console.error(`\n💥 Migration failed: ${error.message}`);
    console.log('\n💡 You may need to manually apply the migration in Supabase SQL editor.');
    process.exit(1);
  }
}

// Run the migration
runMigrations().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});