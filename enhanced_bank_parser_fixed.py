#!/usr/bin/env python3

# Enhanced ICICI Bank Parser - Fixed Version with Multi-Row Transaction Support
import camelot
import pandas as pd
from collections import defaultdict
import re
import os
import warnings

def detect_bank_type(path, password=None):
    """Detect whether the PDF is an HDFC or ICICI bank statement"""
    try:
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=UserWarning)
            
            tables = camelot.read_pdf(path, password=password, pages='1-2', flavor='stream')
            if not tables:
                return 'HDFC'
                
            for table in tables:
                df_text = table.df.to_string().upper()
                if 'ICICI' in df_text or 'ICICI BANK' in df_text:
                    return 'ICICI'
                elif 'HDFC BANK' in df_text:
                    return 'HDFC'
    except Exception as e:
        print(f"Bank detection warning (non-critical): {e}")
        pass
    
    return 'HDFC'

def extract_df(path, password=None):
    """Main extraction function with bank auto-detection"""
    bank_type = detect_bank_type(path, password)
    print(f"Detected {bank_type} bank statement")
    
    if bank_type == 'ICICI':
        return extract_df_icici(path, password)
    else:
        return extract_df_hdfc(path, password)

def extract_df_hdfc(path, password=None):
    """Original HDFC extraction logic - unchanged"""
    with warnings.catch_warnings():
        warnings.filterwarnings("ignore", category=UserWarning)
        
        lattice_tables = camelot.read_pdf(path, password=password, 
            pages='all', flavor='lattice', line_scale=50)

        regions = defaultdict(list)
        for table in lattice_tables:
            bbox = [table._bbox[i] for i in [0, 3, 2, 1]]
            regions[table.page].append(bbox)

        all_dataframes = []
        for page, boxes in regions.items():
            areas = [','.join([str(int(x)) for x in box]) for box in boxes]
            stream_tables = camelot.read_pdf(path, password=password, pages=str(page),
                flavor='stream', table_areas=areas, row_tol=5)
            dataframes = [table.df for table in stream_tables]
            if dataframes:
                page_df = pd.concat(dataframes, ignore_index=True)
                all_dataframes.append(page_df)
        
        if all_dataframes:
            df = pd.concat(all_dataframes, ignore_index=True)
            return process_transactions(df)
        else:
            return pd.DataFrame()

def extract_df_icici(path, password=None):
    """ICICI specific extraction - completely rewritten for better performance"""
    try:
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=UserWarning)
            
            print("Extracting ICICI bank statement...")
            
            # Use stream flavor for ICICI as it works better for their format
            tables = camelot.read_pdf(path, password=password, 
                pages='all', flavor='stream', 
                row_tol=10, column_tol=10)
            
            print(f"Found {len(tables)} tables across all pages")
            
            all_dataframes = []
            
            for i, table in enumerate(tables):
                print(f"Processing table {i+1}/{len(tables)} - Shape: {table.df.shape}")
                
                # Check if table contains transaction data
                df_text = table.df.to_string().upper()
                
                # Look for ICICI-specific indicators
                has_icici_data = any(indicator in df_text for indicator in [
                    'UPI/', 'NEFT', 'IMPS', 'MMT/IMPS', 'DR', 'CR', 
                    '-2025', '-2024', 'BANK/', 'PAYMENT'
                ])
                
                # Check for date patterns (DD-MM-YYYY)
                has_dates = bool(re.search(r'\d{2}-\d{2}-\d{4}', df_text))
                
                # Check for amount patterns  
                has_amounts = bool(re.search(r'\d+\.\d{2}', df_text))
                
                if has_icici_data and has_dates and has_amounts:
                    print(f"  Table {i+1} contains transaction data")
                    all_dataframes.append(table.df)
                else:
                    print(f"  Table {i+1} skipped (no transaction data)")
            
            if all_dataframes:
                print(f"Combining {len(all_dataframes)} transaction tables...")
                combined_df = pd.concat(all_dataframes, ignore_index=True)
                print(f"Combined dataframe shape: {combined_df.shape}")
                return process_transactions_icici(combined_df)
            else:
                print("No transaction tables found")
                return pd.DataFrame()
                
    except Exception as e:
        print(f"Error in ICICI extraction: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def is_date_icici(value):
    """ICICI date format checker - DD-MM-YYYY"""
    if pd.isna(value) or value == '':
        return False
    return bool(re.match(r'^\d{2}-\d{2}-\d{4}$', str(value).strip()))

def extract_reference_number_icici(description):
    """Enhanced ICICI reference number extraction"""
    if pd.isna(description):
        return '', ''
    
    text = str(description).strip()
    
    # ICICI specific patterns - more comprehensive
    patterns = [
        r'/([A-Z]{3}[a-f0-9]{8,})',                     # ICI reference codes like ICI65d4fd3ec
        r'/(\d{12,})',                                   # Long numeric references
        r'UPI/[^/]+/[^/]+/[^/]+/(\d{12,})',             # UPI transaction IDs
        r'NEFT[^/]*/([A-Z0-9]{15,})',                   # NEFT references
        r'IMPS/(\d{12,})',                              # IMPS references  
        r'MMT/IMPS/(\d{12,})',                          # MMT IMPS references
        r'([A-Z]{3}\d{12,})',                           # Generic bank references
        r'/([A-Z0-9]{15,})',                            # Long alphanumeric codes
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            ref_num = match.group(1)
            # Clean the description by removing the reference
            clean_text = text.replace(f'/{ref_num}', '').replace(ref_num, '')
            clean_text = re.sub(r'/+', '/', clean_text)  # Clean up multiple slashes
            clean_text = re.sub(r'^/', '', clean_text)   # Remove leading slash
            clean_text = re.sub(r'/$', '', clean_text)   # Remove trailing slash
            clean_text = re.sub(r'\s+', ' ', clean_text).strip()
            return ref_num, clean_text
    
    return '', text

def extract_date_from_row(row):
    """Extract date from any part of the row"""
    if len(row) == 0:
        return None
        
    for cell in row:
        if pd.isna(cell):
            continue
        cell_text = str(cell).strip()
        if '\n' in cell_text:
            parts = cell_text.split('\n')
            for part in parts:
                part = part.strip()
                if is_date_icici(part):
                    return part
        elif is_date_icici(cell_text):
            return cell_text
    return None

def extract_amount_from_row(row):
    """Extract amount from any part of the row"""
    if len(row) == 0:
        return None
        
    amount_pattern = r'^\d{1,3}(,\d{3})*\.\d{2}$|^\d{4,6}\.\d{2}$'
    
    for cell in row:
        if pd.isna(cell):
            continue
        cell_text = str(cell).strip()
        if '\n' in cell_text:
            parts = cell_text.split('\n')
            for part in parts:
                part = part.strip()
                if re.match(amount_pattern, part):
                    return part
        elif re.match(amount_pattern, cell_text):
            return cell_text
    return None

def extract_type_from_row(row):
    """Extract transaction type (CR/DR) from any part of the row"""
    if len(row) == 0:
        return None
        
    for cell in row:
        if pd.isna(cell):
            continue
        cell_text = str(cell).strip()
        if '\n' in cell_text:
            parts = cell_text.split('\n')
            for part in parts:
                part = part.strip()
                if re.match(r'^(CR|DR)$', part):
                    return part
        elif re.match(r'^(CR|DR)$', cell_text):
            return cell_text
    return None

def extract_description_parts_from_row(row, exclude_date=None, exclude_amount=None, exclude_type=None):
    """Extract description parts from row, excluding known components"""
    if len(row) == 0:
        return []
        
    description_parts = []
    amount_pattern = r'^\d{1,3}(,\d{3})*\.\d{2}$|^\d{4,6}\.\d{2}$'
    
    for cell in row:
        if pd.isna(cell):
            continue
        cell_text = str(cell).strip()
        if '\n' in cell_text:
            parts = cell_text.split('\n')
            for part in parts:
                part = part.strip()
                # Skip empty, dates, amounts, and types
                if (part and part != 'nan' and 
                    not is_date_icici(part) and 
                    not re.match(amount_pattern, part) and 
                    not re.match(r'^(CR|DR)$', part) and
                    part != exclude_date and 
                    part != exclude_amount and 
                    part != exclude_type):
                    description_parts.append(part)
        else:
            # Skip empty, dates, amounts, and types
            if (cell_text and cell_text != 'nan' and 
                not is_date_icici(cell_text) and 
                not re.match(amount_pattern, cell_text) and 
                not re.match(r'^(CR|DR)$', cell_text) and
                cell_text != exclude_date and 
                cell_text != exclude_amount and 
                cell_text != exclude_type):
                description_parts.append(cell_text)
    
    return description_parts

def parse_multi_row_transaction_icici(df, start_idx):
    """Parse a transaction that may span multiple rows"""
    if start_idx >= len(df):
        return None, 0
    
    # Look for date in current row to start transaction
    date_val = extract_date_from_row(df.iloc[start_idx])
    if not date_val:
        return None, 1
    
    # Collect data from this row and subsequent rows until we have amount and type
    transaction_type = None
    amount_str = None
    description_parts = []
    rows_consumed = 0
    
    # Check up to 5 rows ahead for complete transaction data
    for i in range(start_idx, min(start_idx + 5, len(df))):
        row = df.iloc[i]
        rows_consumed = i - start_idx + 1
        
        # Extract components from current row
        if not transaction_type:
            transaction_type = extract_type_from_row(row)
        
        if not amount_str:
            amount_str = extract_amount_from_row(row)
        
        # Add description parts (excluding already found components)
        desc_parts = extract_description_parts_from_row(row, date_val, amount_str, transaction_type)
        description_parts.extend(desc_parts)
        
        # If we have all required components, we can form a transaction
        if date_val and amount_str and transaction_type:
            break
        
        # Stop if we hit another date (start of next transaction)
        if i > start_idx and extract_date_from_row(row):
            rows_consumed = i - start_idx  # Don't consume the next transaction's row
            break
    
    # If we don't have the minimum required components, skip
    if not date_val or not amount_str:
        return None, rows_consumed
    
    # Parse amount
    try:
        amount = float(amount_str.replace(',', ''))
    except ValueError:
        return None, rows_consumed
    
    # Build description
    description = ' '.join(description_parts).strip()
    
    # Extract reference number and clean description
    ref_num, clean_description = extract_reference_number_icici(description)
    
    # Determine withdrawal/deposit based on type
    withdrawal = ''
    deposit = ''
    
    if transaction_type and transaction_type.upper() == 'DR':
        withdrawal = f"{amount:,.2f}"
    elif transaction_type and transaction_type.upper() == 'CR':
        deposit = f"{amount:,.2f}"
    else:
        # If no clear type found, assume withdrawal (most common)
        withdrawal = f"{amount:,.2f}"
    
    return {
        'Date': date_val,
        'Narration': clean_description if clean_description else description,
        'Chq./Ref.No.': ref_num if ref_num else '',
        'Value Dt': date_val,  # ICICI doesn't have separate value date
        'Withdrawal Amt.': withdrawal,
        'Deposit Amt.': deposit,
        'Closing Balance': ''  # ICICI format doesn't include running balance
    }, rows_consumed

def is_summary_start(row):
    """Check if row indicates start of summary section"""
    text = ' '.join([str(cell) for cell in row]).upper()
    summary_markers = [
        'STATEMENT SUMMARY', 'OPENING BALANCE', 'DR COUNT', 'CR COUNT',
        'THIS IS A SYSTEM-GENERATED STATEMENT', 'TOTAL DEBITS', 'TOTAL CREDITS'
    ]
    return any(marker in text for marker in summary_markers)

def process_transactions_icici(df):
    """Enhanced ICICI transaction processor - handles multi-row transactions"""
    if df.empty:
        return df
    
    print(f"Processing ICICI dataframe with shape: {df.shape}")
    
    # Find the start of transaction data
    start_idx = 0
    header_found = False
    
    for idx, row in df.iterrows():
        row_text = ' '.join([str(cell) for cell in row]).upper()
        
        # Look for transaction headers or first valid transaction
        if any(header in row_text for header in ['DATE', 'DESCRIPTION', 'AMOUNT', 'TYPE']):
            start_idx = idx + 1
            header_found = True
            print(f"Found transaction header at row {idx}")
            break
        elif extract_date_from_row(row):
            start_idx = idx
            header_found = True
            print(f"Found first transaction at row {idx}")
            break
    
    if not header_found:
        print("No clear transaction start found, processing entire dataframe")
    
    df = df.iloc[start_idx:].reset_index(drop=True)
    
    # Find end of transaction data
    end_idx = len(df)
    for idx, row in df.iterrows():
        if is_summary_start(row):
            end_idx = idx
            print(f"Found end of transactions at row {idx}")
            break
    
    df = df.iloc[:end_idx]
    print(f"Processing rows {start_idx} to {end_idx}")
    
    # Parse multi-row transactions
    transactions = []
    i = 0
    
    while i < len(df):
        # Try to parse a multi-row transaction starting at row i
        parsed, rows_consumed = parse_multi_row_transaction_icici(df, i)
        
        if parsed:
            transactions.append(parsed)
            if len(transactions) <= 5 or len(transactions) % 50 == 0:
                print(f"  Parsed transaction {len(transactions)}: {parsed['Date']} - {parsed['Narration'][:50]}...")
        
        # Move to next potential transaction start
        i += rows_consumed if rows_consumed > 0 else 1
    
    print(f"Final results: {len(transactions)} transactions parsed")
    
    if not transactions:
        print("No transactions found! Showing first 10 rows for debugging:")
        for i in range(min(10, len(df))):
            row_data = [str(cell) for cell in df.iloc[i][:6] if str(cell).strip() not in ['', 'nan']]
            print(f"  Row {i}: {row_data}")
        return pd.DataFrame()
    
    result_df = pd.DataFrame(transactions)
    
    # Clean up empty values
    for col in result_df.columns:
        result_df[col] = result_df[col].replace(['', 'nan', 'None'], pd.NA)
    
    print(f"Created final dataframe with {len(result_df)} transactions")
    return result_df

# Keep all the HDFC functions - with improvements for multi-line handling
def is_date(value):
    """HDFC date format checker"""
    if pd.isna(value) or value == '':
        return False
    return bool(re.match(r'^\d{2}/\d{2}/\d{2}$', str(value).strip()))

def clean_narration_text(text):
    """Clean up narration text by removing line breaks and normalizing spaces"""
    if pd.isna(text):
        return ''
    
    cleaned = str(text).strip()
    # Remove actual line breaks and normalize spaces
    cleaned = re.sub(r'\n+', ' ', cleaned)
    cleaned = re.sub(r'\\n+', ' ', cleaned)
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()
    
    return cleaned

def extract_reference_number(narration):
    """HDFC reference number extraction - improved with better text cleaning"""
    if pd.isna(narration):
        return '', ''
    
    text = clean_narration_text(narration)
    
    patterns = [
        r'-(\d{12,})-',                    
        r'PAY-HDFC\d+-(\d{12,})-',        
        r'([A-Z]{4,}\d{15,})(?:\s|$)',    
        r'(\d{15,})(?:\s|$)',             
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            ref_num = match.group(1)
            clean_text = re.sub(pattern, lambda m: m.group(0).replace(ref_num, ''), text)
            clean_text = re.sub(r'\s+', ' ', clean_text).strip()
            clean_text = re.sub(r'-\s*-', '-', clean_text)
            return ref_num, clean_text
    
    return '', text

def parse_transaction_row(row):
    """Original HDFC transaction row parser - unchanged"""
    if len(row) < 3:
        return None
    
    cells = [str(cell).strip() for cell in row]
    
    date_val = cells[0]
    if not is_date(date_val):
        return None
    
    narration = cells[1] if len(cells) > 1 else ''
    ref_num, clean_narration = extract_reference_number(narration)
    
    amounts = []
    amount_positions = []
    date_positions = []
    
    for i, cell in enumerate(cells):
        if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cell):
            amounts.append(float(cell.replace(',', '')))
            amount_positions.append(i)
        elif is_date(cell) and i > 0:
            date_positions.append(i)
    
    ref_col = ref_num
    value_dt = date_val
    withdrawal = deposit = balance = ''
    
    if len(cells) >= 7:
        if is_date(cells[3]):
            ref_col = cells[2] if cells[2] not in ['nan', '', 'None'] else ref_num
            value_dt = cells[3]
            
            if len(cells) == 8 and cells[4] == '' and cells[5] == '':
                withdrawal = ''
                deposit = cells[6] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[6]) else ''
                balance = cells[7] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[7]) else ''
            elif len(cells) == 8:
                amounts_in_cells = []
                for i in range(4, 8):
                    if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[i]):
                        amounts_in_cells.append((i, float(cells[i].replace(',', ''))))
                
                if len(amounts_in_cells) == 2:
                    amounts_in_cells.sort(key=lambda x: x[0])
                    first_val = amounts_in_cells[0][1]
                    second_val = amounts_in_cells[1][1]
                    
                    withdrawal = f"{first_val:.2f}"
                    deposit = ''
                    balance = f"{second_val:.2f}"
                else:
                    withdrawal = cells[4] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[4]) else ''
                    deposit = cells[5] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[5]) else ''
                    balance = cells[6] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[6]) else ''
            else:
                withdrawal = cells[4] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[4]) else ''
                deposit = cells[5] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[5]) else ''
                balance = cells[6] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[6]) else ''
        else:
            ref_col = ref_num
            
            potential_amounts = []
            for i in range(2, len(cells)):
                if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[i]):
                    potential_amounts.append((i, float(cells[i].replace(',', ''))))
            
            if len(potential_amounts) >= 2:
                balance = f"{potential_amounts[-1][1]:.2f}"
                
                if len(potential_amounts) >= 3:
                    withdrawal = f"{potential_amounts[0][1]:.2f}"
                    deposit = f"{potential_amounts[1][1]:.2f}"
                elif len(potential_amounts) == 2:
                    withdrawal = f"{potential_amounts[0][1]:.2f}"
            elif len(potential_amounts) == 1:
                pos, amt = potential_amounts[0]
                
                if pos >= 5:
                    balance = f"{amt:.2f}"
                else:
                    withdrawal = f"{amt:.2f}"
    
    elif len(date_positions) > 0:
        value_dt = cells[date_positions[0]]
        ref_col = ref_num
        
        amounts_after_date = [i for i in range(len(cells)) if i > date_positions[0] and re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[i])]
        
        if amounts_after_date:            
            if len(amounts_after_date) >= 3:
                withdrawal = cells[amounts_after_date[0]]
                deposit = cells[amounts_after_date[1]]
                balance = cells[amounts_after_date[2]]
            elif len(amounts_after_date) == 2:
                withdrawal = cells[amounts_after_date[0]]
                balance = cells[amounts_after_date[1]]
            elif len(amounts_after_date) == 1:
                balance = cells[amounts_after_date[0]]
    
    else:
        ref_col = ref_num
        
        if len(amounts) >= 3:
            withdrawal = f"{amounts[0]:.2f}"
            deposit = f"{amounts[1]:.2f}"
            balance = f"{amounts[2]:.2f}"
        elif len(amounts) == 2:
            withdrawal = f"{amounts[0]:.2f}"
            balance = f"{amounts[1]:.2f}"
        elif len(amounts) == 1:
            amount_pos = amount_positions[0]
            
            if amount_pos >= 5:
                balance = f"{amounts[0]:.2f}"
            else:
                withdrawal = f"{amounts[0]:.2f}"
    
    if not ref_col or not re.match(r'^[A-Z0-9]{4,}$', ref_col):
        ref_col = ref_num
    
    def format_amount(amt_str):
        if amt_str and amt_str != '':
            try:
                amount = float(str(amt_str).replace(',', ''))
                return f"{amount:,.2f}"
            except:
                return amt_str
        return ''
    
    return {
        'Date': date_val,
        'Narration': clean_narration,
        'Chq./Ref.No.': ref_col if ref_col and ref_col != 'nan' else '',
        'Value Dt': value_dt if value_dt and value_dt != 'nan' else date_val,
        'Withdrawal Amt.': format_amount(withdrawal),
        'Deposit Amt.': format_amount(deposit),
        'Closing Balance': format_amount(balance)
    }

def is_valid_continuation(row):
    """HDFC continuation checker - improved to better detect multi-line descriptions"""
    if len(row) < 2:
        return False
    
    # Check if first column (date column) is empty or doesn't contain a date
    first_col = str(row.iloc[0]).strip()
    if first_col and first_col != 'nan' and is_date(first_col):
        return False  # This is a new transaction, not a continuation
    
    # Check if there's meaningful text in the narration column (second column)
    continuation = str(row.iloc[1]).strip()
    
    if not continuation or continuation == 'nan':
        return False
    
    # Skip rows with only numbers, punctuation, or reference numbers
    if re.match(r'^[\d\s.,()-]+$', continuation):
        return False
    
    # Skip standalone reference numbers
    if re.match(r'^[A-Z0-9]{10,}$', continuation):
        return False
    
    # Skip if it looks like column headers
    if any(header in continuation.upper() for header in ['DATE', 'NARRATION', 'AMOUNT', 'BALANCE']):
        return False
    
    # If we have meaningful text that's not a date and not a pure reference, it's likely a continuation
    return len(continuation) > 3 and not continuation.isdigit()

def fix_withdrawal_deposit_logic(transactions):
    """HDFC balance logic - unchanged"""
    if not transactions:
        return transactions
    
    def to_float(val):
        if pd.isna(val) or val == '' or val == 'nan':
            return 0.0
        return float(str(val).replace(',', ''))
    
    for i, trans in enumerate(transactions):
        withdrawal = to_float(trans.get('Withdrawal Amt.', ''))
        deposit = to_float(trans.get('Deposit Amt.', ''))
        current_balance = to_float(trans.get('Closing Balance', ''))
        
        if (withdrawal > 0 and deposit > 0) or (withdrawal == 0 and deposit == 0):
            continue
            
        if i > 0:
            prev_balance = to_float(transactions[i-1].get('Closing Balance', ''))
            if prev_balance > 0 and current_balance > 0:
                if current_balance > prev_balance:
                    if withdrawal > 0:
                        trans['Deposit Amt.'] = f"{withdrawal:,.2f}"
                        trans['Withdrawal Amt.'] = ''
    
    return transactions

def process_transactions(df):
    """HDFC transaction processor - improved to better handle multi-line descriptions"""
    if df.empty:
        return df
    
    start_idx = 0
    for idx, row in df.iterrows():
        if 'Date' in str(row.iloc[0]):
            start_idx = idx + 1
            break
    
    df = df.iloc[start_idx:].reset_index(drop=True)
    
    end_idx = len(df)
    for idx, row in df.iterrows():
        if is_summary_start(row):
            end_idx = idx
            break
    
    df = df.iloc[:end_idx]
    
    transactions = []
    current_transaction = None
    
    for idx, row in df.iterrows():
        if len(row) == 0:
            continue
            
        parsed = parse_transaction_row(row)
        
        if parsed:
            if current_transaction:
                # Clean up narration before adding to transactions
                current_transaction['Narration'] = clean_narration_text(current_transaction['Narration'])
                transactions.append(current_transaction)
            current_transaction = parsed
        else:
            if current_transaction and is_valid_continuation(row):
                continuation = str(row.iloc[1]).strip()
                if continuation and continuation != 'nan':
                    # Add space before continuation to avoid concatenation issues
                    current_transaction['Narration'] += ' ' + continuation
    
    if current_transaction:
        # Clean up narration for the last transaction
        current_transaction['Narration'] = clean_narration_text(current_transaction['Narration'])
        transactions.append(current_transaction)
    
    transactions = fix_withdrawal_deposit_logic(transactions)
    
    result_df = pd.DataFrame(transactions)
    
    # Final cleanup of all text fields
    for col in result_df.columns:
        if col == 'Narration':
            result_df[col] = result_df[col].apply(clean_narration_text)
        result_df[col] = result_df[col].replace(['', 'nan', 'None'], pd.NA)
    
    return result_df

# Main execution
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
        df = extract_df(pdf_path)
        
        if not df.empty:
            base_name = os.path.basename(pdf_path).replace('.pdf', '')
            output_file = f"transactions_{base_name}.csv"
            
            df.to_csv(output_file, index=False)
            print(f"Successfully extracted {len(df)} transactions")
            print(f"Transactions saved to {output_file}")
            print("\nFirst few transactions:")
            print(df.head())
            print(df.tail())
        else:
            print("No transactions found or unsupported format")
    else:
        print("Usage: python script.py <pdf_file_path>")
        print("Supports both HDFC and ICICI bank statements with auto-detection")
