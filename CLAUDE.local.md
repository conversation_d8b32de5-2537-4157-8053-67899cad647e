## DEVELOPMENT FOCUS
**CRITICAL**: Backend + web UI only. Mobile UI postponed until web completion.

## DESIGN SYSTEM ESSENTIALS

### Core Colors
```css
--primary-blue: #3B82F6;
--primary-purple: #8B5CF6;
--success-green: #10B981;
--error-red: #EF4444;
--warning-orange: #F59E0B;
```

### Component Templates
```jsx
/* Primary Button */
className="bg-gradient-to-r from-primary-blue to-primary-purple text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200"

/* Base Card */
className="bg-surface-elevated rounded-xl border border-border-light p-6 shadow-sm hover:shadow-md transition-all duration-200"

/* Input Field */
className="w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-2 focus:ring-primary-blue/20"
```

### Reusable Components
- **PageLayout**: Page structure with title/description/actions
- **LoadingState**: Standardized loading indicators
- **EmptyState**: Consistent empty states with CTAs
- **TabNavigation**: Uniform tab interface with counts
- **MetricCard**: Dashboard metric cards
- **ContentCard**: Unified card styling

## CRITICAL REQUIREMENTS
- **Currency**: Always use user's selected currency (never hardcode)
- **Icons**: Use CategoryIcon component pattern for all icon displays
- **Lucide React**: Default 20px (w-5 h-5), stroke-width 2

## KEY FILE PATHS
```
apps/web/src/
├── app/                    # Next.js pages
│   ├── dashboard/page.tsx
│   ├── transactions/page.tsx
│   ├── categories/page.tsx
│   └── templates/page.tsx
├── components/             # Reusable UI components
└── contexts/               # React contexts

packages/shared/src/
├── lib/                    # Business logic
├── types.ts                # Core TypeScript definitions
└── database.types.ts       # Supabase generated types
```

## IMPLEMENTATION STATUS
✅ **Completed**: Dashboard, Transactions, Categories, Templates
🔄 **Pending**: Budgets, Investments, Accounts, Profile

## TOOL EFFICIENCY
Check this file FIRST before using search tools. Use documented paths directly.

## SYSTEM INTERACTION PROMPTS
Follow these steps for each interaction:

1. **User Identification**:
   - Assume you are interacting with default_user
   - If you have not identified default_user, proactively try to do so

2. **Memory Retrieval**:
   - Always begin your chat by saying only "Remembering..." and retrieve all relevant information from your knowledge graph
   - Always refer to your knowledge graph as your "memory"

3. **Memory Capture**:
   - While conversing with the user, be attentive to any new information that falls into these categories:
     a) Basic Identity (age, gender, location, job title, education level, etc.)
     b) Behaviors (interests, habits, etc.)
     c) Preferences (communication style, preferred language, etc.)
     d) Goals (goals, targets, aspirations, etc.)
     e) Relationships (personal and professional relationships up to 3 degrees of separation)

4. **Memory Update**:
   - If any new information was gathered during the interaction, update your memory as follows:
     a) Create entities for recurring organizations, people, and significant events
     b) Connect them to the current entities using relations
     c) Store facts about them as observations