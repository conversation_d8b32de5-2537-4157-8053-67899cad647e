import camelot
import pandas as pd
from collections import defaultdict
import re
import os
import warnings

def detect_bank_type(path, password=None):
    """Detect whether the PDF is an HDFC or ICICI bank statement"""
    try:
        # Suppress camelot warnings during detection
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=UserWarning)
            
            # Read first few pages to detect bank type
            tables = camelot.read_pdf(path, password=password, pages='1-2', flavor='stream')
            if not tables:
                return 'HDFC'  # Default to HDFC
                
            # Check text content for bank identifiers
            for table in tables:
                df_text = table.df.to_string().upper()
                if 'ICICI' in df_text or 'ICICI BANK' in df_text:
                    return 'ICICI'
                elif 'HDFC BANK' in df_text:
                    return 'HDFC'
    except Exception as e:
        print(f"Bank detection warning (non-critical): {e}")
        pass
    
    return 'HDFC'  # Default to HDFC if detection fails

def extract_df(path, password=None):
    """Main extraction function with bank auto-detection"""
    bank_type = detect_bank_type(path, password)
    print(f"Detected {bank_type} bank statement")
    
    if bank_type == 'ICICI':
        return extract_df_icici(path, password)
    else:
        return extract_df_hdfc(path, password)

def extract_df_hdfc(path, password=None):
    """Original HDFC extraction logic - with warning suppression"""
    with warnings.catch_warnings():
        warnings.filterwarnings("ignore", category=UserWarning)
        
        lattice_tables = camelot.read_pdf(path, password=password, 
            pages='all', flavor='lattice', line_scale=50)

        regions = defaultdict(list)
        for table in lattice_tables:
            bbox = [table._bbox[i] for i in [0, 3, 2, 1]]
            regions[table.page].append(bbox)

        all_dataframes = []
        for page, boxes in regions.items():
            areas = [','.join([str(int(x)) for x in box]) for box in boxes]
            stream_tables = camelot.read_pdf(path, password=password, pages=str(page),
                flavor='stream', table_areas=areas, row_tol=5)
            dataframes = [table.df for table in stream_tables]
            if dataframes:
                page_df = pd.concat(dataframes, ignore_index=True)
                all_dataframes.append(page_df)
        
        if all_dataframes:
            df = pd.concat(all_dataframes, ignore_index=True)
            return process_transactions(df)
        else:
            return pd.DataFrame()

def extract_df_icici(path, password=None):
    """ICICI specific extraction - completely rewritten for better performance"""
    try:
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=UserWarning)
            
            print("Extracting ICICI bank statement...")
            
            # Use stream flavor for ICICI as it works better for their format
            tables = camelot.read_pdf(path, password=password, 
                pages='all', flavor='stream', 
                row_tol=10, column_tol=10)
            
            print(f"Found {len(tables)} tables across all pages")
            
            all_dataframes = []
            
            for i, table in enumerate(tables):
                print(f"Processing table {i+1}/{len(tables)} - Shape: {table.df.shape}")
                
                # Check if table contains transaction data
                df_text = table.df.to_string().upper()
                
                # Look for ICICI-specific indicators
                has_icici_data = any(indicator in df_text for indicator in [
                    'UPI/', 'NEFT', 'IMPS', 'MMT/IMPS', 'DR', 'CR', 
                    '-2025', '-2024', 'BANK/', 'PAYMENT'
                ])
                
                # Check for date patterns (DD-MM-YYYY)
                has_dates = bool(re.search(r'\d{2}-\d{2}-\d{4}', df_text))
                
                # Check for amount patterns  
                has_amounts = bool(re.search(r'\d+\.\d{2}', df_text))
                
                if has_icici_data and has_dates and has_amounts:
                    print(f"  Table {i+1} contains transaction data")
                    all_dataframes.append(table.df)
                else:
                    print(f"  Table {i+1} skipped (no transaction data)")
            
            if all_dataframes:
                print(f"Combining {len(all_dataframes)} transaction tables...")
                combined_df = pd.concat(all_dataframes, ignore_index=True)
                print(f"Combined dataframe shape: {combined_df.shape}")
                return process_transactions_icici(combined_df)
            else:
                print("No transaction tables found")
                return pd.DataFrame()
                
    except Exception as e:
        print(f"Error in ICICI extraction: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def is_date(value):
    """HDFC date format checker"""
    if pd.isna(value) or value == '':
        return False
    return bool(re.match(r'^\d{2}/\d{2}/\d{2}$', str(value).strip()))

def is_date_icici(value):
    """ICICI date format checker"""
    if pd.isna(value) or value == '':
        return False
    return bool(re.match(r'^\d{2}-\d{2}-\d{4}$', str(value).strip()))

def is_summary_start(row):
    text = ' '.join([str(cell) for cell in row]).upper()
    summary_markers = ['STATEMENT SUMMARY', 'OPENING BALANCE', 'DR COUNT', 'CR COUNT',
                      'This is a system-generated statement']
    return any(marker in text for marker in summary_markers)

def extract_reference_number(narration):
    """HDFC reference number extraction - unchanged"""
    if pd.isna(narration):
        return '', ''
    
    text = str(narration).strip()
    
    # Fixed regex patterns
    patterns = [
        r'-(\d{12,})-',                    
        r'PAY-HDFC\d+-(\d{12,})-',        
        r'([A-Z]{4,}\d{15,})(?:\s|$)',    
        r'\\n(\d{12,})',                  
        r'(\d{15,})(?:\s|$)',             
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            ref_num = match.group(1)
            clean_text = re.sub(pattern, lambda m: m.group(0).replace(ref_num, ''), text)
            clean_text = re.sub(r'\s+', ' ', clean_text).strip()
            clean_text = re.sub(r'-\s*-', '-', clean_text)
            return ref_num, clean_text
    
    return '', text.replace('\\n', ' ')

def extract_reference_number_icici(description):
    """Enhanced ICICI reference number extraction"""
    if pd.isna(description):
        return '', ''
    
    text = str(description).strip()
    
    # ICICI specific patterns - more comprehensive
    patterns = [
        r'/([A-Z]{3}[a-f0-9]{8,})',                     # ICI reference codes like ICI65d4fd3ec
        r'/(\d{12,})',                                   # Long numeric references
        r'UPI/[^/]+/[^/]+/[^/]+/(\d{12,})',             # UPI transaction IDs
        r'NEFT[^/]*/([A-Z0-9]{15,})',                   # NEFT references
        r'IMPS/(\d{12,})',                              # IMPS references  
        r'MMT/IMPS/(\d{12,})',                          # MMT IMPS references
        r'([A-Z]{3}\d{12,})',                           # Generic bank references
        r'/([A-Z0-9]{15,})',                            # Long alphanumeric codes
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            ref_num = match.group(1)
            # Clean the description by removing the reference
            clean_text = text.replace(f'/{ref_num}', '').replace(ref_num, '')
            clean_text = re.sub(r'/+', '/', clean_text)  # Clean up multiple slashes
            clean_text = re.sub(r'^/', '', clean_text)   # Remove leading slash
            clean_text = re.sub(r'/$', '', clean_text)   # Remove trailing slash
            clean_text = re.sub(r'\s+', ' ', clean_text).strip()
            return ref_num, clean_text
    
    return '', text

def parse_transaction_row(row):
    """Original HDFC transaction row parser - unchanged"""
    if len(row) < 3:
        return None
    
    cells = [str(cell).strip() for cell in row]
    
    
    date_val = cells[0]
    if not is_date(date_val):
        return None
    
    narration = cells[1] if len(cells) > 1 else ''
    ref_num, clean_narration = extract_reference_number(narration)
    
    # Find amounts and dates in the row
    amounts = []
    amount_positions = []
    date_positions = []
    
    for i, cell in enumerate(cells):
        if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cell):
            amounts.append(float(cell.replace(',', '')))
            amount_positions.append(i)
        elif is_date(cell) and i > 0:
            date_positions.append(i)
    
    # Initialize defaults
    ref_col = ref_num
    value_dt = date_val
    withdrawal = deposit = balance = ''
    
    # Don't use text patterns to determine credit/debit - use balance logic instead
    
    # Analyze row structure more carefully
    if len(cells) >= 7:
        # Check if this is a proper 7-column format by analyzing positions
        if is_date(cells[3]):
            ref_col = cells[2] if cells[2] not in ['nan', '', 'None'] else ref_num
            value_dt = cells[3]
            
            # Detect column structure based on content, not position
            if len(cells) == 8 and cells[4] == '' and cells[5] == '':
                # Pattern: Date, Narration, Ref, Value_Dt, Empty, Empty, Deposit, Balance
                withdrawal = ''
                deposit = cells[6] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[6]) else ''
                balance = cells[7] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[7]) else ''
            elif len(cells) == 8:
                # Pattern: Date, Narration, Ref, Value_Dt, Amount, Balance, nan/empty, nan/empty
                # Find the largest amount (likely balance) and assign accordingly
                amounts_in_cells = []
                for i in range(4, 8):
                    if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[i]):
                        amounts_in_cells.append((i, float(cells[i].replace(',', ''))))
                
                if len(amounts_in_cells) == 2:
                    # Two amounts found - use position to determine which is which
                    amounts_in_cells.sort(key=lambda x: x[0])  # Sort by position, not value
                    first_val = amounts_in_cells[0][1]
                    second_val = amounts_in_cells[1][1]
                    
                    # First amount is transaction amount, second is balance (based on position)
                    withdrawal = f"{first_val:.2f}"
                    deposit = ''
                    balance = f"{second_val:.2f}"
                else:
                    # Fallback to position-based for other cases
                    withdrawal = cells[4] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[4]) else ''
                    deposit = cells[5] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[5]) else ''
                    balance = cells[6] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[6]) else ''
            else:
                # Standard 7-column: Date, Narration, Ref, Value_Dt, Withdrawal, Deposit, Balance
                withdrawal = cells[4] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[4]) else ''
                deposit = cells[5] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[5]) else ''
                balance = cells[6] if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[6]) else ''
        else:
            # Malformed 7+ column - check if amounts are actually in wrong positions
            ref_col = ref_num
            
            # Look for the actual structure by examining what's in each position
            potential_amounts = []
            for i in range(2, len(cells)):
                if re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[i]):
                    potential_amounts.append((i, float(cells[i].replace(',', ''))))
            
            if len(potential_amounts) >= 2:
                # Last amount is typically balance
                balance = f"{potential_amounts[-1][1]:.2f}"
                
                if len(potential_amounts) >= 3:
                    withdrawal = f"{potential_amounts[0][1]:.2f}"
                    deposit = f"{potential_amounts[1][1]:.2f}"
                elif len(potential_amounts) == 2:
                    # Put in withdrawal initially, balance logic will fix if it should be deposit
                    withdrawal = f"{potential_amounts[0][1]:.2f}"
            elif len(potential_amounts) == 1:
                # Single amount - determine what it represents based on context and position
                pos, amt = potential_amounts[0]
                
                # Single amount - put in withdrawal initially, balance logic will fix if needed
                if pos >= 5:  # Later position suggests balance
                    balance = f"{amt:.2f}"
                else:
                    withdrawal = f"{amt:.2f}"
    
    elif len(date_positions) > 0:
        # Has separate Value Dt column
        value_dt = cells[date_positions[0]]
        ref_col = ref_num
        
        # Find amounts after Value Dt
        amounts_after_date = [i for i in range(len(cells)) if i > date_positions[0] and re.match(r'^\d{1,3}(,\d{3})*\.\d{2}$', cells[i])]
        
        if amounts_after_date:            
            if len(amounts_after_date) >= 3:
                withdrawal = cells[amounts_after_date[0]]
                deposit = cells[amounts_after_date[1]]
                balance = cells[amounts_after_date[2]]
            elif len(amounts_after_date) == 2:
                # Put transaction amount in withdrawal initially, will fix with balance logic
                withdrawal = cells[amounts_after_date[0]]
                balance = cells[amounts_after_date[1]]
            elif len(amounts_after_date) == 1:
                balance = cells[amounts_after_date[0]]  # Only balance available
    
    else:
        # Compact format - determine from number of amounts
        ref_col = ref_num
        
        if len(amounts) >= 3:
            withdrawal = f"{amounts[0]:.2f}"
            deposit = f"{amounts[1]:.2f}"
            balance = f"{amounts[2]:.2f}"
        elif len(amounts) == 2:
            # Put transaction amount in withdrawal initially, will fix with balance logic
            withdrawal = f"{amounts[0]:.2f}"
            balance = f"{amounts[1]:.2f}"
        elif len(amounts) == 1:
            # Single amount - determine what it represents
            amount_pos = amount_positions[0]
            
            # Single amount - put in withdrawal initially, will fix with balance logic
            if amount_pos >= 5:  # Later position suggests balance
                balance = f"{amounts[0]:.2f}"
            else:
                withdrawal = f"{amounts[0]:.2f}"
    
    # Use extracted ref if column ref is not valid
    if not ref_col or not re.match(r'^[A-Z0-9]{4,}$', ref_col):
        ref_col = ref_num
    
    # Ensure consistent number formatting with commas
    def format_amount(amt_str):
        if amt_str and amt_str != '':
            try:
                # Remove existing commas and convert to float
                amount = float(str(amt_str).replace(',', ''))
                return f"{amount:,.2f}"
            except:
                return amt_str
        return ''
    
    return {
        'Date': date_val,
        'Narration': clean_narration,
        'Chq./Ref.No.': ref_col if ref_col and ref_col != 'nan' else '',
        'Value Dt': value_dt if value_dt and value_dt != 'nan' else date_val,
        'Withdrawal Amt.': format_amount(withdrawal),
        'Deposit Amt.': format_amount(deposit),
        'Closing Balance': format_amount(balance)
    }

def parse_transaction_row_icici(row):
    """Enhanced ICICI transaction parsing"""
    if len(row) < 3:
        return None
    
    cells = [str(cell).strip() for cell in row if str(cell).strip() not in ['', 'nan']]
    
    if len(cells) < 3:
        return None
    
    # ICICI format: Date, Description, Amount, Type
    date_val = cells[0]
    if not is_date_icici(date_val):
        return None
    
    description = cells[1] if len(cells) > 1 else ''
    
    # Find amount and type - they could be in different positions due to table parsing issues
    amount_str = ''
    transaction_type = ''
    
    # Look for amount pattern in remaining cells - enhanced to handle more formats
    amount_pattern = r'^\d{1,3}(,\d{3})*\.\d{2}$|^\d{4,6}\.\d{2}$'
    type_pattern = r'^(CR|DR)$'
    
    for i in range(2, len(cells)):
        cell_val = cells[i].strip()
        if re.match(amount_pattern, cell_val) and not amount_str:
            amount_str = cell_val
        elif re.match(type_pattern, cell_val) and not transaction_type:
            transaction_type = cell_val
    
    # If we couldn't find amount in expected position, skip this row
    if not amount_str:
        return None
    
    # Parse amount
    try:
        amount = float(amount_str.replace(',', ''))
    except ValueError:
        return None
    
    # Extract reference number and clean description
    ref_num, clean_description = extract_reference_number_icici(description)
    
    # Determine withdrawal/deposit based on type
    withdrawal = ''
    deposit = ''
    
    if transaction_type.upper() == 'DR':
        withdrawal = f"{amount:,.2f}"
    elif transaction_type.upper() == 'CR':
        deposit = f"{amount:,.2f}"
    else:
        # If no clear type, default to withdrawal (common case)
        withdrawal = f"{amount:,.2f}"
    
    return {
        'Date': date_val,
        'Narration': clean_description,
        'Chq./Ref.No.': ref_num if ref_num else '',
        'Value Dt': date_val,  # ICICI doesn't have separate value date
        'Withdrawal Amt.': withdrawal,
        'Deposit Amt.': deposit,
        'Closing Balance': ''  # ICICI format doesn't include running balance
    }

def is_valid_continuation_icici(row):
    """Check if row is a valid continuation for ICICI transactions"""
    if len(row) < 2:
        return False
    
    continuation = str(row.iloc[1]).strip()
    
    if not continuation or continuation == 'nan':
        return False
    
    # Don't treat pure numbers or dates as continuation
    if re.match(r'^[\d\s.,()-]+$', continuation):
        return False
    
    if re.match(r'^\d{2}-\d{2}-\d{4}$', continuation):  # ICICI date format
        return False
    
    if re.match(r'^[A-Z0-9]{10,}$', continuation):
        return False
    
    return True

def is_valid_continuation(row):
    if len(row) < 2:
        return False
    
    continuation = str(row.iloc[1]).strip()
    
    if not continuation or continuation == 'nan':
        return False
    
    if re.match(r'^[\d\s.,()-]+$', continuation):
        return False
    
    if re.match(r'^[A-Z0-9]{10,}$', continuation):
        return False
    
    return True

def fix_withdrawal_deposit_logic(transactions):
    """Use balance change logic to determine if amounts should be withdrawal or deposit"""
    if not transactions:
        return transactions
    
    def to_float(val):
        if pd.isna(val) or val == '' or val == 'nan':
            return 0.0
        return float(str(val).replace(',', ''))
    
    for i, trans in enumerate(transactions):
        withdrawal = to_float(trans.get('Withdrawal Amt.', ''))
        deposit = to_float(trans.get('Deposit Amt.', ''))
        current_balance = to_float(trans.get('Closing Balance', ''))
        
        # Skip if we already have both withdrawal and deposit, or no transaction amount
        if (withdrawal > 0 and deposit > 0) or (withdrawal == 0 and deposit == 0):
            continue
            
        # Get previous balance for comparison
        if i > 0:
            prev_balance = to_float(transactions[i-1].get('Closing Balance', ''))
            if prev_balance > 0 and current_balance > 0:
                # If balance increased, it's a deposit; if decreased, it's a withdrawal
                if current_balance > prev_balance:
                    # Balance increased - this should be a deposit
                    if withdrawal > 0:
                        trans['Deposit Amt.'] = f"{withdrawal:,.2f}"
                        trans['Withdrawal Amt.'] = ''
                else:
                    # Balance decreased - this should be a withdrawal (already correct)
                    pass
    
    return transactions

def process_transactions(df):
    """Original HDFC transaction processor - unchanged"""
    if df.empty:
        return df
    
    start_idx = 0
    for idx, row in df.iterrows():
        if 'Date' in str(row.iloc[0]):
            start_idx = idx + 1
            break
    
    df = df.iloc[start_idx:].reset_index(drop=True)
    
    end_idx = len(df)
    for idx, row in df.iterrows():
        if is_summary_start(row):
            end_idx = idx
            break
    
    df = df.iloc[:end_idx]
    
    transactions = []
    current_transaction = None
    
    for idx, row in df.iterrows():
        if len(row) == 0:
            continue
            
        parsed = parse_transaction_row(row)
        
        if parsed:
            if current_transaction:
                transactions.append(current_transaction)
            current_transaction = parsed
        else:
            if current_transaction and is_valid_continuation(row):
                continuation = str(row.iloc[1]).strip()
                current_transaction['Narration'] += ' ' + continuation
    
    if current_transaction:
        transactions.append(current_transaction)
    
    # Use balance change logic to fix withdrawal/deposit classification
    transactions = fix_withdrawal_deposit_logic(transactions)
    
    result_df = pd.DataFrame(transactions)
    
    for col in result_df.columns:
        result_df[col] = result_df[col].replace(['', 'nan', 'None'], pd.NA)
    
    return result_df

def process_transactions_icici(df):
    """Enhanced ICICI transaction processor"""
    if df.empty:
        return df
    
    print(f"Processing ICICI dataframe with shape: {df.shape}")
    
    # Find the start of transaction data
    start_idx = 0
    header_found = False
    
    for idx, row in df.iterrows():
        row_text = ' '.join([str(cell) for cell in row]).upper()
        
        # Look for transaction headers or first valid transaction
        if any(header in row_text for header in ['DATE', 'DESCRIPTION', 'AMOUNT', 'TYPE']):
            start_idx = idx + 1
            header_found = True
            print(f"Found transaction header at row {idx}")
            break
        elif is_date_icici(str(row.iloc[0]).strip()) and len(row) >= 3:
            start_idx = idx
            header_found = True
            print(f"Found first transaction at row {idx}")
            break
    
    if not header_found:
        print("No clear transaction start found, processing entire dataframe")
    
    df = df.iloc[start_idx:].reset_index(drop=True)
    
    # Find end of transaction data
    end_idx = len(df)
    for idx, row in df.iterrows():
        if is_summary_start(row):
            end_idx = idx
            print(f"Found end of transactions at row {idx}")
            break
    
    df = df.iloc[:end_idx]
    print(f"Processing rows {start_idx} to {end_idx}")
    
    transactions = []
    parsed_count = 0
    skipped_count = 0
    
    for idx, row in df.iterrows():
        if len(row) == 0:
            continue
            
        parsed = parse_transaction_row_icici(row)
        
        if parsed:
            parsed_count += 1
            transactions.append(parsed)
            if parsed_count <= 5 or parsed_count % 50 == 0:  # Progress indicator
                print(f"  Parsed transaction {parsed_count}: {parsed['Date']} - {parsed['Narration'][:50]}...")
        else:
            skipped_count += 1
            if skipped_count <= 10:  # Show first few skipped rows for debugging
                row_preview = [str(cell)[:30] for cell in row[:4] if str(cell).strip() not in ['', 'nan']]
                if row_preview:
                    print(f"  Skipped row {idx}: {row_preview}")
    
    print(f"Final results: {parsed_count} transactions parsed, {skipped_count} rows skipped")
    
    if not transactions:
        print("No transactions found! Showing first 10 rows for debugging:")
        for i in range(min(10, len(df))):
            row_data = [str(cell) for cell in df.iloc[i][:6] if str(cell).strip() not in ['', 'nan']]
            print(f"  Row {i}: {row_data}")
        return pd.DataFrame()
    
    result_df = pd.DataFrame(transactions)
    
    # Clean up empty values
    for col in result_df.columns:
        result_df[col] = result_df[col].replace(['', 'nan', 'None'], pd.NA)
    
    print(f"Created final dataframe with {len(result_df)} transactions")
    return result_df

# Usage example and main execution
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
        df = extract_df(pdf_path)
        
        if not df.empty:
            # Fix the file path issue
            base_name = os.path.basename(pdf_path).replace('.pdf', '')
            output_file = f"transactions_{base_name}.csv"
            
            df.to_csv(output_file, index=False)
            print(f"Successfully extracted {len(df)} transactions")
            print(f"Transactions saved to {output_file}")
            print("\nFirst few transactions:")
            print(df.head())
            print(df.tail())
        else:
            print("No transactions found or unsupported format")
    else:
        print("Usage: python script.py <pdf_file_path>")
        print("Supports both HDFC and ICICI bank statements with auto-detection")
