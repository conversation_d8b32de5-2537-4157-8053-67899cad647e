# Database Migrations

This project uses a versioned migration system to ensure consistent database schema across all environments (development, staging, production).

## ⚠️ CRITICAL: Never Apply Manual SQL Changes

**Always use the migration system!** Manual SQL changes will cause production deployments to fail when the code expects schema changes that don't exist.

## Migration System Overview

- **Migrations Directory**: `/migrations/`
- **Migration Files**: Numbered SQL files (e.g., `001_add_display_order_to_accounts.sql`)
- **Migration Tracking**: `migrations` table tracks applied migrations
- **Runner Script**: `scripts/migrate-simple.js` applies pending migrations

## File Naming Convention

```
{version}_{description}.sql

Examples:
000_create_migrations_table.sql
001_add_display_order_to_accounts.sql
002_add_user_preferences.sql
```

## Migration Commands

```bash
# Show what migrations would be applied (safe to run)
npm run migrate:dry-run

# Apply all pending migrations
npm run migrate

# Check migration status during development
npm run migrate:dry-run
```

## Creating a New Migration

1. **Create the migration file**:
   ```bash
   touch migrations/002_your_migration_name.sql
   ```

2. **Write the migration**:
   ```sql
   -- Migration: Your migration title
   -- Version: 002
   -- Description: What this migration does
   -- Date: YYYY-MM-DD

   -- Your SQL changes here
   ALTER TABLE your_table ADD COLUMN new_field TEXT;

   -- Always use IF NOT EXISTS for safety
   CREATE INDEX IF NOT EXISTS idx_your_index ON your_table(field);
   ```

3. **Test the migration**:
   ```bash
   npm run migrate:dry-run  # Check what would be applied
   npm run migrate          # Apply to local development
   ```

4. **Commit the migration file** with your code changes

## Deployment Process

### Development
```bash
# After pulling new code with migrations
npm install                # Runs migrate:dry-run automatically
npm run migrate           # Apply any new migrations
npm run dev               # Start development
```

### Production Deployment

**Environment Variables Required**:
- `NEXT_PUBLIC_SUPABASE_URL` or `EXPO_PUBLIC_SUPABASE_URL`
- `SUPABASE_SERVICE_ROLE_KEY` (for applying migrations)

**Deployment Steps**:
```bash
# 1. Install dependencies
npm install

# 2. Apply migrations (REQUIRED before build)
npm run migrate

# 3. Build the application
npm run build

# 4. Deploy built application
```

## Migration File Structure

### Header Comments (Required)
```sql
-- Migration: Brief title
-- Version: XXX (must match filename)
-- Description: Detailed description
-- Date: YYYY-MM-DD
```

### Safe SQL Practices
```sql
-- ✅ Good: Use IF NOT EXISTS
ALTER TABLE accounts ADD COLUMN IF NOT EXISTS display_order INTEGER DEFAULT 0;
CREATE INDEX IF NOT EXISTS idx_name ON table(column);

-- ❌ Bad: Will fail if already exists
ALTER TABLE accounts ADD COLUMN display_order INTEGER DEFAULT 0;
CREATE INDEX idx_name ON table(column);

-- ✅ Good: Handle existing data
UPDATE accounts 
SET display_order = row_number() OVER (PARTITION BY user_id ORDER BY created_at)
WHERE display_order IS NULL OR display_order = 0;

-- ✅ Good: Add comments for documentation
COMMENT ON COLUMN accounts.display_order IS 'Custom ordering within account type';
```

## Troubleshooting

### Migration Fails in Production
1. Check Supabase logs for the exact error
2. Verify the migration SQL in Supabase SQL Editor
3. Check if the migration was partially applied
4. Use the migrations table to see what was applied:
   ```sql
   SELECT * FROM migrations ORDER BY version;
   ```

### Manual Recovery (Last Resort)
If you must manually fix a failed migration:

1. **Apply the SQL manually** in Supabase SQL Editor
2. **Record the migration** so the system knows it was applied:
   ```sql
   INSERT INTO migrations (version, name, description, checksum, execution_time_ms)
   VALUES ('001', '001_your_migration', 'Manual application', 'manual', 0);
   ```

### Reset Migration State (Dangerous)
Only use in development environments:
```sql
-- This removes migration records but NOT schema changes
DELETE FROM migrations WHERE version = '001';
```

## Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Run Database Migrations
  run: npm run migrate
  env:
    NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
    SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

- name: Build Application
  run: npm run build
```

### Vercel Deployment
Add to `vercel.json`:
```json
{
  "buildCommand": "npm run migrate && npm run build",
  "env": {
    "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-role-key"
  }
}
```

## Best Practices

1. **Always test migrations locally first**
2. **Never modify existing migration files** (create a new one)
3. **Include rollback instructions** in migration comments if complex
4. **Use transactions** for multi-statement migrations when possible
5. **Add appropriate indexes** for new columns that will be queried
6. **Consider performance impact** of migrations on large tables
7. **Backup before major schema changes** in production

## Current Migrations

- `000_create_migrations_table.sql` - Creates migration tracking system
- `001_add_display_order_to_accounts.sql` - Adds drag-and-drop ordering to accounts

## Environment Setup

### Local Development
Add to `.env.local`:
```env
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### Production
Ensure these environment variables are set in your hosting platform:
- `SUPABASE_SERVICE_ROLE_KEY` (from Supabase Dashboard > Settings > API)